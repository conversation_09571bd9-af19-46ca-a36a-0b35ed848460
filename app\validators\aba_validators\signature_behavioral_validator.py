from typing import List, <PERSON><PERSON>, Dict
from .utils import map_segment
from datetime import datetime, timezone, timedelta
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "83-2983293"
GROUP_NPI = "**********"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
POS_99_GT_DATE = datetime(2025, 1, 25)  # From update on 01/27/2025
WEEKLY_BILLING_DAY = 4  # Friday (0=Monday, 4=Friday)

def validate_signature_behavioral_medicaid_pos_11_gt(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 5: Maryland Medicaid uses POS 11 with GT modifier, not POS 02."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_medicaid = any("MEDICAID" in seg.get("Payer Name", "").upper() or "MEDICAID" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_medicaid:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            modifier = parts[2] if len(parts) > 2 else ""
            cpt = parts[1]
            if pos != "11" or "GT" not in modifier:
                errors.append((
                    f"Medicaid CPT {cpt} with POS {pos} and modifier '{modifier}'",
                    "Use POS 11 with GT modifier for Maryland Medicaid per update on 01/23/2023"
                ))
    return errors

def validate_signature_behavioral_carefirst_pos_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 6: CareFirst BCBS - Separate POS 02 and 12 claims, change POS 11 to 02."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_carefirst = any("CAREFIRST" in seg.get("Payer Name", "").upper() or "CAREFIRST" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_carefirst:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    pos_list = [seg.get("Place of Service", "").strip() for seg in service_lines]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)

    # Check for mixed POS 02 and 12 on same claim
    if "02" in pos_list and "12" in pos_list:
        errors.append((
            f"CareFirst BCBS claim on DOS {dos} mixes POS 02 and POS 12",
            "Submit separate claims: one with POS 02, another with POS 12 per update on 01/23/2023"
        ))

    # Check for POS 11 and replace with 02
    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt = parts[1]
            if pos == "11":
                errors.append((
                    f"CareFirst BCBS CPT {cpt} with POS 11",
                    "Change POS 11 to POS 02 per update on 01/23/2023"
                ))
    return errors

def validate_signature_behavioral_optum_unit_limits(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 7: Optum Maryland and Carelon CPT unit/provider limits per day."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_optum = any("OPTUM" in seg.get("Payer Name", "").upper() or "OPTUM" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    is_carelon = any("CARELON" in seg.get("Payer Name", "").upper() or "CARELON" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not (is_optum or is_carelon):
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    if not dos:
        dos = CURRENT_DATE.strftime("%Y%m%d")

    # CPT limits: {CPT: (max_units, max_providers)}
    cpt_limits = {
        "97151": (32, 1),
        "97154": (16, 1),
        "97155": (24, 1),
        "97156": (16, 1),
        "97153": (32, 3)
    }

    dos_cpt_provider_map = {}
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in cpt_limits:
            cpt = parts[1]
            rendering_npi = seg.get("Rendering Provider Identifier")
            units = float(seg.get("Units/Days", "").strip())
            key = (dos, cpt)
            dos_cpt_provider_map.setdefault(key, []).append((rendering_npi, units))

    for (dos, cpt), provider_units in dos_cpt_provider_map.items():
        max_units, max_providers = cpt_limits[cpt]
        unique_providers = len(set(p for p, _ in provider_units))
        total_units_per_provider = {}
        for provider, units in provider_units:
            total_units_per_provider[provider] = total_units_per_provider.get(provider, 0) + units

        if unique_providers > max_providers:
            errors.append((
                f"{('Optum' if is_optum else 'Carelon')} CPT {cpt} on DOS {dos} billed by {unique_providers} providers; max {max_providers}",
                f"Limit CPT {cpt} to {max_providers} provider(s) per day"
            ))

        for provider, total_units in total_units_per_provider.items():
            if total_units > max_units:
                errors.append((
                    f"{('Optum' if is_optum else 'Carelon')} CPT {cpt} on DOS {dos} for NPI {provider} exceeds {max_units} units; found {total_units}",
                    f"Reduce units to {max_units} for CPT {cpt} per provider per day"
                ))
    return errors

def validate_signature_behavioral_pos_99_gt(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 8: Bill sessions on 01/25/2025 with POS 99 and GT modifier."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE

    if dos_date == POS_99_GT_DATE:
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            pos = seg.get("Place of Service", "").strip()
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2:
                modifier = parts[2] if len(parts) > 2 else ""
                cpt = parts[1]
                if pos != "99" or "GT" not in modifier:
                    errors.append((
                        f"CPT {cpt} on DOS {dos} with POS {pos} and modifier '{modifier}'",
                        "Use POS 99 with GT modifier for sessions on 01/25/2025 per update on 01/27/2025"
                    ))
    return errors

def validate_signature_behavioral_temp_auth(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 9: Do not bill claims with temporary authorizations."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Look for authorization information
    auth_info = next((seg.get("Authorization Information", "") for seg in mapped_all if "Authorization Information" in seg), "")
    if "TEMP" in auth_info.upper():
        errors.append((
            "Temporary authorization detected",
            "Do not bill claims with temporary authorizations per update on 12/15/2022"
        ))
    
    return errors

def validate_signature_behavioral_session_notes(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 10: Ensure session notes are available before billing."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # This is a placeholder - actual implementation would need to check against a session notes database
    # For now, we'll add a reminder to check manually
    errors.append((
        "Session notes verification required",
        "Verify session notes are available before billing per update on 12/15/2022"
    ))
    
    return errors

def validate_signature_behavioral_unit_accuracy(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 11: Check for unusual unit counts that might indicate errors."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt = parts[1]
            try:
                units = float(seg.get("Units/Days", "").strip())
                hours = seg.get("Hours", "")
                if hours and float(hours) > 0:
                    expected_units = float(hours) * 4  # Assuming 15-minute units (4 per hour)
                    if units > expected_units * 2:  # If units are more than double expected
                        errors.append((
                            f"Unusual unit count: {units} units for {hours} hours of service",
                            "Verify unit calculation accuracy per update on 12/23/2022"
                        ))
            except (ValueError, TypeError):
                pass  # Skip if we can't parse the values
    
    return errors

def validate_signature_behavioral_friday_billing(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 12: Ensure billing is completed by Friday morning."""
    errors = []
    today = CURRENT_DATE
    
    # If today is not Friday and we're processing claims
    if today.weekday() != WEEKLY_BILLING_DAY:
        next_friday = today + timedelta(days=(WEEKLY_BILLING_DAY - today.weekday()) % 7)
        errors.append((
            f"Billing being processed on {today.strftime('%A')}",
            f"Complete all billing by Friday morning per update on 04/07/2025. Next billing day: {next_friday.strftime('%A, %m/%d/%Y')}"
        ))
    
    return errors

def validate_signature_behavioral_optum_carelon_pos_02_to_11_gt(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """For Optum and Carelon, if POS 02, require POS 11 with GT modifier."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    # Look for payer in both Payer Name and Name Last or Organization Name, and only for NM1*PR segments
    is_optum = any(
        (seg.get("Entity Identifier Code") == "PR") and (
            "OPTUM" in seg.get("Payer Name", "").upper() or
            "OPTUM" in seg.get("Name Last or Organization Name", "").upper() or
            "OPTUM" in seg.get("Application Receiver's Code", "").upper()
        )
        for seg in mapped_all
    )
    is_carelon = any(
        (seg.get("Entity Identifier Code") == "PR") and (
            "CARELON" in seg.get("Payer Name", "").upper() or
            "CARELON" in seg.get("Name Last or Organization Name", "").upper() or
            "CARELON" in seg.get("Application Receiver's Code", "").upper()
        )
        for seg in mapped_all
    )
    if not (is_optum or is_carelon):
        return []
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        modifier = parts[2] if len(parts) > 2 else ""
        cpt = parts[1] if len(parts) > 1 else ""
        if pos == "02":
            errors.append((
                f"{'Optum' if is_optum else 'Carelon'} CPT {cpt} with POS 02",
                "Change POS to 11 and add GT modifier"
            ))
    return errors

def validate_signature_behavioral_psycho_eval_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Psycho eval claims must be billed under Deija McLean NPI ********** as rendering provider."""
    errors = []
    PSYCHO_CODES = {"90791", "96130", "96131", "96136", "96137", "90837"}
    EXPECTED_NPI = "**********"
    flagged_cpts = set()

    # Map all segments for easier access
    mapped_all = [map_segment(seg) for seg in claim.get("billing_provider", []) + claim.get("subscriber", []) + claim.get("patient", []) + claim.get("claim_segments", [])]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    for seg in service_lines:
        service_id = seg.get("Service Identification", "")
        parts = service_id.split(":")
        cpt = parts[1] if len(parts) > 1 else ""
        rendering_npi = seg.get("Rendering Provider Identifier", "")
        if cpt in PSYCHO_CODES and rendering_npi != EXPECTED_NPI and cpt not in flagged_cpts:
            errors.append((
                f"Psycho eval CPT {cpt} billed with NPI {rendering_npi}",
                f"Bill under Deija McLean NPI {EXPECTED_NPI}"
            ))
            flagged_cpts.add(cpt)
    return errors

def validate_signature_behavioral_psycho_eval_billing_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Psycho eval claims must have billing provider NPI ********** in 32a & 33b."""
    errors = []
    PSYCHO_CODES = {"90791", "96130", "96131", "96136", "96137", "90837"}
    EXPECTED_NPI = "**********"

    # Map all segments for easier access
    mapped_billing = [map_segment(seg) for seg in claim.get("billing_provider", [])]
    mapped_all = [map_segment(seg) for seg in claim.get("billing_provider", []) +
                  claim.get("subscriber", []) +
                  claim.get("patient", []) +
                  claim.get("claim_segments", [])]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    # Only check if there is a psycho eval CPT in the claim
    psycho_eval_present = any(
        (seg.get("Service Identification", "").split(":")[1] if len(seg.get("Service Identification", "").split(":")) > 1 else "") in PSYCHO_CODES
        for seg in service_lines
    )

    if psycho_eval_present:
        # Only add one error per claim if any billing provider NPI is incorrect
        npi_incorrect = any(seg.get("Identification Code", "") != EXPECTED_NPI for seg in mapped_billing)
        if npi_incorrect:
            errors.append((
                f"Psycho eval claim not billed under NPI {EXPECTED_NPI}",
                f"Bill under Deija McLean NPI {EXPECTED_NPI} in 32a & 33b"
            ))
    return errors


def validate_signature_behavioral_same_patient_dos_cpt_diff_pos(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """If same patient, DOS, CPT, but POS 02 & 12, require POS 99 with GT modifier."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    cpt_dos_pos = {}
    for seg in service_lines:
        cpt = seg["Service Identification"].split(":")[1] if "Service Identification" in seg else ""
        dos = seg.get("Date Time Period", "")
        pos = seg.get("Place of Service", "").strip()
        key = (cpt, dos)
        cpt_dos_pos.setdefault(key, set()).add(pos)
    for (cpt, dos), poses in cpt_dos_pos.items():
        if "02" in poses and "12" in poses:
            errors.append((
                f"Same patient/DOS/CPT {cpt} has POS 02 & 12",
                "Change to POS 99 with GT modifier"
            ))
    return errors

def validate_signature_behavioral_optum_md_to_carelon(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """If Optum MD Medicaid and DOS >= 2024-12-22, flag to submit under Carelon."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_optum_md = any("OPTUM MD MEDICAID" in seg.get("Payer Name", "").upper() for seg in mapped_all)
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg), None)
    if is_optum_md and dos and dos >= "20241222":
        errors.append((
            f"Optum MD Medicaid claim on DOS {dos}",
            "Submit under Carelon Behavioral Health"
        ))
    return errors


def validate_signature_behavioral_psycho_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Psycho codes: no modifier except 95 for telehealth."""
    errors = []
    PSYCHO_CODES = {"90791", "96130", "96131", "96136", "96137", "90837"}
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        cpt = seg["Service Identification"].split(":")[1] if "Service Identification" in seg else ""
        modifier = seg["Service Identification"].split(":")[2] if len(seg["Service Identification"].split(":")) > 2 else ""
        pos = seg.get("Place of Service", "")
        if cpt in PSYCHO_CODES:
            if modifier != "95":
                errors.append((
                    f"Psycho code {cpt} with telehealth POS {modifier} missing 95 modifier",
                    "Add 95 modifier for telehealth"
                ))
    return errors

def validate_signature_behavioral_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Signature Behavioral Health-specific validation rules."""
    return (
        validate_signature_behavioral_medicaid_pos_11_gt(claim) +
        validate_signature_behavioral_carefirst_pos_rules(claim) +
        validate_signature_behavioral_optum_unit_limits(claim) +
        validate_signature_behavioral_pos_99_gt(claim) +
        validate_signature_behavioral_temp_auth(claim) +
        validate_signature_behavioral_optum_carelon_pos_02_to_11_gt(claim) +
        validate_signature_behavioral_same_patient_dos_cpt_diff_pos(claim) +
        validate_signature_behavioral_optum_md_to_carelon(claim) +
        validate_signature_behavioral_psycho_modifiers(claim) +
        validate_signature_behavioral_unit_accuracy(claim) +
        validate_signature_behavioral_psycho_eval_billing_npi(claim) +
        validate_signature_behavioral_psycho_eval_provider(claim)
        # validate_signature_behavioral_friday_billing(claim)
    )