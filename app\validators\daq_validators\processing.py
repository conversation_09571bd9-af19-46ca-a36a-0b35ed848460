# app/validators/daq_validators/processing.py
import re
import csv
import logging
from datetime import datetime
import io
import pandas as pd
from fastapi import UploadFile, HTTPException
from fastapi.responses import JSONResponse

# Import rule modules (assuming they exist in the same package)
from . import rules_pcm
from . import rules_raso
from . import rules_well
from . import rules_everyday

logger = logging.getLogger(__name__)

# ----------------------------
# Cleaning Functions
# ----------------------------
def clean_csv(content: str) -> list:
    # only_commas = re.compile(r'^\s*,+\s*$')
    # printed_line = re.compile(r'Printed:')
    # billing_log = re.compile(r'Billing Log')
    # revision = re.compile(r'\brev\.')
    drop_patterns = [
        re.compile(r'^\s*,+\s*$'),                         # blank/comma-only
        re.compile(r'Printed:'),                           # "Printed:4-30-25 …"
        re.compile(r'\brev\.'),                            # "rev. 3/10/2020"
        re.compile(r'Billing Log'),                        # footers
        re.compile(r'Sort by'),                            # footers
        re.compile(r'Date Range'),                         # footers
        re.compile(r'All Locations?'),                     # footers
        re.compile(r'All Service Locations'),              # drop “All Service Locations”
        re.compile(r'Page\s+\d+'),                         # “Page 2”
        re.compile(r'Totals for'),                         # “Totals for primary…”
        re.compile(r'\*+ Rebill'),                         # "* Rebill…"
        # NEW: drop any row that's just a MM-DD-YYYY stamp
        re.compile(r'^[0-9]{1,2}[-/][0-9]{1,2}[-/][0-9]{2,4},'),
        # NEW: drop any row that's just an ISO datetime stamp
        re.compile(r'^[0-9]{4}-[0-9]{2}-[0-9]{2}\s+[0-9]{2}:[0-9]{2}:[0-9]{2},'),
    ]

    header_start = "Encounter ID"

    header_seen = False
    clean_lines = []
    lines = content.splitlines()
    # print(f"DEBUG clean_csv - Total lines: {len(lines)}")
    i = 0
    # while i < len(lines):
    #     line = lines[i].rstrip('\n')
    #     # print(f"DEBUG clean_csv - Line {i}: '{line}'")
    #     if only_commas.match(line) or printed_line.search(line) or billing_log.search(line) or revision.search(line):
    #         i += 1
    #         continue
    while i < len(lines):
        line = lines[i].rstrip('\n')
        # drop any line matching any drop_pattern
        if any(p.search(line) for p in drop_patterns):
            i += 1
            continue
        if header_start in line:
            if not header_seen:
                header_seen = True
                clean_lines.append(line)
                if i + 1 < len(lines):
                    clean_lines.append(lines[i+1].rstrip('\n'))
                if i + 2 < len(lines):
                    clean_lines.append(lines[i+2].rstrip('\n'))
                i += 3
                continue
            else:
                i += 3
                continue
        clean_lines.append(line)
        i += 1
    return clean_lines

# ----------------------------
# Date Helpers
# ----------------------------
# def parse_date(date_str):
#     try:
#         return datetime.strptime(date_str, '%m-%d-%y')
#     except ValueError:
#         return None

def parse_date(date_str):
    """
    Try:
      - MM-DD-YY
      - MM-DD-YYYY
      - ISO datetime (YYYY-MM-DD HH:MM:SS)
      - Plain YYYY-MM-DD
    """
    if not date_str or not date_str.strip():
        return None
    for fmt in ("%m-%d-%y", "%m-%d-%Y", "%Y-%m-%d %H:%M:%S", "%Y-%m-%d"):
        try:
            return datetime.strptime(date_str.strip(), fmt)
        except ValueError:
            continue
    return None

def is_date_close(service_date, claim_date, days_threshold=7):
    s_date = parse_date(service_date)
    c_date = parse_date(claim_date)
    if not s_date or not c_date:
        return True
    return abs((s_date - c_date).days) <= days_threshold

# ----------------------------
# Encounter Data Parsing
# ----------------------------
def parse_encounter_data(sample_group):
    rows = []
    encounters = {}
    pending_procedures = []
    columns = [
        'Service Date', 'Proc', 'Mod', 'Units', 'Description', 'Dx Pointers', 'Charge',
        'Encounter ID', 'Patient ID', 'Patient Name', 'Dx', 'Insurance', 'Charges Total',
        'Status', 'Claim Date', 'Provider', 'POS'
    ]
    for i, line in enumerate(sample_group):
        reader = csv.reader([line])
        try:
            fields = next(reader)
            if len(fields) < 17:
                logger.debug(f"Skipping line {i} due to insufficient fields: {len(fields)}")
                continue
            if fields[0].strip().isdigit():
                encounter_id = fields[0].strip()
                encounters[encounter_id] = {
                    'Encounter ID': encounter_id,
                    'Patient ID': fields[3].strip(),
                    'Patient Name': fields[4].strip('" ').strip(),
                    'Dx': fields[7].strip('" ').strip(),
                    'Insurance': fields[11].strip(),
                    'Charges Total': fields[16].strip(),
                    'POS': fields[10].strip() if len(fields) > 10 else '11'
                }
                # Look ahead for claim info
                for j in range(i + 1, len(sample_group)):
                    next_reader = csv.reader([sample_group[j]])
                    claim_fields = next(next_reader)
                    if len(claim_fields) >= 5 and claim_fields[0].strip() and not claim_fields[0].strip().isdigit():
                        encounters[encounter_id].update({
                            'Status': claim_fields[0].strip(),
                            'Claim Date': claim_fields[3].strip(),
                            'Provider': claim_fields[4].strip('" ').strip()
                        })
                        break
                # Attach pending procedures if dates are close
                for proc in pending_procedures[:]:
                    if is_date_close(proc['Service Date'], encounters[encounter_id]['Claim Date']):
                        row = {**encounters[encounter_id], **proc}
                        rows.append(row)
                        pending_procedures.remove(proc)
            elif fields[0] == '' and fields[1] == '' and '-' in fields[2]:
                procedure = {
                    'Service Date': fields[2].strip(),
                    'Proc': fields[3].strip(),
                    'Mod': fields[4].strip(),
                    'Units': fields[6].strip(),
                    'Description': fields[7].strip('" ').strip(),
                    'Dx Pointers': fields[11].strip(),
                    'Charge': fields[14].strip()
                }
                if encounters:
                    last_encounter = list(encounters.values())[-1]
                    if is_date_close(procedure['Service Date'], last_encounter['Claim Date']):
                        row = {**last_encounter, **procedure}
                        rows.append(row)
                    else:
                        pending_procedures.append(procedure)
                else:
                    pending_procedures.append(procedure)
        except Exception as e:
            logger.error(f"Error parsing line {i}: {str(e)}")
    if not rows:
        return pd.DataFrame(columns=columns)
    df = pd.DataFrame(rows)
    for col in columns:
        if col not in df.columns:
            df[col] = pd.NA
    return df[columns]

def process_all_groups(groups_dict):
    all_dfs = []
    for group_data in groups_dict.values():
        df = parse_encounter_data(group_data)
        if not df.empty:
            all_dfs.append(df)
    if all_dfs:
        return pd.concat(all_dfs, ignore_index=True)
    return pd.DataFrame(columns=[
        'Service Date', 'Proc', 'Mod', 'Units', 'Description', 'Dx Pointers', 'Charge',
        'Encounter ID', 'Patient ID', 'Patient Name', 'Dx', 'Insurance', 'Charges Total',
        'Status', 'Claim Date', 'Provider', 'POS'
    ])

# ----------------------------
# Practice Detection
# ----------------------------
def detect_practice(first_cell_text: str) -> str:
    """
    Detect the practice type based on the text in the first cell.
      - If it contains "pa lisa" or "pm/lisa" then assume "PCM"
      - If it contains "raso" then assume "Raso"
      - If it contains "well" then assume "Well"
      - Otherwise default to "Everyday"
    """
    cell = first_cell_text.strip().lower()
    print(f"First cell content: '{first_cell_text}'")
    if "pa lisa" in cell or "pm/lisa" in cell:
        practice = "PCM"
    elif "raso" in cell:
        practice = "Raso"
    elif "well" in cell:
        practice = "Well"
    else:
        practice = "Everyday"
    print(f"Detected practice type: '{practice}'")
    return practice

# ----------------------------
# Error Collection (Dynamic Rule Loading)
# ----------------------------
def collect_errors(df: pd.DataFrame, practice_type: str) -> list:
    errors = []
    # print(f"DEBUG collect_errors - Practice: {practice_type}, Rows: {len(df)}")
    
    if practice_type == "PCM":
        rules_row = [rules_pcm.rule2, rules_pcm.rule5, rules_pcm.rule6, rules_pcm.rule7]
        rules_group = [rules_pcm.rule3]
    elif practice_type == "Raso":
        rules_row = [rules_raso.rule1, rules_raso.rule2, rules_raso.rule3]
        rules_group = []
    elif practice_type == "Well":
        rules_row = [
            rules_well.rule1, rules_well.rule2, rules_well.rule3, rules_well.rule4,
            rules_well.rule5, rules_well.rule7, rules_well.rule13
        ]
        rules_group = []
    else:  # Everyday
        rules_row = [
            rules_everyday.rule3, rules_everyday.rule6,
            rules_everyday.rule7, rules_everyday.rule9,
            rules_everyday.rule16, rules_everyday.rule17
        ]
        rules_group = [rules_everyday.rule5, rules_everyday.rule8, rules_everyday.rule18]
    
    def row_errors(row):
        for rule in rules_row:
            result = rule(row)
            if result:
                errors.append({
                    "encounter_id": row['Encounter ID'],
                    "error": result["error"],
                    "correction": result["correction"]
                })
    
    try:
        df.apply(row_errors, axis=1)
    except Exception as e:
        logger.error(f"Error applying row rules: {str(e)}")
    
    for encounter, group in df.groupby("Encounter ID"):
        for rule in rules_group:
            try:
                result = rule(group)
                if result:
                    errors.append({
                        "encounter_id": encounter,
                        "error": result["error"],
                        "correction": result["correction"]
                    })
            except Exception as e:
                logger.error(f"Error in group rule for encounter {encounter}: {str(e)}")
    return errors

# ----------------------------
# Expose process_file for external calls
# ----------------------------
async def process_file(file: UploadFile) -> dict:
    try:
        logger.info(f"Processing file: {file.filename}")
        content = await file.read()
        filename = file.filename.lower()

        if filename.endswith('.csv'):
            content_str = content.decode('utf-8')
            lines = content_str.splitlines()
            first_non_empty_line = next((line for line in lines if line.strip()), "")
            practice_type = detect_practice(first_non_empty_line)
        elif filename.endswith(('.xlsx', '.xls')):
            df_raw = pd.read_excel(io.BytesIO(content), engine="openpyxl", header=None)
            # print(f"DEBUG process_file - Raw Excel:\n{df_raw.to_string()}")
            first_cell = str(df_raw.iloc[0, 0])
            practice_type = detect_practice(first_cell)
            content_str = df_raw.to_csv(index=False)
            # print(f"DEBUG process_file - CSV string:\n{content_str[:500]}...")

        cleaned_lines = clean_csv(content_str)
        # print(f"DEBUG process_file - Cleaned lines (count: {len(cleaned_lines)}):\n{cleaned_lines}")
        groups = {}
        current_group = None
        group_counter = 1
        for line in cleaned_lines:
            if not line.strip():
                continue
            if "Encounter ID" in line and not any(c.isdigit() for c in line.split(',')[0].strip()):
                if current_group and groups.get(current_group):
                    group_counter += 1
                current_group = f"group_{group_counter}"
                groups[current_group] = [line]
            elif current_group:
                groups[current_group].append(line)
        # print(f"DEBUG process_file - Groups: {groups}")

        combined_df = process_all_groups(groups)
        # print(f"DEBUG process_file - DataFrame rows: {len(combined_df)}")
        # print(f"DEBUG process_file - DataFrame sample:\n{combined_df.head().to_string()}")
        if combined_df.empty:
            return {"results": [], "message": "No valid encounter data found."}

        errors = collect_errors(combined_df, practice_type)
        # Optionally, increment your file validation counter here if needed.
        return {"results": errors, "message": f"Processing complete. Found {len(errors)} issues."}
    except Exception as e:
        logger.error(f"Error processing file {file.filename}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")
