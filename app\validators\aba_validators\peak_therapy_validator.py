from typing import List, <PERSON><PERSON>, Dict
from .utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "82-3853381"
GROUP_NPI = "**********"
PROVIDER_NPIS = {
    "Jackeline Jas": "**********",
    "Saira Khan": "**********",
    "Tancy Iturrizaga": "**********",
    "<PERSON>": "**********",
    "Dai<PERSON> Reyes": "**********"
}
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
XS_MODIFIER_START_DATE = datetime(2023, 8, 8)  # From update on 08/08/2023

def validate_peak_therapy_modifier_requirements(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 1-5: Modifier requirements for <PERSON><PERSON><PERSON> (yes), <PERSON><PERSON><PERSON> (no), BCBS FL (no), Tricare (no), <PERSON><PERSON> (yes)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    requires_modifier = False
    payer_desc = ""
    if "AETNA" in payer_name or "AETNA" in payer_code:
        requires_modifier = True
        payer_desc = "Aetna"
    elif "OPTUM" in payer_name or "OPTUM" in payer_code:
        requires_modifier = True
        payer_desc = "United Optum"
    elif "CIGNA" in payer_name or "CIGNA" in payer_code:
        requires_modifier = False
        payer_desc = "Cigna"
    elif "BCBS" in payer_name or "BCBS" in payer_code or "FLORIDA BLUE" in payer_name:
        requires_modifier = False
        payer_desc = "Blue Cross Blue Shield Florida"
    elif "TRICARE" in payer_name or "TRICARE" in payer_code:
        requires_modifier = False
        payer_desc = "Tricare"
    else:
        return []  # No specific rules for other payers

    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            modifier = parts[2] if len(parts) > 2 else ""
            cpt = parts[1]
            if requires_modifier and not modifier:
                errors.append((
                    f"{payer_desc} CPT {cpt} missing required modifier",
                    f"Add modifier (e.g., HM, HN, HO) for {payer_desc} claims"
                ))
            elif not requires_modifier and modifier:
                errors.append((
                    f"{payer_desc} CPT {cpt} has unnecessary modifier '{modifier}'",
                    f"Remove modifier for {payer_desc} claims"
                ))
    return errors

def validate_peak_therapy_same_dos_cpt_different_pos(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 11: Same DOS & CPT with different POS requires XS modifier post-08/08/2023."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE

    if dos_date >= XS_MODIFIER_START_DATE:
        dos_cpt_pos_map = {}
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2:
                cpt = parts[1]
                modifier = parts[2] if len(parts) > 2 else ""
                pos = seg.get("Place of Service", "Unknown")
                key = (dos, cpt)
                dos_cpt_pos_map.setdefault(key, []).append((pos, modifier))

        for (dos, cpt), pos_modifiers in dos_cpt_pos_map.items():
            unique_pos = set(pos for pos, _ in pos_modifiers)
            if len(unique_pos) > 1:  # Different POS for same DOS and CPT
                xs_count = sum(1 for _, mod in pos_modifiers if "XS" in mod)
                if xs_count != 1:
                    errors.append((
                        f"Same DOS {dos} and CPT {cpt} with different POS ({', '.join(unique_pos)}) missing XS modifier",
                        "Add XS modifier to one line for same DOS and CPT with different POS post-08/08/2023"
                    ))
    return errors

def validate_peak_therapy_pos_99_to_03(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 14: Barrington Wong POS 99 changed to POS 03."""
    errors = []
    mapped_patient = [map_segment(seg) for seg in claim["patient"]]
    patient_name = next((seg.get("Name Last or Organization Name", "").upper() + "_" + seg.get("Name First", "").upper() for seg in mapped_patient if "Name Last or Organization Name" in seg), "")
    
    if "BARRINGTON_WONG" in patient_name:
        service_lines = [map_segment(seg) for seg in claim["claim_segments"] if "Service Identification" in seg]
        for seg in service_lines:
            pos = seg.get("Place of Service", "").strip()
            if pos == "99":
                errors.append((
                    f"POS 99 for Barrington Wong on CPT {seg['Service Identification'].split(':')[1]}",
                    "Change POS 99 to POS 03 per update on 05/22/2024 and 01/17/2024"
                ))
    return errors

def validate_peak_therapy_medicaid_97153_97155_overlap(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 16: Medicaid 97153 split with XP modifier if timing overlaps with 97155."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_medicaid = any("MEDICAID" in seg.get("Payer Name", "").upper() or "MEDICAID" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_medicaid:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    time_ranges = {}
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in {"97153", "97155"}:
            dtp = next((s for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), None)
            if dtp and dtp.get("Date Time Period Format Qualifier") == "RD8":
                start_date, end_date = dtp["Date Time Period"].split("-")
                try:
                    start = int(start_date[-4:])  # HHMM format
                    end = int(end_date[-4:])
                    time_ranges[(parts[1], start_date[:8])] = (start, end, parts[2] if len(parts) > 2 else "")
                except ValueError:
                    errors.append((f"Invalid time format in DTP: {dtp['Date Time Period']}", "Use YYYYMMDDHHMM format"))

    for (cpt1, date1), (start1, end1, mod1) in time_ranges.items():
        for (cpt2, date2), (start2, end2, mod2) in time_ranges.items():
            if date1 == date2 and cpt1 == "97153" and cpt2 == "97155" and not (end1 <= start2 or end2 <= start1):
                if "XP" not in mod1:
                    errors.append((
                        f"Medicaid 97153 ({start1}-{end1}) overlaps with 97155 ({start2}-{end2}) on {date1} missing XP modifier",
                        "Add XP modifier to 97153 for overlapping sessions with 97155"
                    ))
    return errors

def validate_peak_therapy_aiden_boulos_97153_units(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 17: Aiden Boulos CPT 97153 capped at 32 units if above 32."""
    errors = []
    mapped_patient = [map_segment(seg) for seg in claim["patient"]]
    patient_name = next((seg.get("Name Last or Organization Name", "").upper() + "_" + seg.get("Name First", "").upper() for seg in mapped_patient if "Name Last or Organization Name" in seg), "")
    
    if "AIDEN_BOULOS" in patient_name:
        mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] == "97153":
                try:
                    units = float(seg.get("Units/Days", "").strip())
                    if units > 32:
                        errors.append((
                            f"Aiden Boulos CPT 97153 exceeds 32 units; found {units}",
                            "Change 97153 to 32 units per practice rule"
                        ))
                except ValueError:
                    errors.append((f"Invalid Units/Days '{seg.get('Units/Days', '')}' for 97153", "Ensure Units/Days is a valid number"))
    return errors

def validate_peak_therapy_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Peak Therapy Center-specific validation rules."""
    return (
        validate_peak_therapy_modifier_requirements(claim) +
        validate_peak_therapy_same_dos_cpt_different_pos(claim) +
        validate_peak_therapy_pos_99_to_03(claim) +
        validate_peak_therapy_medicaid_97153_97155_overlap(claim) +
        validate_peak_therapy_aiden_boulos_97153_units(claim)
    )