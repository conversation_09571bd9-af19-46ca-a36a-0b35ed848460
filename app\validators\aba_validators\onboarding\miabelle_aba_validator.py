from typing import List, Tuple, Dict
from ..utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"  # MiaBelle ABA INC
PROVIDER_NPI = "**********"  # DORINA KAPLAN
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)

def validate_miabelle_insurance_npi_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: BCBS of FL, Aetna, and Cigna claims must use DORINA KAPLAN NPI ********** in billing and service lines, based on payer name only."""
    errors = []
   
    TARGET_PAYERS = [
        "BCBS OF FL",
        "AETNA",
        "CIGNA"
    ]

    # Map all segments
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]

    # Find payer segment
    payer_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"]
    if not payer_segments:
        return errors

    payer_seg = payer_segments[0]
    payer_name = payer_seg.get("Name Last or Organization Name", "").strip().upper()

    if payer_name not in TARGET_PAYERS:
        return errors

    matched_payer = payer_name.title()

    # Check billing provider NPI (box 33)
    billing_npi = next((seg.get("Identification Code") for seg in mapped_all 
                        if seg.get("Entity Identifier Code") == "85"), None)
    if billing_npi != PROVIDER_NPI:
        errors.append((
            f"{matched_payer} claim uses Billing NPI {billing_npi} instead of expected NPI {PROVIDER_NPI}",
            f"Use NPI {PROVIDER_NPI} in (box 33) for {matched_payer} claims"
        ))

    # Check service line rendering provider NPIs (box 24J)
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        print("Seg",seg)
        rendering_npi = seg.get("Rendering Provider Identifier")
        if rendering_npi and rendering_npi != PROVIDER_NPI:
            errors.append((
                f"{matched_payer} claim uses Rendering NPI {rendering_npi} instead of expected NPI {PROVIDER_NPI}",
                f"Use NPI {PROVIDER_NPI} in (box 24J) for {matched_payer} claims"
            ))

    return errors




def validate_miabelle_no_modifiers_required(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: No modifiers required for BCBS, Aetna, or Cigna claims per MiaBelle specifications."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Get payer information using both name and identifier
    payer_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"]
    payer_name = ""
    payer_id = ""
    
    if payer_segments:
        payer_seg = payer_segments[0]
        payer_name = payer_seg.get("Name Last or Organization Name", "").upper()
        payer_id = payer_seg.get("Identification Code", "")
    
    # Check if it's BCBS, Aetna, or Cigna using both name and payer identifier
    is_bcbs = any(keyword in payer_name for keyword in ["BCBS", "BLUE CROSS", "BLUE SHIELD"]) or payer_id == "590"
    is_aetna = "AETNA" in payer_name or payer_id == "60054"
    is_cigna = "CIGNA" in payer_name or payer_id == "62308"
    
    is_bcbs_aetna_cigna = is_bcbs or is_aetna or is_cigna
    
    if is_bcbs_aetna_cigna:
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) > 2 and parts[2].strip():
                if is_bcbs:
                    payer_type = "BCBS"
                elif is_aetna:
                    payer_type = "Aetna"
                elif is_cigna:
                    payer_type = "Cigna"
                else:
                    payer_type = payer_name
                errors.append((
                    f"CPT {parts[1]} has modifier '{parts[2]}' for {payer_type}",
                    f"No modifiers required for {payer_type} per MiaBelle specifications"
                ))
    return errors

def validate_miabelle_supported_insurance_only(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: Only BCBS and Aetna insurance are supported by MiaBelle ABA INC."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Get payer information
    payer_name = next((seg.get("Name Last or Organization Name", "").upper() 
                       for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() 
                       for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    # Check if it's supported insurance
    is_bcbs = any(keyword in payer_name for keyword in ["BCBS", "BLUE CROSS", "BLUE SHIELD"])
    is_aetna = "AETNA" in payer_name
    
    if not (is_bcbs or is_aetna) and payer_name:
        errors.append((
            f"Unsupported insurance: {payer_name}",
            "MiaBelle ABA INC only accepts BCBS and Aetna insurance"
        ))
    
    return errors


def validate_miabelle_billing_address_check(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 6: Verify billing address matches practice specifications."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Expected address components
    expected_address = "121 SOUTH ORANGE AVENUE"
    expected_suite = "STE 1500"
    expected_city = "ORLANDO"
    expected_state = "FL"
    expected_zip = "32801"
    
    # Check billing provider address
    address_line = next((seg.get("Address Information", "").upper() 
                        for seg in mapped_all if "Address Information" in seg), "")
    city = next((seg.get("City Name", "").upper() 
                for seg in mapped_all if "City Name" in seg), "")
    state = next((seg.get("State or Province Code", "").upper() 
                 for seg in mapped_all if "State or Province Code" in seg), "")
    postal_code = next((seg.get("Postal Code", "") 
                       for seg in mapped_all if "Postal Code" in seg), "")
    
    if address_line and expected_address not in address_line:
        errors.append((
            f"Billing address mismatch: found '{address_line}'",
            f"Use correct address: {expected_address} {expected_suite}"
        ))
    
    if city and city != expected_city:
        errors.append((
            f"Billing city mismatch: found '{city}', expected '{expected_city}'",
            f"Use correct city: {expected_city}"
        ))
    
    if state and state not in ["FL", "FLORIDA"]:
        errors.append((
            f"Billing state mismatch: found '{state}', expected 'FL'",
            "Use correct state: FL"
        ))
    
    if postal_code and not postal_code.startswith(expected_zip):
        errors.append((
            f"Billing ZIP mismatch: found '{postal_code}', expected '{expected_zip}*'",
            f"Use correct ZIP: {expected_zip} or {expected_zip}-3241"
        ))
    
    return errors

def validate_miabelle_clearinghouse_compatibility(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 7: Ensure claim format is compatible with MyAbility clearinghouse."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Check for required segments that MyAbility typically requires
    has_billing_provider = any(seg.get("Entity Identifier Code") == "85" for seg in mapped_all)
    has_subscriber = any("subscriber" in str(seg).lower() for seg in claim.get("subscriber", []))
    has_patient = any("patient" in str(seg).lower() for seg in claim.get("patient", []))
    has_service_lines = any("Service Identification" in seg for seg in mapped_all)
    
    if not has_billing_provider:
        errors.append((
            "Missing billing provider information (NM1*85)",
            "Add billing provider segment for MyAbility clearinghouse compatibility"
        ))
    
    if not has_subscriber:
        errors.append((
            "Missing subscriber information",
            "Add subscriber information for MyAbility clearinghouse compatibility"
        ))
    
    if not has_service_lines:
        errors.append((
            "Missing service line information",
            "Add service line details for MyAbility clearinghouse compatibility"
        ))
    
    return errors

def validate_miabelle_bcbs_specific_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """BCBS-specific validation rules for MiaBelle ABA INC."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Check if this is a BCBS claim
    is_bcbs = any(seg.get("Name Last or Organization Name", "").upper() in ["BCBS", "BLUE CROSS BLUE SHIELD", "BLUE CROSS", "BLUE SHIELD"] 
                  for seg in mapped_all if seg.get("Entity Identifier Code") == "PR")
    
    if not is_bcbs:
        return []
    
    # Add BCBS-specific validation rules here
    # Placeholder for additional BCBS rules to be added based on your update
    
    return errors




def validate_bbs_tax_id_change(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: Ensure the TAX ID in the 837 files is set to *********. Raise error for any other value."""
    errors = []
    
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    # print(f"Total claim segments mapped: {mapped_all}")
    tax_id_ref = next(
        (seg for seg in mapped_all if seg.get("Reference Identification Qualifier") == "EI"), 
        None
    )
    # print(f"Found REF*EI segment: {tax_id_ref}")
    if tax_id_ref:
        current_tax_id = tax_id_ref.get("Reference Identification")
        # print(f"Current TAX ID found: {current_tax_id}")
        if current_tax_id != "*********":
            errors.append((
                f"Invalid TAX ID {current_tax_id} found in REF*EI segment.",
                "The TAX ID should be ********* in REF*EI segment"
            ))
    
    return errors

def validate_sebastian_vega_cortez_exception(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: Claims for Sebastian Vega-Cortez should use group NPI and tax ID, but rendering provider must be Dorina Kaplan."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
 
    patient_name = next((seg.get("Name Last or Organization Name", "").upper() 
                         for seg in mapped_all if seg.get("Entity Identifier Code") == "QC"), "")
    print("patient_name",patient_name,"&"*50)  # Debug print statement
    if "SEBASTIAN VEGA-CORTEZ" in patient_name:
        print("Patient matched:", patient_name, "*" * 50)  # Debug print statement

        # Billing provider NPI must be GROUP_NPI
        billing_npi = next((seg.get("Identification Code") for seg in mapped_all    
                           if seg.get("Entity Identifier Code") == "85"), None)
        print("billing_npi",billing_npi,'*'*50)
        if billing_npi != GROUP_NPI:
            errors.append((
                f"Sebastian Vega-Cortez claim uses Billing NPI {billing_npi}, expected Group NPI {GROUP_NPI}",
                f"Use Group NPI {GROUP_NPI} (MiaBelle ABA INC) in NM1*85 for this patient"
            ))

        # Tax ID must be MiaBelle's
        tax_id_ref = next((seg for seg in mapped_all if seg.get("Reference Identification Qualifier") == "EI"), None)
        print("tax_id_ref",tax_id_ref,"%"*50)
        if tax_id_ref and tax_id_ref.get("Reference Identification") != TAX_ID:
            errors.append((
                f"Sebastian Vega-Cortez claim has Tax ID {tax_id_ref.get('Reference Identification')}, expected {TAX_ID}",
                f"Use Tax ID {TAX_ID} (MiaBelle ABA INC) in REF*EI for this patient"
            ))

        # Rendering provider must still be Dorina Kaplan
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        print("service_lines",service_lines,"*"*50)
        for seg in service_lines:
            rendering_npi = seg.get("Rendering Provider Identifier")
            print("rendering_npi",rendering_npi,"&"*50)
            if rendering_npi and rendering_npi != PROVIDER_NPI:
                errors.append((
                    f"Sebastian Vega-Cortez claim has rendering NPI {rendering_npi}, expected {PROVIDER_NPI}",
                    f"Use rendering provider NPI {PROVIDER_NPI} (Dorina Kaplan) in service lines"
                ))

    return errors





def validate_miabelle_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all MiaBelle ABA INC-specific validation rules."""
    return (
        validate_miabelle_insurance_npi_rules(claim) +
        validate_miabelle_no_modifiers_required(claim) +
        validate_miabelle_supported_insurance_only(claim) +
        validate_bbs_tax_id_change(claim) +
        validate_miabelle_billing_address_check(claim) +
        validate_miabelle_clearinghouse_compatibility(claim) +
        validate_miabelle_bcbs_specific_rules(claim) +
        validate_sebastian_vega_cortez_exception(claim)
    )