import pandas as pd

def rule1(row: pd.Series) -> dict:
    # Rule 1: U0003 should be billed with CPT 99000
    if row['Proc'] == "U0003":
        if "99000" not in str(row['Description']):
            return {
                "error": "Missing CPT 99000 for U0003",
                "correction": "Add CPT 99000"
            }
    return {}

def rule2(row: pd.Series) -> dict:
    # Rule 2: U0003 should have CS modifier
    if row['Proc'] == "U0003":
        if "cs" not in str(row['Mod']).lower():
            return {
                "error": "Missing CS modifier for U0003",
                "correction": "Add modifier CS"
            }
    return {}

def rule3(row: pd.Series) -> dict:
    # Rule 3: 2020U should be replaced with CPT 0202U
    if row['Proc'] == "2020u":
        return {
            "error": "Replace CPT 2020U with CPT 0202U",
            "correction": "Use CPT 0202U instead"
        }
    return {}

def rule4(group: pd.DataFrame) -> dict:
    # Rule 4 (group-level): For J codes with rules_everyday.py description,
    # the encounter must include CPT 96372.
    j_codes = {"J1720", "J2765", "J3420", "J1885", "J2405", "J0696", "J1200", "J3411", "J1040", "J1020"}
    has_j_injection = group.apply(lambda r: r['Proc'] in j_codes and "injection" in r['Description'].lower(), axis=1).any()
    has_96372 = (group['Proc'] == "96372").any()
    if has_j_injection and not has_96372:
        return {
            "error": "Missing accompanying CPT 96372 for Injection",
            "correction": "Add CPT 96372"
        }
    return {}

def rule5(group: pd.DataFrame) -> dict:
    has_u_codes = group['Proc'].isin(["U0003", "U0004"]).any()
    has_87811 = (group['Proc'] == "87811").any()
    print(f"DEBUG rule5 - Encounter {group['Encounter ID'].iloc[0]}: has_u_codes={has_u_codes}, has_87811={has_87811}")
    if has_u_codes and not has_87811:
        encounter_id = group['Encounter ID'].iloc[0]
        return {
            "encounter_id": encounter_id,
            "error": "Replace with CPT 87811",
            "correction": "Replace U0003/U0004 with CPT 87811"
        }
    return {}

def rule6(row: pd.Series) -> dict:
    # Rule 6: S9083, S0028, or Q0144 must be non-billable for NJ Medicare
    if row['Proc'] in {"S9083", "S0028", "Q0144"} and "nj medicare" in row['Insurance'].lower():
        return {
            "encounter_id": row['Encounter ID'],
            "error": "Non Billable (S9083,S0028, Q0144): Code not allowed for NJ Medicare",
            "correction": "Remove these codes"
        }
    return {}

def rule7(row: pd.Series) -> dict:
    # Rule 7: S9083 must be non-billable for Medicaid or United Healthcare
    if row['Proc'] == "S9083" and row['Insurance'].strip().lower() in {"medicaid", "united healthcare"}:
        return {
            "error": "Non Billable: S9083 for Medicaid/United Healthcare",
            "correction": "Remove this code"
        }
    return {}

def rule8(group: pd.DataFrame) -> dict:
    has_90715 = (group['Proc'] == "90715").any()
    has_90471 = (group['Proc'] == "90471").any()
    print(f"DEBUG rule8 - Encounter {group['Encounter ID'].iloc[0]}: has_90715={has_90715}, has_90471={has_90471}")
    if has_90715 and not has_90471:
        encounter_id = group['Encounter ID'].iloc[0]
        return {
            "encounter_id": encounter_id,
            "error": "Missing Admin code 90471 for CPT 90715",
            "correction": "Add Admin code 90471"
        }
    return {}

def rule9(row: pd.Series) -> dict:
    # Rule 9: 0202U should be billed for DX Z20.822
    if row['Proc'] == "0202u":
        if "z20.822" not in row['Dx'].lower():
            return {
                "error": "Missing DX Z20.822 for CPT 0202U",
                "correction": "Add DX Z20.822"
            }
    return {}

def rule10(row: pd.Series) -> dict:
    # Medicare: Don't bill S9083, Q0144, S0028
    if row['Proc'] in {"S9083", "Q0144", "S0028"} and "medicare" in row['Insurance'].lower():
        return {
            "error": f"CPT {row['Proc']} not allowed for Medicare",
            "correction": "Remove this code for Medicare"
        }
    return {}

def rule11(row: pd.Series) -> dict:
    # G0008 only for Medicare flu shots
    if row['Proc'] == "G0008" and "medicare" not in row['Insurance'].lower():
        return {
            "error": "G0008 only valid for Medicare flu vaccines",
            "correction": "Remove G0008 for non-Medicare"
        }
    return {}

def rule12(row: pd.Series) -> dict:
    # UHC restrictions
    uhc_codes = {"S9083", "A9150", "A4565", "97803", "99051", "87070", "Q0144"}
    if row['Proc'] in uhc_codes and "united healthcare" in row['Insurance'].lower():
        return {
            "error": f"CPT {row['Proc']} invalid for UHC",
            "correction": "Remove code for UHC"
        }
    return {}

def rule13(row: pd.Series) -> dict:
    # Aetna S9083 restriction
    if row['Proc'] == "S9083" and "aetna" in row['Insurance'].lower():
        return {
            "error": "S9083 not allowed for Aetna",
            "correction": "Remove S9083 for Aetna"
        }
    return {}

def rule14(row: pd.Series) -> dict:
    # Provider validation
    provider = str(row['Provider']).lower()
    insurance = row['Insurance'].lower()
    
    if "medicare" in insurance:
        if "johnson, lakeia" in provider and "**********" not in str(row.get('NPI', '')):
            return {
                "error": "Medicare claims for Lakeia Johnson require NPI **********",
                "correction": "Update NPI to **********"
            }
        
    if "lynch, jisann" in provider and "shaaban" not in str(row.get('Rendering Provider', '')).lower():
        return {
            "error": "Jisann Lynch claims must be under Hamid Shaaban",
            "correction": "Update rendering provider to Shaaban, Hamid"
        }
    return {}

def rule15(row: pd.Series) -> dict:
    # Horizon BCBS description cleanup
    jq_codes = {"J", "Q"}
    if any(row['Proc'].startswith(c) for c in jq_codes) and "horizon bcbs" in row['Insurance'].lower():
        if pd.notna(row['Description']) and str(row['Description']).strip() != "":
            return {
                "error": "Remove description for J/Q codes in Horizon BCBS",
                "correction": "Clear description field"
            }
    return {}

def rule16(row: pd.Series) -> dict:
    # Rule 16: CPT 82962 (Expired) should be replaced with CPT 82947
    if str(row['Proc']).strip() == "82962":
        return {
            "error": "CPT 82962 is expired. Use CPT 82947 instead.",
            "correction": "Replace CPT 82962 with CPT 82947"
        }
    return {}

def rule17(row: pd.Series) -> dict:
    # Rule 17: CPT 99354 (Expired) should be replaced with CPT 99415 or 99417 (depends on time)
    if str(row['Proc']).strip() == "99354":
        return {
            "error": "CPT 99354 is expired. Use CPT 99415 or 99417 instead (depends on time).",
            "correction": "Replace CPT 99354 with CPT 99415 or 99417 as appropriate"
        }
    return {}

def rule18(group: pd.DataFrame) -> dict:
    """
    If CPT 96365 is present along with CPT 96374 in the same encounter,
    ensure that 96374 has modifier 59. If not, raise an error.
    """
    has_96365 = (group['Proc'] == "96365").any()
    idx_96374 = group[group['Proc'] == "96374"].index
    if has_96365 and not idx_96374.empty:
        for idx in idx_96374:
            mods = str(group.loc[idx, 'Mod'])
            if "59" not in mods.split(','):
                return {
                    "error": "CPT 96374 must have modifier 59 when billed with 96365",
                    "correction": "Append modifier 59 to CPT 96374"
                }
    return {}