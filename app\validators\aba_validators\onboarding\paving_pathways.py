from typing import Dict, List, <PERSON>ple
from ..utils import map_segment




# constants.py
GROUP_NPI = "**********"
PRACTICE_INSURANCE_NAMES = {"CHAMPVA", "CAREFIRST", "ANTHEM"}


def validate_insurance_specific_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    Rule: For Champva, Carefirst, Anthem insurance companies (starts with), procedure codes 97155 and 97156
    must have HO modifier on service lines.
    """
    errors: List[Tuple[str, str]] = []

    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]

    # Extract insurance company names (uppercased)
    insurance_names = set(
        seg.get("Name Last or Organization Name", "").upper()
        for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"
    )

    # Check if any insurance name starts with any name in PRACTICE_INSURANCE_NAMES
    applies = any(
        any(ins_name.startswith(prac_name) for prac_name in PRACTICE_INSURANCE_NAMES)
        for ins_name in insurance_names
    )

    if not applies:
        return errors  # Rule doesn't apply, return no errors

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    seen_proc_codes = set()

    for seg in service_lines:
        svc_id = seg["Service Identification"]
        parts = svc_id.split(":")

        if len(parts) < 2:
            continue

        proc_code = parts[1]
        modifiers = parts[2:] if len(parts) > 2 else []

        if proc_code in {"97155", "97156"} and "HO" not in modifiers and proc_code not in seen_proc_codes:
            errors.append((
                f"For insurance(s) {', '.join(PRACTICE_INSURANCE_NAMES)}, procedure code {proc_code} is missing 'HO' modifier.",
                f"Change modifier for {proc_code} to 'HO' for insurance(s): {', '.join(PRACTICE_INSURANCE_NAMES)}"
            ))
            seen_proc_codes.add(proc_code)

    return errors




def handle_dx_rule(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    Rule: If diagnosis codes (DX) are missing in HI segments of claim_segments → raise an error.
    """
    errors = []

    dx_found = False
    for segment in claim.get("claim_segments", []):
        if isinstance(segment, str) and segment.startswith("HI*"):
            print(f"HI segment found: {segment}")
            elements = segment.split("*")[1:]
            for elem in elements:
                if ":" in elem:
                    qualifier, dx_code = elem.split(":", 1)
                    if dx_code.strip():
                        print(f"Diagnosis code found: {dx_code}")
                        dx_found = True
                        break
            if dx_found:
                break 

    if not dx_found:
        print("No diagnosis code found — raising error.")
        errors.append((
            "Missing diagnosis code in claim_segments.",
            "At least one diagnosis code (DX) is required in HI segment."
        ))

    return errors




def validate_pp_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combine all Paving Pathways rules."""
    return (
        validate_insurance_specific_modifiers(claim)+
        handle_dx_rule(claim)
    )
