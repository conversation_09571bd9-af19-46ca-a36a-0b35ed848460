from typing import List, Tuple, Dict
from .utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
BCBS_MODIFIER_CUTOFF = datetime(2023, 5, 25)

def validate_adbc_tricare_97151_two_weeks(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: TRICARE 97151 must be billed within a 2-week period."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_tricare = any(seg.get("Application Receiver's Code", "").upper().startswith("TRICARE") for seg in mapped_all if "Application Receiver's Code" in seg)
    if not is_tricare:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dates_97151 = []
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "97151":
            dos = next((s.get("Date Time Period") for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), None)
            if dos and seg.get("Date Time Period Format Qualifier") == "D8":
                dos_date = datetime.strptime(dos, "%Y%m%d")
                dates_97151.append(dos_date)
                # except ValueError:
                    # errors.append((f"Invalid DOS format for 97151: {dos}", "Use YYYYMMDD format"))

    if dates_97151:
        min_date = min(dates_97151)
        max_date = max(dates_97151)
        if (max_date - min_date).days > 14:
            errors.append((
                f"TRICARE 97151 spans {min_date.strftime('%Y%m%d')} to {max_date.strftime('%Y%m%d')} (>14 days)",
                "Bill 97151 within a 2-week period per TRICARE rules"
            ))
    return errors

def validate_adbc_tricare_t1023_once_per_auth(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 3: TRICARE T1023 can be billed only once per auth period."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_tricare = any(seg.get("Application Receiver's Code", "").upper().startswith("TRICARE") for seg in mapped_all if "Application Receiver's Code" in seg)
    if not is_tricare:
        return []

    t1023_count = sum(1 for seg in mapped_all if "Service Identification" in seg and seg["Service Identification"].split(":")[1] == "T1023")
    if t1023_count > 1:
        errors.append((
            f"TRICARE T1023 billed {t1023_count} times in claim",
            "Bill T1023 only once per authorization period"
        ))
    return errors

def validate_adbc_tricare_97155_97156_units(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: TRICARE 97155/97156 limited to 8 units per day."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_tricare = any(seg.get("Application Receiver's Code", "").upper().startswith("TRICARE") for seg in mapped_all if "Application Receiver's Code" in seg)
    if not is_tricare:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos_units = {}
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in {"97155", "97156"}:
            dos = next((s.get("Date Time Period") for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), "Unknown")
            try:
                units = float(seg.get("Units/Days", "").strip())
                if dos not in dos_units:
                    dos_units[dos] = {}
                dos_units[dos][parts[1]] = dos_units[dos].get(parts[1], 0) + units
            except ValueError:
                errors.append((f"Invalid Units/Days '{seg.get('Units/Days', '')}' for {parts[1]}", "Ensure valid numeric units"))

    for dos, cpt_units in dos_units.items():
        for cpt, units in cpt_units.items():
            if units > 8:
                errors.append((
                    f"TRICARE {cpt} exceeds 8 units on DOS {dos}; found {units}",
                    "Limit 97155/97156 to 8 units per day"
                ))
    return errors

def validate_adbc_tricare_overlap(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 5: TRICARE 97155/97156 cannot overlap with direct (97153/97154)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_tricare = any(seg.get("Application Receiver's Code", "").upper().startswith("TRICARE") for seg in mapped_all if "Application Receiver's Code" in seg)
    if not is_tricare:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    time_ranges = {}
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in {"97153", "97154", "97155", "97156"}:
            dtp = next((s for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), None)
            if dtp and dtp.get("Date Time Period Format Qualifier") == "RD8":
                start_date, end_date = dtp["Date Time Period"].split("-")
                try:
                    start = int(start_date[-4:])
                    end = int(end_date[-4:])
                    time_ranges[(parts[1], start_date[:8])] = (start, end)
                except ValueError:
                    errors.append((f"Invalid time format in DTP: {dtp['Date Time Period']}", "Use YYYYMMDDHHMM format"))

    for (cpt1, date1), (start1, end1) in time_ranges.items():
        for (cpt2, date2), (start2, end2) in time_ranges.items():
            if date1 == date2 and cpt1 in {"97155", "97156"} and cpt2 in {"97153", "97154"} and not (end1 <= start2 or end2 <= start1):
                errors.append((
                    f"TRICARE overlap: {cpt1} ({start1}-{end1}) with {cpt2} ({start2}-{end2}) on {date1}",
                    "Avoid overlapping 97155/97156 with 97153/97154"
                ))
    return errors

def validate_adbc_bcbs_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rules 8 & 9: BCBS modifier rules (no modifiers pre-05/25/2023, required post-05/25/2023)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_bcbs = any(seg.get("Application Receiver's Code", "").upper().startswith("BCBS") for seg in mapped_all if "Application Receiver's Code" in seg)
    if not is_bcbs:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((s.get("Date Time Period") for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos and seg.get("Date Time Period Format Qualifier") == "D8" else CURRENT_DATE

    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            modifier = parts[2] if len(parts) > 2 else ""
            if dos_date < BCBS_MODIFIER_CUTOFF and modifier and parts[1] == "97153":
                errors.append((
                    f"BCBS RBT session {parts[1]} on {dos} has modifier '{modifier}' before 05/25/2023",
                    "Do not use modifiers for BCBS RBT sessions (97153) before 05/25/2023"
                ))
            elif dos_date >= BCBS_MODIFIER_CUTOFF and not modifier:
                errors.append((
                    f"BCBS {parts[1]} on {dos} missing modifier after 05/25/2023",
                    "Add modifier for BCBS claims post-05/25/2023"
                ))
    return errors

def validate_adbc_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all ADBC-specific validation rules."""
    return (
        validate_adbc_tricare_97151_two_weeks(claim) +
        validate_adbc_tricare_t1023_once_per_auth(claim) +
        validate_adbc_tricare_97155_97156_units(claim) +
        validate_adbc_tricare_overlap(claim) +
        validate_adbc_bcbs_modifiers(claim)
    )