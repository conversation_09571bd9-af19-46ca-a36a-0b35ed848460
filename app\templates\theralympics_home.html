<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Theralympics EDI Validator</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Lottie Player -->
  <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary: #4361ee;
      --primary-dark: #3a0ca3;
      --secondary: #4cc9f0;
      --accent: #f72585;
      --dark: #14213d;
      --light: #f8f9fa;
      --card-bg: #ffffff;
      --text-primary: #212529;
      --text-secondary: #6c757d;
      --error-bg: #f8d7da;
      --error-text: #721c24;
      --success-bg: #d1e7dd;
      --success-text: #0f5132;
      --success: #4CAF50;
      --danger: #f44336;
      --warning: #ff9800;
      --info: #2196F3;
    }

    [data-theme="dark"] {
        --primary: #4895ef;
        --primary-dark: #4361ee;
        --secondary: #4cc9f0;
        --accent: #f72585;
        --dark: #f8f9fa;
        --light: #121212;
        --card-bg: #1e1e1e;
        --feature-card: #2d2d2d;
        --text-primary: #f8f9fa;
        --text-secondary: #adb5bd;
        --error-bg: #422b2d;
        --error-text: #f1aeb5;
        --success-bg: #1a3a2e;
        --success-text: #75b798;
        --success: #81c784;
        --danger: #e57373;
        --warning: #ffb74d;
        --info: #64b5f6;
    }

    body {
      background-color: var(--light);
      color: var(--text-dark);
      transition: all 0.3s ease;
      font-family: 'Poppins', sans-serif;
    }

    .text-muted {
      color: var(--text-muted) !important;
    }

    .card-3d {
      transform-style: preserve-3d;
      perspective: 1000px;
      transition: transform 0.5s;
      background-color: var(--card-bg);
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      border: none;
    }

    .card-3d:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .upload-area {
      border: 2px dashed var(--primary);
      transition: all 0.3s;
      border-radius: 12px;
      background-color: rgba(67, 97, 238, 0.03);
      cursor: pointer;
    }

    .upload-area:hover {
      border-color: var(--accent);
      background-color: rgba(247, 37, 133, 0.05);
    }

    .theme-toggle {
      cursor: pointer;
      transition: all 0.3s;
      background: rgba(67, 97, 238, 0.1);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .theme-toggle:hover {
      transform: scale(1.1);
      background: rgba(67, 97, 238, 0.2);
    }

    .btn-primary-custom {
      background-color: var(--primary);
      border-color: var(--primary);
      color: white;
      padding: 12px 24px;
      border-radius: 50px;
      font-weight: 500;
      letter-spacing: 0.5px;
      transition: all 0.3s;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .btn-primary-custom:hover {
      background-color: var(--primary-dark);
      border-color: var(--primary-dark);
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .theralympics-icon {
      color: var(--secondary);
    }

    /* Custom error message style */
    .error-message {
        background-color: var(--error-bg) !important;
        color: var(--error-text) !important;
        border-radius: 8px;
        padding: 12px;
        margin-top: 15px;
        display: none;
        border-left: 4px solid var(--error-border);
    }

    .header-title {
      background: linear-gradient(90deg, var(--primary), var(--secondary));
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      font-weight: 700;
    }

    .feature-card {
      border-radius: 12px;
      transition: all 0.3s;
      background-color: var(--feature-card);
      border: 1px solid rgba(0, 0, 0, 0.05);
    }

    [data-theme="dark"] .feature-card {
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .back-btn {
      border-radius: 50px;
      padding: 8px 16px;
      transition: all 0.3s;
    }

    .back-btn:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    [data-theme="dark"] {
        --light: #2d3748;
        --card-bg: #4a5568;
        --feature-card: #718096;
    }
            
    [data-theme="dark"] {
        --light: #3a3636;
        --card-bg: #504a4a;
        --feature-card: #6b6565;
    }
            
    [data-theme="dark"] {
        --light: #3d3d3d;
        --card-bg: #525252;
        --feature-card: #6b6b6b;
    }
            
    [data-theme="dark"] {
        --light: #1a202c;
        --card-bg: #2d3748;
        --feature-card: #4a5568;
    }

    footer {
      background-color: rgba(0, 0, 0, 0.02);
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      color: var(--text-muted);
    }

    [data-theme="dark"] footer {
      background-color: rgba(255, 255, 255, 0.02);
      border-top: 1px solid rgba(255, 255, 255, 0.05);
    }

    .btn-outline-primary {
      color: var(--primary);
      border-color: var(--primary);
    }

    .btn-outline-primary:hover {
      background-color: var(--primary);
      color: white;
    }

    .file-name {
      color: var(--success-text);
    }

    .icon-wrapper {
      width: 60px;
      height: 60px;
      background: rgba(67, 97, 238, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }
  </style>
</head>
<body class="min-h-screen mx-5">
  <div class="container py-5">
    <!-- Header with Theme Toggle -->
    <div class="d-flex justify-content-between align-items-center mb-5">
      <div>
        <h1 class="display-5 fw-bold header-title">
          <i class="fas fa-dumbbell theralympics-icon me-2"></i>
          Theralympics 837 EDI Validator
        </h1>
        <p class="text-muted">Validate your 837 EDI files with precision and care</p>
      </div>
      <div class="theme-toggle" id="themeToggle">
        <i class="fas fa-moon fs-5" id="themeIcon"></i>
      </div>
    </div>
    
    <!-- Main Card -->
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="card card-3d shadow-lg">
          <div class="card-body p-5">
            <!-- Back Arrow Button -->
            <button class="btn btn-outline-secondary back-btn mb-4" onclick="goBack()">
              <i class="fas fa-arrow-left me-2"></i> Go Back
            </button>
            
            <!-- Animation -->
            <div class="text-center mb-4">
              <lottie-player 
                src="https://assets5.lottiefiles.com/packages/lf20_3vbOcw.json" 
                background="transparent" 
                speed="1" 
                style="width: 200px; height: 200px; margin: 0 auto;" 
                loop autoplay>
              </lottie-player>
            </div>
            
            <h3 class="card-title text-center mb-4 fw-bold">
              <i class="fas fa-file-medical theralympics-icon me-2"></i>
              Upload 837 EDI File for Validation
            </h3>
            
            <!-- Upload Form -->
            <form action="/theralympics/validate-837" method="post" enctype="multipart/form-data" id="uploadForm">
              <div class="upload-area rounded-3 p-5 text-center mb-4" id="dropArea">
                <i class="fas fa-cloud-upload-alt theralympics-icon fs-1 mb-3"></i>
                <p class="mb-2">Drag & drop your 837 file here or</p>
                <input type="file" name="file" id="fileInput" class="d-none" required accept=".txt,.edi">
                <label for="fileInput" class="btn btn-outline-primary px-4 py-2 rounded-pill">
                  <i class="fas fa-folder-open me-2"></i> Browse Files
                </label>
                <div class="mt-3" id="fileName"></div>
              </div>

              <!-- Custom Error Message -->
              <div id="errorMessage" class="error-message">
                <i class="fas fa-exclamation-circle me-2"></i> Please upload a file before validating.
              </div>
              
              <div class="d-grid">
                <button type="button" class="btn btn-primary-custom btn-lg" id="validateButton">
                  <i class="fas fa-check-circle me-2"></i> Validate 837 File
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Features Section -->
    <div class="row mt-5">
      <div class="col-md-4 mb-4">
        <div class="card h-100 feature-card p-4">
          <div class="card-body text-center">
            <div class="icon-wrapper mb-3">
              <i class="fas fa-shield-alt theralympics-icon fs-4"></i>
            </div>
            <h5 class="fw-bold mb-3">Secure Validation</h5>
            <p class="text-muted">Your healthcare data is processed securely with industry-standard encryption.</p>
          </div>
        </div>
      </div>
      <div class="col-md-4 mb-4">
        <div class="card h-100 feature-card p-4">
          <div class="card-body text-center">
            <div class="icon-wrapper mb-3" style="background: rgba(247, 37, 133, 0.1);">
              <i class="fas fa-bolt theralympics-icon fs-4" style="color: var(--accent)"></i>
            </div>
            <h5 class="fw-bold mb-3">Fast Processing</h5>
            <p class="text-muted">Quick validation results to keep your healthcare operations running smoothly.</p>
          </div>
        </div>
      </div>
      <div class="col-md-4 mb-4">
        <div class="card h-100 feature-card p-4">
          <div class="card-body text-center">
            <div class="icon-wrapper mb-3">
              <i class="fas fa-chart-line theralympics-icon fs-4"></i>
            </div>
            <h5 class="fw-bold mb-3">Detailed Reports</h5>
            <p class="text-muted">Comprehensive validation reports to help you maintain compliance.</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Footer -->
    <footer class="py-4 text-center rounded-3 mt-5">
      <p class="mb-0">© 2025 Theralympics EDI Validator | Built with care for the medical community</p>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
      // Theme Toggle
      const themeToggle = document.getElementById('themeToggle');
      const themeIcon = document.getElementById('themeIcon');
      const html = document.documentElement;
      
      // Check for saved theme preference or use preferred color scheme
      const currentTheme = localStorage.getItem('theme') || 
                          (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
      
      if (currentTheme === 'dark') {
          html.setAttribute('data-theme', 'dark');
          themeIcon.classList.replace('fa-moon', 'fa-sun');
      }

      themeToggle.addEventListener('click', () => {
          if (html.getAttribute('data-theme') === 'light') {
              html.setAttribute('data-theme', 'dark');
              themeIcon.classList.replace('fa-moon', 'fa-sun');
              localStorage.setItem('theme', 'dark');
          } else {
              html.setAttribute('data-theme', 'light');
              themeIcon.classList.replace('fa-sun', 'fa-moon');
              localStorage.setItem('theme', 'light');
          }
      });

      // Listen for theme changes from other tabs/windows
      window.addEventListener('storage', (e) => {
          if (e.key === 'theme') {
              const newTheme = e.newValue;
              html.setAttribute('data-theme', newTheme);
              if (newTheme === 'dark') {
                  themeIcon.classList.replace('fa-moon', 'fa-sun');
              } else {
                  themeIcon.classList.replace('fa-sun', 'fa-moon');
              }
          }
      });
      
      // Go Back to the previous page
      function goBack() {
        window.location.href = '/';
      }
      
      // File Upload Handling
      const fileInput = document.getElementById('fileInput');
      const dropArea = document.getElementById('dropArea');
      const fileName = document.getElementById('fileName');
      const validateButton = document.getElementById('validateButton');
      const errorMessage = document.getElementById('errorMessage');
      
      // On file selection, hide the error message
      fileInput.addEventListener('change', function(e) {
        if (this.files.length) {
          fileName.innerHTML = `<span class="file-name"><i class="fas fa-check-circle me-2"></i>Selected: ${this.files[0].name}</span>`;
          errorMessage.style.display = 'none';
        }
      });

      // Validation on button click
      validateButton.addEventListener('click', function() {
        if (fileInput.files.length === 0) {
          errorMessage.style.display = 'block';
        } else {
          errorMessage.style.display = 'none';
          document.getElementById('uploadForm').submit();
        }
      });
      
      // Drag and drop functionality
      ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
      });
      
      function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
      });
      
      ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
      });
      
      function highlight() {
        dropArea.classList.add('bg-primary', 'bg-opacity-10');
      }
      
      function unhighlight() {
        dropArea.classList.remove('bg-primary', 'bg-opacity-10');
      }
      
      dropArea.addEventListener('drop', handleDrop, false);
      
      function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length) {
          fileInput.files = files;
          fileName.innerHTML = `<span class="file-name"><i class="fas fa-check-circle me-2"></i>Selected: ${files[0].name}</span>`;
          errorMessage.style.display = 'none';
        }
      }
        // Go Back to the previous page
        function goBack() {
            window.location.href = '/';
        }
    </script>
  </div>
</body>
</html>