from typing import List, Tu<PERSON>, Dict
from ..utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Constants
TAX_ID = "*********"
GROUP_NPI = "**********"
# PROVIDER_NPI = "**********"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
# MIM_PROVIDER_NPI = "**********"

# Special cases
IFAH_PATIENTS = {"YAZED_TURSHAN", "MAHER_ZAHRAH"}
RBT_MASTERS_PROVIDER = {"MADYSEN_GAULT"}

MIM_STANDARD_RATES = {
    "97151": 10.73,
    "97153:HN": 19.92,
    "97153:HM": 15.94,
    "97155": 24.96,
    "97156": 23.79,
}


def validate_mim_provider_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """All claims should be submitted under provider <PERSON> (NPI: **********)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["claim_segments"]]
    billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "82"), None)
    # print(f"DEBUG: Billing NPI found: {billing_npi}")  # Debug output
    REQUIRED_NPI = "**********"
    if billing_npi != REQUIRED_NPI:
        errors.append(( 
            f"Claim submitted with NPI {billing_npi} instead of Katy Wilson's NPI ({**********})",
            f"Use provider NPI {**********} in NM1*85"
        ))
    return errors


def validate_mim_standard_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    segments = claim.get("claim_segments") or []
    mapped_segments = [mapped for mapped in (map_segment(seg) for seg in segments if seg) if mapped]
    # print(f"Total claim segments mapped: {len(mapped_segments)}")
    service_lines = [seg for seg in mapped_segments if seg and "Service Identification" in seg]
    # print(f"Service lines found: {len(service_lines)}")

    for seg in service_lines:
        service_id = seg.get("Service Identification", "")
        # print(f"\nProcessing service line: {service_id}")
        parts = service_id.split(":")

        if len(parts) < 2:
            # print("Skipping: Not enough parts in Service Identification")
            continue

        procedure_qualifier = parts[0].strip()
        cpt = parts[1].strip()

        # Extract modifiers from parts[2:6]; if none found, use procedure_qualifier as modifier
        modifiers = [p.strip() for p in parts[2:6] if p.strip()]
        modifier = modifiers[0] if modifiers else procedure_qualifier

        # print(f"Extracted CPT code: '{cpt}'")
        # print(f"Extracted modifier: '{modifier}'")

        expected_rate = None
        if cpt == "97153":
            if modifier == "HN":
                expected_rate = 19.92
            elif modifier == "HM":
                expected_rate = 15.94
        elif cpt == "97151":
            expected_rate = 10.73
        elif cpt == "97155":
            expected_rate = 24.96
        elif cpt == "97156":
            expected_rate = 23.79


        total_charge_str = seg.get("Monetary Amount", "").strip()
        units_str = seg.get("Units/Days", "").strip()
        # print(f"Total charge string: '{total_charge_str}', Units string: '{units_str}'")

        total_charge = float(total_charge_str)
        units = float(units_str)
        print(f"Parsed total charge: {total_charge}, units: {units}")


        actual_per_unit = round(total_charge / units, 2)
        # print(f"Actual per-unit rate calculated: {actual_per_unit}, Expected per-unit rate: {expected_rate}")

        if actual_per_unit != expected_rate:
            rate_key = f"{cpt}:{modifier}" if modifier else cpt
            error_msg = f"Incorrect per-unit rate for {rate_key}: charged {actual_per_unit}/unit, expected {expected_rate}/unit"
            fix_msg = f"Set per-unit charge to {expected_rate}"
            errors.append((error_msg, fix_msg))
            # print(f"Error appended: {error_msg}")

    # print(f"\nTotal errors found: {len(errors)}")
    return errors


def validate_mim_97153_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """For CPT 97153, require provider-level modifier except for BCBS Community insurance."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Name Last or Organization Name", "").upper() for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"), "")
    is_bcbs_community = "BLUE CROSS COMMUNITY HEALTH" in payer_name or "BCCHP" in payer_name
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) < 2:
            continue
        cpt = parts[1].strip()
        modifier = parts[2].strip() if len(parts) > 2 else ""
        if cpt == "97153":
            if not is_bcbs_community and not modifier:
                errors.append((
                    "CPT 97153 missing provider-level modifier (HM/HN/HO) for non-BCBS Community insurance.",
                    "Add provider-level modifier for CPT 97153."
                ))
    return errors


# def validate_mim_no_modifiers_97151_97155_97156(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
#     """For CPTs 97151, 97155, 97156, no provider-level modifier except for BCBS Community insurance."""
#     errors = []
#     mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
#     payer_name = next((seg.get("Name Last or Organization Name", "").upper() for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"), "")
#     is_bcbs_community = "BLUE CROSS COMMUNITY HEALTH" in payer_name or "BCCHP" in payer_name
#     service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
#     for seg in service_lines:
#         parts = seg["Service Identification"].split(":")
#         if len(parts) < 2:
#             continue
#         cpt = parts[1].strip()
#         modifier = parts[2].strip() if len(parts) > 2 else ""
#         if cpt in ("97151", "97155", "97156"):
#             if not is_bcbs_community and modifier:
#                 errors.append((
#                     f"CPT {cpt} should not have provider-level modifier for non-BCBS Community insurance.",
#                     f"Remove provider-level modifier for CPT {cpt}."
#                 ))
#     return errors


def validate_mim_telehealth_95_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """For Telehealth sessions (POS 02 & 10), require 95 modifier."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_all:
        if "Service Identification" not in seg:
            continue
        service_id = seg["Service Identification"]
        parts = service_id.split(":")
        if len(parts) < 2:
            continue
        cpt = parts[1].strip()
        modifiers = [p.strip() for p in parts[2:6] if p.strip()]
        pos = seg.get("Place of Service", "").strip()
        if pos in ("02", "10"):
            if "95" not in modifiers:
                errors.append((
                    f"Telehealth POS {pos} for CPT {cpt} missing 95 modifier.",
                    f"Add 95 modifier for telehealth sessions (POS {pos})."
                ))
    return errors


def validate_mim_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rates: $25 for 97153, $30 for 97151, 97155, 97156 for all insurance."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        service_id = seg.get("Service Identification", "")
        parts = service_id.split(":")
        if len(parts) < 2:
            continue
        cpt = parts[1].strip()
        total_charge_str = seg.get("Monetary Amount", "").strip()
        units_str = seg.get("Units/Days", "").strip()
        try:
            total_charge = float(total_charge_str)
            units = float(units_str)
        except Exception:
            continue
        if units == 0:
            continue
        actual_per_unit = round(total_charge / units, 2)
        expected_rate = None
        if cpt == "97153":
            expected_rate = 25.00
        elif cpt in ("97151", "97155", "97156"):
            expected_rate = 30.00
        if expected_rate is not None and actual_per_unit != expected_rate:
            errors.append((
                f"Incorrect per-unit rate for {cpt}: charged {actual_per_unit}/unit, expected {expected_rate}/unit",
                f" Expected per-unit charge: ${expected_rate:.2f}"
            ))
    return errors


def validate_mim_madysen_gault_ho_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """For provider Madysen Gault (NPI **********), require HO modifier on all service lines."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["claim_segments"]]
    provider_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "82"), None)
    provider_name = next((seg.get("Name Last or Organization Name", "").upper() for seg in mapped_all if seg.get("Entity Identifier Code") == "82"), "")
    if provider_npi == "**********" or "MADYSEN GAULT" in provider_name:
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            service_id = seg.get("Service Identification", "")
            parts = service_id.split(":")
            if len(parts) < 2:
                continue
            modifiers = [p.strip() for p in parts[2:6] if p.strip()]
            if "HO" not in modifiers:
                errors.append((
                    "Madysen Gault (NPI **********) requires HO modifier on all service lines.",
                    "Add HO modifier for all service lines when provider is Madysen Gault."
                ))
    return errors


def validate_mim_bcbs_community_taxonomy(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """For Blue Cross Community Health plan, require group taxonomy 193200000X in 33 and 103K00000X in 24J."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Name Last or Organization Name", "").upper() for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"), "")
    if "BLUE CROSS COMMUNITY HEALTH" in payer_name or "BCCHP" in payer_name:
        # Group taxonomy (33, PRV*BI*PXC)
        group_taxonomy = next((seg.get("Reference Identification") for seg in mapped_all if seg.get("Provider Code") == "BI" and seg.get("Reference Identification Qualifier") == "PXC"), None)
        if group_taxonomy != "193200000X":
            errors.append((
                f"BCCHP requires group taxonomy code '193200000X'; found '{group_taxonomy}'.",
                "Please include the correct group taxonomy code '193200000X' for BCCHP claims"
            ))
        # Individual taxonomy (24J, PRV*PE*PXC)
        ind_taxonomy = next((seg.get("Reference Identification") for seg in mapped_all if seg.get("Provider Code") == "PE" and seg.get("Reference Identification Qualifier") == "PXC"), None)
        if ind_taxonomy != "103K00000X":
            errors.append((
                f"BCCHP requires individual taxonomy code '103K00000X'; found '{ind_taxonomy}'.",
                "Please include the correct group taxonomy code '103K00000X' for BCCHP claims"
            ))
    return errors


def validate_mimd_in_motion_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combine all validations for Minds in Motion ABA."""
    return (
        validate_mim_provider_npi(claim)
        + validate_mim_97153_modifier(claim)
        # + validate_mim_no_modifiers_97151_97155_97156(claim)
        # + validate_mim_telehealth_95_modifier(claim)
        + validate_mim_rates(claim)
        + validate_mim_madysen_gault_ho_modifier(claim)
        + validate_mim_bcbs_community_taxonomy(claim)
    )
