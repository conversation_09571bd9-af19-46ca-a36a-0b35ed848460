from typing import List, Tu<PERSON>, Dict
from .utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "93-1391538"
GROUP_NPI = "**********"
PROVIDER_NPI = "**********"  # Teba Aijaz
PROVIDER_SSN = "*********"  # Teba's SSN without hyphens per change request
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
BCBSIL_HM_START_DATE = datetime(2024, 1, 22)  # From update on 01/22/2024
AETNA_SSN_NPI_START_DATE = datetime(2024, 10, 22)  # From update on 10/22/2024

def validate_green_pediatrics_bcbsil_97153_hm(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 1: BCBS-IL 97153 requires HM modifier post-01/22/2024 since therapist is not an RBT."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_bcbsil = any(seg.get("Payer Name", "").upper() in {"BCBS-IL", "BCBS IL"} or "BCBS" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_bcbsil:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE

    if dos_date >= BCBSIL_HM_START_DATE:
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] == "97153":
                modifier = parts[2] if len(parts) > 2 else ""
                if modifier != "HM":
                    errors.append((
                        f"BCBS-IL CPT 97153 on {dos} missing HM modifier; found '{modifier}'",
                        "Use HM modifier for 97153 post-01/22/2024 as therapist is not an RBT"
                    ))

    return errors

def validate_green_pediatrics_aetna_teba_ssn_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: Aetna requires Teba's SSN as Tax ID (Box 25) and NPI/Name (Box 33) post-10/22/2024."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_aetna = any(seg.get("Payer Name", "").upper() == "AETNA" or "AETNA" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_aetna:
        return []

    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE

    if dos_date >= AETNA_SSN_NPI_START_DATE:
        # Check Tax ID (Box 25) in REF*EI
        tax_id = next((seg.get("Reference Identification") for seg in mapped_all if seg.get("Reference Identification Qualifier") == "EI"), None)
        if tax_id != PROVIDER_SSN:
            errors.append((
                f"Aetna claim Tax ID {tax_id} does not match Teba's SSN {PROVIDER_SSN} in Box 25 post-10/22/2024",
                f"Use Teba Aijaz's SSN {PROVIDER_SSN} as Tax ID in REF*EI for Aetna"
            ))

        # Check Billing Provider NPI and Name (Box 33) in NM1*85
        billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
        billing_name = next((seg.get("Name Last or Organization Name", "").upper() + " " + seg.get("Name First", "").upper() for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
        if billing_npi != PROVIDER_NPI:
            errors.append((
                f"Aetna claim Billing NPI {billing_npi} does not match Teba's NPI {PROVIDER_NPI} in Box 33 post-10/22/2024",
                f"Use Teba Aijaz's NPI {PROVIDER_NPI} in NM1*85 for Aetna"
            ))
        if billing_name != "AIJAZ TEBA":
            errors.append((
                f"Aetna claim Billing Name '{billing_name}' does not match 'Teba Aijaz' in Box 33 post-10/22/2024",
                "Use 'Teba Aijaz' as Billing Provider Name in NM1*85 for Aetna"
            ))

    return errors

def validate_green_pediatrics_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Green Pediatrics-specific validation rules."""
    return (
        validate_green_pediatrics_bcbsil_97153_hm(claim) +
        validate_green_pediatrics_aetna_teba_ssn_npi(claim)
    )