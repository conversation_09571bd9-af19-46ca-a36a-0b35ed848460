from typing import List, <PERSON><PERSON>, Dict
from .utils import map_segment

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
PROVIDER_NPIS = {
    "Phoebe Forsley": "**********"
}

def validate_snh_rendering_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 3: All claims submitted under <PERSON> Forsley."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Get all NM1*82 segments (rendering providers)
    rendering_providers = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "82"]
    
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        # Get associated rendering provider via REF
        ref_id = seg.get("Reference Identification")
        rendering_npi = next((rp["Identification Code"] for rp in rendering_providers 
                             if rp.get("Reference Identification") == ref_id), "Unknown")
        
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt = parts[1]
            if rendering_npi != PROVIDER_NPIS["Phoebe Forsley"]:
                errors.append((
                    f"CPT {cpt} billed with Rendering NPI {rendering_npi}",
                    f"Bill under Phoebe Forsley (NPI: {PROVIDER_NPIS['Phoebe Forsley']}) per SNH rules"
                ))
    return errors

def validate_snh_bcbs_nh_ho_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: BCBS of NH uses HO modifier for 97153."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_bcbs_nh = any("BCBS" in seg.get("Payer Name", "").upper() and "NH" in seg.get("Payer Name", "").upper() or
                     "BCBS" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_bcbs_nh:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "97153":
            modifier = parts[2] if len(parts) > 2 else ""
            if modifier != "HO":
                errors.append((
                    f"BCBS NH CPT 97153 with modifier '{modifier}'",
                    "Use HO modifier for CPT 97153 per BCBS NH rules"
                ))
    return errors

def validate_snh_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all SNH Behavior Services-specific validation rules."""
    return (
        validate_snh_rendering_provider(claim) +
        validate_snh_bcbs_nh_ho_modifier(claim)
    )