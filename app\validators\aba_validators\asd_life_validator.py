from typing import List, <PERSON><PERSON>, Dict
from .utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
BCBS_MODIFIER_START_DATE = datetime(2023, 7, 5)  # From update on 07/05/2023
TELEHEALTH_97155_NON_BILLABLE_DATE = datetime(2025, 1, 27)  # From update on 01/27/2025
UNITS_96137_CHANGE_DATE = datetime(2025, 2, 10)  # From update on 02/10/2025
MAGELLAN_HN_MODIFIER_DATE = datetime(2025, 4, 15)  # From update on 04/15/2025
OPTUM_GROUP_NPI_DATE = datetime(2023, 1, 24)  # From update on 01/24/2023

def validate_asd_telehealth_modifier_pos_02(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: Modifier 95 required for POS 02 (Telehealth) across all insurance."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
    
    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            modifier = parts[2] if len(parts) > 2 else ""
            if pos == "02" and "95" not in modifier:
                errors.append((
                    f"Telehealth POS 02 missing modifier 95 for CPT {parts[1]}",
                    "Add modifier 95 for POS 02 across all insurance per ASD Life rules"
                ))
    return errors

def validate_asd_bcbsil_97153_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rules 4, 5, 6: BCBS IL 97153 requires modifiers (pre/post-07/05/2023) and XP for multiple providers."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_bcbsil = any(seg.get("Payer Name", "").upper().startswith("BCBS") or "BCBS" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_bcbsil:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    # Track 97153 by DOS for XP modifier check
    dos_97153_map = {}
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "97153":
            modifier = parts[2] if len(parts) > 2 else ""
            key = (dos, seg.get("Rendering Provider Identifier", "Unknown"))
            dos_97153_map.setdefault(key, []).append(modifier)

    # Validate modifiers and XP requirement
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "97153":
            modifier = parts[2] if len(parts) > 2 else ""
            if dos_date < BCBS_MODIFIER_START_DATE and modifier:
                errors.append((
                    f"BCBS IL CPT 97153 on {dos} has modifier '{modifier}' before 07/05/2023",
                    "No modifiers required for BCBS IL 97153 before 07/05/2023"
                ))
            elif dos_date >= BCBS_MODIFIER_START_DATE and modifier not in {"HM", "HN", "HO"}:
                errors.append((
                    f"BCBS IL CPT 97153 on {dos} missing or invalid modifier; found '{modifier}'",
                    "Use HM (less than bachelor's), HN (bachelor's), or HO (master's/doctoral) post-07/05/2023"
                ))
    
    # XP modifier check for multiple providers
    for (dos, provider), modifiers in dos_97153_map.items():
        if len(modifiers) > 1 and not any("XP" in mod for mod in modifiers):
            errors.append((
                f"BCBS IL CPT 97153 on {dos} with multiple providers missing XP modifier",
                "Add XP modifier to one 97153 line when billed by different providers on same DOS (post-05/08/2024)"
            ))
    
    return errors

def validate_asd_tricare_97156_telehealth(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 12: TRICARE 97156 telehealth requires 6 months after 97151 DOS."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_tricare = any(seg.get("Name Last or Organization Name", "").upper().startswith("TRICARE") or "TRICARE" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_tricare:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dtp_97151 = None
    dtp_97156 = None
    
    for seg in mapped_all:
        if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472":
            dos = seg["Date Time Period"]
            if seg.get("Date Time Period Format Qualifier") == "D8":
                try:
                    dos_date = datetime.strptime(dos, "%Y%m%d")
                    for line in service_lines:
                        parts = line["Service Identification"].split(":")
                        if len(parts) >= 2:
                            if parts[1] == "97151" and not dtp_97151:
                                dtp_97151 = dos_date
                            elif parts[1] == "97156" and line.get("Place of Service") == "02" and not dtp_97156:
                                dtp_97156 = dos_date
                except ValueError:
                    errors.append((f"Invalid date format in DTP: {dos}", "Ensure date is in YYYYMMDD format"))

    if dtp_97156 and dtp_97151:
        delta = (dtp_97156 - dtp_97151).days
        if delta < 180:  # Approx 6 months
            errors.append((
                f"TRICARE 97156 telehealth on {dtp_97156.strftime('%Y%m%d')} billed within 6 months of 97151 ({dtp_97151.strftime('%Y%m%d')})",
                "Ensure 97156 telehealth is billed at least 6 months after 97151 DOS"
            ))
    
    return errors

def validate_asd_telehealth_97155_non_billable(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 13: 97155 telehealth non-billable post-01/27/2025."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= TELEHEALTH_97155_NON_BILLABLE_DATE:
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] == "97155" and seg.get("Place of Service") == "02":
                errors.append((
                    f"CPT 97155 telehealth on {dos} post-01/27/2025",
                    "Move 97155 telehealth sessions to non-billable per update"
                ))
    
    return errors

def validate_asd_telehealth_97153_97155_overlap(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 14: Detect 97153/97155 telehealth overlap (post-01/27/2025, TRICARE-specific)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_tricare = any(seg.get("Name Last or Organization Name", "").upper().startswith("TRICARE") or "TRICARE" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_tricare:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= TELEHEALTH_97155_NON_BILLABLE_DATE:
        telehealth_codes = {}
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] in {"97153", "97155"} and seg.get("Place of Service") == "02":
                telehealth_codes[parts[1]] = True
        
        if "97153" in telehealth_codes and "97155" in telehealth_codes:
            errors.append((
                f"TRICARE telehealth overlap: 97153 and 97155 on {dos} post-01/27/2025",
                "97153 units should be billed as is; 97155 should be non-billable (overlap detected)"
            ))
    
    return errors

def validate_asd_96137_units(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 15: 96137 must be billed with 11 units post-02/10/2025."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= UNITS_96137_CHANGE_DATE:
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] == "96137":
                try:
                    units = float(seg.get("Units/Days", "").strip())
                    if units != 11:
                        errors.append((
                            f"CPT 96137 on {dos} has {units} units; expected 11 post-02/10/2025",
                            "Bill 96137 with exactly 11 units per update"
                        ))
                except ValueError:
                    errors.append((
                        f"Invalid Units/Days '{seg.get('Units/Days', '')}' for 96137 on {dos}",
                        "Ensure Units/Days is a valid number (11 expected)"
                    ))
    
    return errors

def validate_asd_magellan_97153_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 16: Magellan 97153 requires HN modifier post-04/15/2025."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_magellan = any("MAGELLAN" in seg.get("Payer Name", "").upper() or "MAGELLAN" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_magellan:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= MAGELLAN_HN_MODIFIER_DATE:
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] == "97153":
                modifier = parts[2] if len(parts) > 2 else ""
                if "HN" not in modifier:
                    errors.append((
                        f"Magellan CPT 97153 on {dos} missing HN modifier; found '{modifier}'",
                        "Use HN modifier for Magellan 97153 per update on 04/15/2025 (HM not in contract)"
                    ))
    
    return errors

def validate_asd_optum_group_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 17: Optum claims should use ASD Life Group NPI in box 24J post-01/24/2023."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_optum = any("OPTUM" in seg.get("Name Last or Organization Name", "").upper() or "OPTUM" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_optum:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= OPTUM_GROUP_NPI_DATE:
        for seg in service_lines:
            rendering_npi = seg.get("Rendering Provider Identifier", "")
            if rendering_npi and rendering_npi != GROUP_NPI:
                errors.append((
                    f"Optum claim on {dos} using provider NPI {rendering_npi} instead of group NPI",
                    f"Use ASD Life Group NPI {GROUP_NPI} in box 24J for Optum claims per update on 01/24/2023"
                ))
    
    return errors

# def validate_asd_duplicate_charges(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
#     """Rule 18: Check for and merge duplicate charges."""
#     errors = []
#     mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
#     # Filter service lines and remove duplicates by converting to a set of tuples
#     seen_service_lines = set()
#     unique_service_lines = []
    
#     for seg in mapped_all:
#         # Check if the 'Service Identification' key exists in the segment
#         if 'Service Identification' not in seg:
#             # print(f"Skipping segment due to missing 'Service Identification': {seg}")
#             continue 
        
#         # Create a tuple of the important fields to compare for uniqueness
#         service_key = (seg["Service Identification"], seg.get("Place of Service", "").strip(), seg.get("Units/Days", "").strip())
        
#         if service_key not in seen_service_lines:
#             seen_service_lines.add(service_key)
#             unique_service_lines.append(seg)
    
#     # print("Unique Service Lines:", unique_service_lines)  # Debugging line
    
#     # Track services by CPT, modifier, POS, and provider
#     service_map = {}
    
#     for seg in unique_service_lines:
#         parts = seg["Service Identification"].split(":")
        
#         if len(parts) >= 2:
#             cpt = parts[1]
#             modifier = parts[2] if len(parts) > 2 else ""
#             pos = seg.get("Place of Service", "").strip()
#             rendering_npi = seg.get("Rendering Provider Identifier", "Unknown")
            
#             # print(f"Processing: CPT={cpt}, Modifier={modifier}, POS={pos}, NPI={rendering_npi}")  # Debugging line
            
#             units = float(seg.get("Units/Days", "").strip())
                
#             # Key is the combination of CPT, modifier, POS, and rendering provider
#             key = (cpt, modifier.strip(), pos.strip(), rendering_npi)
                
#                 # Adding the number of units to the service_map
#             service_map.setdefault(key, []).append(units)
                
    
#     # print("Service Map:", service_map)  # Debugging line to check the service map
    
#     # Now check for duplicates
#     for (cpt, modifier, pos, rendering_npi), units_list in service_map.items():
#         # print(f"Checking: CPT={cpt}, Modifier={modifier}, POS={pos}, NPI={rendering_npi}, Units={units_list}")  # Debugging line
        
#         # Check if there's more than 1 occurrence for the same service
#         if len(units_list) > 1:
#             total_units = sum(units_list)
#             error_message = (
#                 f"Duplicate charges for CPT {cpt} with modifier '{modifier}' and POS {pos}",
#                 f"Club {len(units_list)} duplicate charges into a single line with {total_units} units"
#             )
#             errors.append(error_message)
    
#     return errors





# def validate_asd_authorization_check(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
#     """Rule 19: Verify authorization before billing."""
#     errors = []
    
#     # This is a placeholder - actual implementation would need to check against an authorization database
#     # For now, we'll add a reminder to check manually
#     errors.append((
#         "Authorization verification required",
#         "Verify authorization is in place before billing per ASD Life rules"
#     ))
    
#     return errors

def validate_asd_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all ASD Life-specific validation rules."""
    return (
        validate_asd_telehealth_modifier_pos_02(claim) +
        validate_asd_bcbsil_97153_modifiers(claim) +
        validate_asd_tricare_97156_telehealth(claim) +
        validate_asd_telehealth_97155_non_billable(claim) +
        validate_asd_telehealth_97153_97155_overlap(claim) +
        validate_asd_96137_units(claim) +
        validate_asd_magellan_97153_modifier(claim) +
        validate_asd_optum_group_npi(claim)
        # validate_asd_duplicate_charges(claim)
        # validate_asd_authorization_check(claim)
    )