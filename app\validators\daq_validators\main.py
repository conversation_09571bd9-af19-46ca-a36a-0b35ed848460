import io
import logging
from fastapi import UploadFile, File, HTTPException
import pandas as pd
import json
import collections
import os
import sqlite3
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from dotenv import load_dotenv
from datetime import datetime, date
from typing import Dict
from fastapi.responses import JSONResponse

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Import your processing functions (clean_csv, process_all_groups, etc.)
from .processing import clean_csv, process_all_groups, collect_errors, detect_practice

# Load environment variables
load_dotenv()

SMTP_SERVER     = os.getenv("SMTP_SERVER", "")
SMTP_PORT       = int(os.getenv("SMTP_PORT", "587"))
SENDER_EMAIL    = os.getenv("SENDER_EMAIL", "")
SENDER_LOGIN    = os.getenv("SENDER_LOGIN", "")
SENDER_PASSWORD = os.getenv("SENDER_PASSWORD", "")
# Hard‐code or pull from .env; here we just keep a single recipient for testing
RECEIVER_EMAILS = ["<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>"]

# ────────────────
# Database helpers
# ────────────────
def get_db_connection():
    conn = sqlite3.connect("validation_counts.db")
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """Create tables if they do not already exist."""
    conn = get_db_connection()
    conn.execute("""
        CREATE TABLE IF NOT EXISTS daq_daily_counts (
            date TEXT PRIMARY KEY,
            count INTEGER
        )
    """)
    conn.execute("""
        CREATE TABLE IF NOT EXISTS daq_file_validation_stats (
            id              INTEGER PRIMARY KEY AUTOINCREMENT,
            filename        TEXT,
            practice_name   TEXT,
            validation_time DATETIME,
            total_errors    INTEGER,
            error_breakdown TEXT
        )
    """)
    conn.commit()
    conn.close()

def save_validation_stats(filename: str, practice_name: str, total_errors: int, error_breakdown: Dict[str, int]):
    """Insert a new row for this validated file into daq_file_validation_stats."""
    conn = get_db_connection()
    conn.execute("""
        INSERT INTO daq_file_validation_stats
            (filename, practice_name, validation_time, total_errors, error_breakdown)
        VALUES (?, ?, ?, ?, ?)
    """, (
        filename,
        practice_name,
        datetime.now().isoformat(),
        total_errors,
        json.dumps(dict(error_breakdown))
    ))
    conn.commit()
    conn.close()

def get_daily_stats() -> Dict:
    """
    Return a dict with:
      - total_validations (int)
      - file_stats: a list of { filename, practice_name, total_errors, error_breakdown }
    for "today" (local server date).
    """
    today_str = date.today().isoformat()
    conn = get_db_connection()

    # 1) fetch the daily count
    row = conn.execute(
        "SELECT count FROM daq_daily_counts WHERE date = ?", (today_str,)
    ).fetchone()
    total_validations = row["count"] if row else 0

    # 2) fetch each per‐file row whose validation_time is today
    file_rows = conn.execute("""
        SELECT filename, practice_name, total_errors, error_breakdown
          FROM daq_file_validation_stats
         WHERE DATE(validation_time) = DATE(?)
    """, (today_str,)).fetchall()
    conn.close()

    def _safe_load(s: str):
        if not s or s.strip() == "":
            return {}
        try:
            return json.loads(s)
        except json.JSONDecodeError:
            return {}

    files = []
    for r in file_rows:
        files.append({
            "filename": r["filename"],
            "practice_name": r["practice_name"],
            "total_errors": r["total_errors"],
            "error_breakdown": _safe_load(r["error_breakdown"])
        })

    return {
        "total_validations": total_validations,
        "file_stats": files
    }
   
def get_daily_count() -> int:
    """Return the number of files validated so far today."""
    today_str = date.today().isoformat()
    conn = get_db_connection()
    row = conn.execute(
        "SELECT count FROM daq_daily_counts WHERE date = ?", (today_str,)
    ).fetchone()
    conn.close()
    return row["count"] if row else 0

def increment_daily_count():
    """Increment today's validated-file count by 1 (or insert a new row if none exists)."""
    today_str = date.today().isoformat()
    conn = get_db_connection()
    row = conn.execute(
        "SELECT count FROM daq_daily_counts WHERE date = ?", (today_str,)
    ).fetchone()
    new_count = (row["count"] if row else 0) + 1
    conn.execute("""
        INSERT OR REPLACE INTO daq_daily_counts (date, count)
        VALUES (?, ?)
    """, (today_str, new_count))
    conn.commit()
    conn.close()

def reset_daily_count():
    """
    Delete any rows from daq_daily_counts older than today.
    This is run once at midnight (server local time).
    """
    today_str = date.today().isoformat()
    conn = get_db_connection()
    conn.execute("DELETE FROM daq_daily_counts WHERE date < ?", (today_str,))
    conn.commit()
    conn.close()

# ───────────────────────────────────────
# Email-reporting logic (runs once/day)
# ───────────────────────────────────────
def send_daily_report():
    """
    Compose and send an HTML + plain-text email of today's validation stats.
    """
    try:
        stats = get_daily_stats()
        today_str = date.today().isoformat()

        # Build plain-text fallback
        plain = f"""
Daily DAQ Validation Report - {today_str}
====================================
- Total files validated: {stats['total_validations']}
- Files with errors: {sum(1 for f in stats['file_stats'] if f['total_errors'] > 0)}
- Total errors detected: {sum(f['total_errors'] for f in stats['file_stats'])}

File Details:
"""
        for fs in stats["file_stats"]:
            plain += f"\nFile: {fs['filename']} (Practice: {fs['practice_name']})\n  - Errors: {fs['total_errors']}"
            for etype, cnt in fs["error_breakdown"].items():
                plain += f"\n    • {etype}: {cnt}"

        # aggregate error types
        agg = collections.defaultdict(int)
        for fs in stats["file_stats"]:
            for etype, cnt in fs["error_breakdown"].items():
                agg[etype] += cnt

        plain += "\n\nError Type Totals:\n------------------"
        for etype, cnt in sorted(agg.items(), key=lambda x: x[1], reverse=True):
            plain += f"\n- {etype}: {cnt}"

        plain += f"\n\nReport generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # Build HTML version
        html = f"""
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <style>
    body {{ font-family: Arial, sans-serif; color: #333; }}
    .container {{ max-width: 700px; margin: 0 auto; padding: 20px; }}
    h1 {{ border-bottom: 2px solid #eee; padding-bottom: 10px; }}
    table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
    th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
    th {{ background: #f4f4f4; }}
    .error-count {{ color: #c00; font-weight: bold; }}
    .footer {{ margin-top: 30px; font-size: 0.9em; color: #666; }}
  </style>
</head>
<body>
  <div class="container">
    <h1>Daily DAQ Validation Report – {today_str}</h1>
    <h2>Summary</h2>
    <table>
      <tr><th>Total Files Validated</th><td>{stats['total_validations']}</td></tr>
      <tr><th>Files with Errors</th><td>{sum(1 for f in stats['file_stats'] if f['total_errors'] > 0)}</td></tr>
      <tr><th>Total Errors Detected</th><td class="error-count">{sum(f['total_errors'] for f in stats['file_stats'])}</td></tr>
    </table>

    <h2>Per-File Details</h2>
    {"".join([
      f'''
      <details>
        <summary>{fs['filename']} (Practice: {fs['practice_name']}) ({fs['total_errors']} errors)</summary>
        <div>
          <p>Total Errors: <span class="error-count">{fs['total_errors']}</span></p>
          <table>
            <tr><th>Error Type</th><th>Count</th></tr>
            {"".join([f"<tr><td>{etype}</td><td>{cnt}</td></tr>" for etype, cnt in fs["error_breakdown"].items()])}
          </table>
        </div>
      </details>
      ''' for fs in stats["file_stats"]
    ])}

    <h2>Error Type Analysis</h2>
    <table>
      <tr><th>Error Type</th><th>Occurrences</th></tr>
      {"".join([f"<tr><td>{etype}</td><td>{cnt}</td></tr>" for etype, cnt in sorted(agg.items(), key=lambda x: x[1], reverse=True)])}
    </table>

    <div class="footer">
      Report generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    </div>
  </div>
</body>
</html>
"""

        # Compose the email
        msg = MIMEMultipart("alternative")
        msg["Subject"] = f"Daily DAQ Validation Report – {today_str}"
        msg["From"] = SENDER_EMAIL
        msg["To"] = ", ".join(RECEIVER_EMAILS)

        msg.attach(MIMEText(plain, "plain"))
        msg.attach(MIMEText(html, "html"))

        # Send over SMTP
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(SENDER_LOGIN, SENDER_PASSWORD)
            server.sendmail(SENDER_EMAIL, RECEIVER_EMAILS, msg.as_string())

        logger.info(f"[DAQ] Sent daily report for {today_str}")
    except Exception as e:
        logger.error(f"[DAQ/send_daily_report] failed: {e}")

# ─────────────────────────────────────────
# "Core" function to process one file blob
# ─────────────────────────────────────────
async def process_file(file: UploadFile = File(...)) -> Dict:
    """
    Read the uploaded file (CSV or XLSX), clean it, group it, run collect_errors(),
    and return a dict { "results": [...], "message": "..." }.
    Also: increment the daily counter + save stats in SQLite.
    """
    try:
        content = await file.read()
        filename = file.filename.lower()

        # Detect practice_type from first non-empty line or first cell
        if filename.endswith(".csv"):
            s = content.decode("utf-8")
            lines = s.splitlines()
            first_non_empty = next((ln for ln in lines if ln.strip()), "")
            practice_type = detect_practice(first_non_empty)
            content_str = s
        elif filename.endswith((".xlsx", ".xls")):
            df_raw = pd.read_excel(io.BytesIO(content), engine="openpyxl", header=None)
            first_cell = str(df_raw.iloc[0, 0])
            practice_type = detect_practice(first_cell)
            content_str = df_raw.to_csv(index=False)
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")

        # Clean and group
        cleaned = clean_csv(content_str)
        groups = {}
        current = None
        counter = 1
        for ln in cleaned:
            if not ln.strip():
                continue
            if "Encounter ID" in ln and not any(c.isdigit() for c in ln.split(",")[0].strip()):
                if current and groups.get(current):
                    counter += 1
                current = f"group_{counter}"
                groups[current] = [ln]
            elif current:
                groups[current].append(ln)

        combined_df = process_all_groups(groups)
        if combined_df.empty:
            return {"results": [], "message": "No valid encounter data found."}

        errors = collect_errors(combined_df, practice_type)
        logger.debug(f"Raw errors from collect_errors: {errors}")

        # (A) increment the sqlite-based daily counter
        increment_daily_count()

        # (B) build an error breakdown for this file (use "error" key)
        error_breakdown = collections.Counter(
            err.get("error", "Unknown") for err in errors
        )
        logger.debug(f"Error breakdown: {error_breakdown}")

        save_validation_stats(
            filename=file.filename,
            practice_name=practice_type,
            total_errors=len(errors),
            error_breakdown=error_breakdown
        )

        # Return a plain dict. The router will render it via Jinja2.
        return {
            "results": errors,
            "message": f"Processing complete. Found {len(errors)} issues."
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[DAQ/process_file] {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing file: {e}")

# ─────────────────────────────────────────
# APScheduler instance (shared by the unified app)
# ─────────────────────────────────────────
scheduler = BackgroundScheduler()
