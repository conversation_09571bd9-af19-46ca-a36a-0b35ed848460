from typing import List, Tuple, Dict
from .utils import map_segment
from datetime import datetime, timezone, timedelta
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
PROVIDER_NPI = "**********"  # Cristina Guerrero
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
MODIFIER_START_DATE = datetime(2022, 9, 15)  # From Optum update on 06/22/2022
TRICARE_97151_CUTOFF = datetime(2025, 4, 9)  # Current date as reference for 2-week rule
ANTHEM_MEDICAL_RATE_UPDATE_DATE = datetime(2025, 3, 1)  # From update on 04/03/2025
BLUESHIELD_EFFECTIVE_DATE = datetime(2025, 1, 1)  # From update on 03/25/2025
NAYSHA_INACTIVE_DATE = datetime(2025, 3, 4)  # From update on 03/04/2025
ALFI_INACTIVE_DATE = datetime(2025, 4, 11)  # From update on 04/11/2025

# Blueshield patients from 03/25/2025 update
BLUESHIELD_PATIENTS = [
    "MILLER, ELIZABETH",
    "KEFLEMARIAM, NAYEL",
    "HASEGAWA, ISAAC",
    "HASEGAWA, AVERY",
    "TOBER, TYLER"
]

# Anthem Medical rates from 04/03/2025 update
ANTHEM_MEDICAL_RATES = {
    "97151": 22.00,
    "97153": 12.38,
    "97155": 24.20,
    "97156": 24.20
}

def validate_able_kids_telehealth_pos_02_10(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 3: POS 02 and 10 require 95 modifier for all insurance."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            modifier = parts[2] if len(parts) > 2 else ""
            if pos in {"02", "10"} and "95" not in modifier:
                errors.append((
                    f"POS {pos} missing 95 modifier for CPT {parts[1]}",
                    "Add modifier 95 for POS 02 or 10 per Able Kids rules"
                ))
    return errors

def validate_able_kids_no_modifiers_cigna_anthem_medicaid_mt(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: No modifiers for Cigna, Anthem, Anthem Medical, Medicaid Montana."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_exempt = any(p in payer_name or p in payer_code for p in ["CIGNA", "ANTHEM", "MEDICAID MONTANA", "MEDICAID MT"])
    if not is_exempt:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) > 2 and parts[2]:
            errors.append((
                f"CPT {parts[1]} has modifier '{parts[2]}' for {payer_name or payer_code}",
                "Do not use modifiers for Cigna, Anthem, or Medicaid Montana per Able Kids rules"
            ))
    return errors

def validate_able_kids_taxonomy_partnership(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 8: Taxonomy required for Partnership insurance in 24J (individual) and 33b (group)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_partnership = any("PARTNERSHIP" in seg.get("Payer Name", "").upper() or "PARTNERSHIP" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_partnership:
        return []

    # Check individual taxonomy (24J) in PRV with PRV03
    ind_taxonomy = next((seg.get("Reference Identification") for seg in mapped_all if seg.get("Provider Code") == "PE" and seg.get("Reference Identification Qualifier") == "PXC"), None)
    if not ind_taxonomy:
        errors.append(("Missing individual taxonomy in PRV*PE for Partnership", "Add taxonomy code in PRV*PE*PXC for box 24J"))

    # Check group taxonomy (33b) in PRV with PRV03
    group_taxonomy = next((seg.get("Reference Identification") for seg in mapped_all if seg.get("Provider Code") == "BI" and seg.get("Reference Identification Qualifier") == "PXC"), None)
    if not group_taxonomy:
        errors.append(("Missing group taxonomy in PRV*BI for Partnership", "Add taxonomy code in PRV*BI*PXC for box 33b"))

    return errors

def validate_able_kids_magellan_uhc_umr_optum_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 11: Magellan, UHC, UMR, Optum require modifiers based on provider degree post-09/15/2022."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_applicable = any(p in payer_name or p in payer_code for p in ["MAGELLAN", "UHC", "UMR", "OPTUM"])
    if not is_applicable:
        return []

    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE

    if dos_date >= MODIFIER_START_DATE:
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2:
                modifier = parts[2] if len(parts) > 2 else ""
                if modifier not in {"HM", "HN", "HO", "HP"}:
                    errors.append((
                        f"CPT {parts[1]} on {dos} missing required modifier for {payer_name or payer_code}; found '{modifier}'",
                        "Use HM (RBT), HN (BCABA), HO (BCBA), or HP (doctoral) post-09/15/2022"
                    ))
    return errors

def validate_able_kids_partnership_h2019_h2014_hm(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 12: Partnership insurance requires HM modifier for H2019 and H2014."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_partnership = any("PARTNERSHIP" in seg.get("Payer Name", "").upper() or "PARTNERSHIP" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_partnership:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in {"H2019", "H2014"}:
            modifier = parts[2] if len(parts) > 2 else ""
            if modifier != "HM":
                errors.append((
                    f"Partnership CPT {parts[1]} missing HM modifier; found '{modifier}'",
                    "Use HM modifier for H2019 and H2014 per Partnership rules"
                ))
    return errors

def validate_able_kids_tricare_97151_two_weeks(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 16: TRICARE 97151 must be billed within a 2-week period."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_tricare = any("TRICARE" in seg.get("Payer Name", "").upper() or "TRICARE" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_tricare:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dates_97151 = []
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "97151":
            dos = next((s.get("Date Time Period") for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), None)
            if dos:
                try:
                    dos_date = datetime.strptime(dos, "%Y%m%d")
                    dates_97151.append(dos_date)
                except ValueError:
                    errors.append((f"Invalid DOS format for 97151: {dos}", "Use YYYYMMDD format"))

    if dates_97151:
        min_date = min(dates_97151)
        max_date = max(dates_97151)
        if (max_date - min_date).days > 14:
            errors.append((
                f"TRICARE 97151 spans {min_date.strftime('%Y%m%d')} to {max_date.strftime('%Y%m%d')} (>14 days)",
                "Bill 97151 within a 2-week period per TRICARE rules"
            ))
    return errors

def validate_able_kids_tricare_97155_97156_units(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 17: TRICARE 97155/97156 limited to 8 units per day."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_tricare = any("TRICARE" in seg.get("Payer Name", "").upper() or "TRICARE" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_tricare:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos_units = {}
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in {"97155", "97156"}:
            dos = next((s.get("Date Time Period") for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), "Unknown")
            try:
                units = float(seg.get("Units/Days", "").strip())
                dos_units.setdefault(dos, {}).setdefault(parts[1], 0)
                dos_units[dos][parts[1]] += units
            except ValueError:
                errors.append((f"Invalid Units/Days '{seg.get('Units/Days', '')}' for {parts[1]}", "Ensure valid numeric units"))

    for dos, cpt_units in dos_units.items():
        for cpt, units in cpt_units.items():
            if units > 8:
                errors.append((
                    f"TRICARE {cpt} exceeds 8 units on DOS {dos}; found {units}",
                    "Limit 97155/97156 to 8 units per day per TRICARE rules"
                ))
    return errors

def validate_able_kids_missing_pos(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 9: Check for missing POS and suggest checking motivity portal."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    
    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and not pos:
            errors.append((
                f"Missing POS for CPT {parts[1]}",
                "Check motivity portal for POS and update in billing screen before submitting"
            ))
    
    return errors

def validate_able_kids_0373t_duplicate_sessions(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 13: For CPT 0373T with duplicate timing, ignore one session (Aetna, Beacon)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_applicable = any(p in payer_name or p in payer_code for p in ["AETNA", "BEACON"])
    if not is_applicable:
        return []
    
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    cpt_0373t_providers = {}
    
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "0373T":
            time_slot = seg.get("Time", "Unknown")
            provider = seg.get("Rendering Provider Identifier", "Unknown")
            key = time_slot
            cpt_0373t_providers.setdefault(key, []).append(provider)
    
    for time_slot, providers in cpt_0373t_providers.items():
        if len(providers) > 1:
            errors.append((
                f"Multiple 0373T sessions at {time_slot} with providers {', '.join(providers)}",
                "For Aetna/Beacon, ignore one session and bill only one provider per time slot"
            ))
    
    return errors

def validate_able_kids_anthem_90889(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 14: Anthem does not cover 90889, use 97151 instead."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_anthem = any("ANTHEM" in p for p in [payer_name, payer_code])
    if not is_anthem:
        return []
    
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "90889":
            errors.append((
                "Anthem does not cover CPT 90889 for report writing",
                "Use 97151 instead per update on 06/21/2022"
            ))
    
    return errors

def validate_able_kids_naysha_inactive(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 15: Do not use Naysha for billing/auth post-03/04/2025."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= NAYSHA_INACTIVE_DATE:
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            provider_name = seg.get("Rendering Provider Name", "").upper()
            if "NAYSHA" in provider_name:
                errors.append((
                    f"Provider Naysha used on {dos} post-03/04/2025",
                    "Do not use Naysha for billing/auth per update on 03/04/2025"
                ))
    
    return errors

def validate_able_kids_anthem_medical_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 18: Anthem Medical rates updated effective 03/01/2025."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_anthem_medical = any("ANTHEM MEDICAL" in p for p in [payer_name, payer_code])
    if not is_anthem_medical:
        return []
    
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= ANTHEM_MEDICAL_RATE_UPDATE_DATE:
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] in ANTHEM_MEDICAL_RATES:
                cpt = parts[1]
                expected_rate = ANTHEM_MEDICAL_RATES[cpt]
                try:
                    charge = float(seg.get("Monetary Amount", "0").strip())
                    if abs(charge - expected_rate) > 0.01:  # Allow for small rounding differences
                        errors.append((
                            f"Anthem Medical CPT {cpt} on {dos} has rate ${charge:.2f}; expected ${expected_rate:.2f}",
                            f"Update to new rate effective 03/01/2025 per update on 04/03/2025"
                        ))
                except ValueError:
                    errors.append((f"Invalid charge amount '{seg.get('Monetary Amount', '')}' for {cpt}", "Ensure valid numeric charge"))
    
    return errors

def validate_able_kids_blueshield_patients(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 19: Specific patients belong to Blueshield effective 01/01/2025."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= BLUESHIELD_EFFECTIVE_DATE:
        patient_name = next((seg.get("Patient Name", "").upper() for seg in mapped_all if "Patient Name" in seg), "")
        payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
        
        for bs_patient in BLUESHIELD_PATIENTS:
            if bs_patient.upper() in patient_name and "BLUESHIELD" not in payer_name:
                errors.append((
                    f"Patient {patient_name} belongs to Blueshield effective 01/01/2025",
                    "Submit claim to Blueshield per update on 03/25/2025"
                ))
                break
    
    return errors

def validate_able_kids_alfi_inactive(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 20: Alexander Field is inactive post-04/11/2025."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= ALFI_INACTIVE_DATE:
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            provider_name = seg.get("Rendering Provider Name", "").upper()
            if "ALEXANDER FIELD" in provider_name or "ALEX FIELD" in provider_name:
                errors.append((
                    f"Provider Alexander Field used on {dos} post-04/11/2025",
                    "Alexander Field is now inactive per update on 04/11/2025"
                ))
    
    return errors

def validate_able_kids_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Able Kids-specific validation rules."""
    return (
        # validate_able_kids_weekly_billing(claim) +
        validate_able_kids_telehealth_pos_02_10(claim) +
        validate_able_kids_no_modifiers_cigna_anthem_medicaid_mt(claim) +
        # validate_able_kids_authorization_check(claim) +
        validate_able_kids_taxonomy_partnership(claim) +
        validate_able_kids_missing_pos(claim) +
        # validate_able_kids_scheduled_vs_rendered(claim) +
        validate_able_kids_magellan_uhc_umr_optum_modifiers(claim) +
        validate_able_kids_partnership_h2019_h2014_hm(claim) +
        validate_able_kids_0373t_duplicate_sessions(claim) +
        validate_able_kids_anthem_90889(claim) +
        validate_able_kids_naysha_inactive(claim) +
        validate_able_kids_tricare_97151_two_weeks(claim) +
        validate_able_kids_tricare_97155_97156_units(claim) +
        validate_able_kids_anthem_medical_rates(claim) +
        validate_able_kids_blueshield_patients(claim) +
        validate_able_kids_alfi_inactive(claim)
    )