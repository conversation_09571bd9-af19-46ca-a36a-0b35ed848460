from typing import List, <PERSON><PERSON>, Dict
from .utils import map_segment, has_secondary_insurance
import logging

# ABA CPT codes and their MUE limits (units per day, 15-minute increments)
ABA_CPT_MUE = {
    "97151": 8,   # Behavior identification assessment
    "97152": 8,   # Follow-up assessment
    "97153": 32,  # Direct behavior therapy
    "97154": 12,  # Group direct behavior therapy
    "97155": 24,  # Supervision
    "97156": 16,  # Parent training
    "97157": 16,  # Group parent training
    "97158": 16   # Social skills group
}

# Additional HCPCS codes (no MUE limits specified)
HCPCS_CODES = {"H2012", "H2019", "H2014", "H0046", "H0031", "H0032", "S5111", "H0004", "0373T", "H2012"}  # Added H2019, H0046, S5111, 0373T per changes
SPEECH_CPT_CODES = {"92507"}
PSYCHO_CPT_CODES = {"90791", "96130", "96131", "96136", "96137", "90837"}

# Valid modifiers by degree level and telehealth
VALID_MODIFIERS = {
    "RBT": "HM",    # Registered Behavior Technician
    "BT": "HM",     # Behavior Technician (assuming same as RBT)
    "BCABA": "HN",  # Board Certified Assistant Behavior Analyst
    "BCBA": "HO",   # Board Certified Behavior Analyst
    "DOCTORAL": "HP",  # Added for Optum/Able Kids
    "TELEHEALTH": {"GT", "95"}  # Telehealth modifiers
}

# Valid Place of Service (POS) codes
VALID_POS_CODES = {"02", "03", "10", "11", "12", "99"}

# File Structure Validation Rules
def check_file_not_empty(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks if the file contains any segments."""
    return [("File is empty", "Upload a valid 837 EDI file")] if not segments else []

def check_isa_segment(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks if the file starts with an ISA segment."""
    return [("File does not start with ISA segment", "Ensure the file begins with an ISA segment")] if not segments or not segments[0].startswith("ISA") else []

def check_iea_segment(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks if the file ends with an IEA segment."""
    return [("File does not end with IEA segment", "Ensure the file ends with an IEA segment")] if not segments or not segments[-1].startswith("IEA") else []

def check_gs_presence(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks for the presence of at least one GS segment."""
    gs_count = sum(1 for seg in segments if seg.startswith("GS"))
    return [("Missing GS segment", "Add a GS segment to the file")] if gs_count < 1 else []

def check_ge_presence(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks for the presence of at least one GE segment."""
    ge_count = sum(1 for seg in segments if seg.startswith("GE"))
    return [("Missing GE segment", "Add a GE segment to the file")] if ge_count < 1 else []

def check_gs_ge_balance(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks if the number of GS and GE segments match."""
    gs_count = sum(1 for seg in segments if seg.startswith("GS"))
    ge_count = sum(1 for seg in segments if seg.startswith("GE"))
    return [(f"Mismatch in GS and GE counts: {gs_count} GS, {ge_count} GE", "Ensure each GS segment has a corresponding GE segment")] if gs_count != ge_count else []

def check_st_presence(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks for the presence of at least one ST segment."""
    st_count = sum(1 for seg in segments if seg.startswith("ST"))
    return [("Missing ST segment", "Add an ST segment to the file")] if st_count < 1 else []

def check_se_presence(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks for the presence of at least one SE segment."""
    se_count = sum(1 for seg in segments if seg.startswith("SE"))
    return [("Missing SE segment", "Add an SE segment to the file")] if se_count < 1 else []

def check_st_se_balance(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks if the number of ST and SE segments match."""
    st_count = sum(1 for seg in segments if seg.startswith("ST"))
    se_count = sum(1 for seg in segments if seg.startswith("SE"))
    return [(f"Mismatch in ST and SE counts: {st_count} ST, {se_count} SE", "Ensure each ST segment has a corresponding SE segment")] if st_count != se_count else []

def check_bht_presence(segments: List[str]) -> List[Tuple[str, str]]:
    """Checks for the presence of a BHT segment."""
    return [("Missing BHT segment", "Add a BHT segment to the file")] if not any(seg.startswith("BHT") for seg in segments) else []

def validate_file_structure(segments: List[str]) -> List[Tuple[str, str]]:
    """Validates the overall structure of the EDI file by combining all file structure rules."""
    return (
        check_file_not_empty(segments) +
        check_isa_segment(segments) +
        check_iea_segment(segments) +
        check_gs_presence(segments) +
        check_ge_presence(segments) +
        check_gs_ge_balance(segments) +
        check_st_presence(segments) +
        check_se_presence(segments) +
        check_st_se_balance(segments) +
        check_bht_presence(segments)
    )

# Claim Group Validation Rules
def check_clm_segment(mapped_claim_segments: List[Dict[str, str]]) -> List[Tuple[str, str]]:
    """Checks for the presence of a CLM segment in the claim."""
    return [("Missing CLM segment", "Add a CLM segment to the claim with all required elements")] if not any("Claim Submitter's Identifier" in seg for seg in mapped_claim_segments) else []

def check_service_lines_presence(mapped_claim_segments: List[Dict[str, str]]) -> List[Tuple[str, str]]:
    """Checks for the presence of at least one SV1 segment."""
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
    return [("Missing service lines (SV1 segment)", "Add at least one SV1 segment for the claim")] if not service_lines else []

def check_service_line_format(service_lines: List[Dict[str, str]]) -> List[Tuple[str, str]]:
    """Checks if each SV1 segment has a valid Service Identification format."""
    errors = []
    for seg in service_lines:
        service_ident = seg["Service Identification"]
        parts = service_ident.split(":")
        if len(parts) < 2:  # Adjusted to allow HCPCS without modifier
            errors.append((f"Invalid format: '{service_ident}'", "Ensure Service Identification has at least two elements separated by ':'"))
    return errors

def check_valid_cpt_codes(service_lines: List[Dict[str, str]], claim: Dict[str, List[str]] = None) -> List[Tuple[str, str]]:
    """Validates that codes in SV1 segments are valid ABA, Speech, or Psycho CPT/HCPCS codes for Phos Inc."""
    errors = []
    # Detect if this is a Phos Inc claim (by group NPI)
    # is_phos_inc = False
    # if claim:
        # mapped_billing = [map_segment(seg) for seg in claim.get("billing_provider", [])]
        # is_phos_inc = any(seg.get("Identification Code") == "**********" for seg in mapped_billing if "Identification Code" in seg)
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            code = parts[1]
            # Allow Speech and Psycho codes for Phos Inc
            if (
                code not in ABA_CPT_MUE
                and code not in HCPCS_CODES
                and code not in SPEECH_CPT_CODES
                and code not in PSYCHO_CPT_CODES
            ):
                errors.append((
                    f"Invalid code '{code}'",
                    "Use Category I CPT codes 97151-97158, valid HCPCS codes (H2012, H2019, etc.), Speech (92507), or Psycho codes for ABA services"
                ))
            # else:
            #     if code not in ABA_CPT_MUE and code not in HCPCS_CODES:
            #         errors.append((
            #             f"Invalid code '{code}'",
            #             "Use Category I CPT codes 97151-97158 or valid HCPCS codes (H2012, H2019, etc.) for ABA services"
            #         ))
    return errors

def check_mue_limits(service_lines: List[Dict[str, str]]) -> List[Tuple[str, str]]:  # Rule 15
    """Validates that units in SV1 segments do not exceed MUE limits for CPT codes."""
    errors = []
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt_code = parts[1]
            if cpt_code in ABA_CPT_MUE:  # HCPCS codes have no MUE limits
                try:
                    units = float(seg.get("Units/Days", "").strip())
                    if units > ABA_CPT_MUE[cpt_code]:
                        errors.append((f"CPT {cpt_code} exceeds MUE limit of {ABA_CPT_MUE[cpt_code]} units/day; found {units}", f"Reduce units to {ABA_CPT_MUE[cpt_code]} or less"))
                except ValueError:
                    errors.append((f"Invalid Units/Days '{seg.get('Units/Days', '')}' for '{seg['Service Identification']}'", "Ensure Units/Days is a valid number"))
    return errors

def check_face_to_face_time(service_lines: List[Dict[str, str]], mapped_claim_segments: List[Dict[str, str]]) -> List[Tuple[str, str]]:  # Rule 3, 14
    """Checks if units meet the minimum 8-minute face-to-face time requirement per 15-minute unit."""
    errors = []
    # Extract date/time ranges from DTP segments (simplified, assumes one DTP per claim)
    dtp_seg = next((seg for seg in mapped_claim_segments if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    if dtp_seg and dtp_seg.get("Date Time Period Format Qualifier") == "RD8":
        try:
            start_date, end_date = dtp_seg["Date Time Period"].split("-")
            start = int(start_date[-4:])  # HHMM format
            end = int(end_date[-4:])
            duration_minutes = ((end // 100 * 60 + end % 100) - (start // 100 * 60 + start % 100))
            if duration_minutes < 0:
                duration_minutes += 1440  # Overnight adjustment
        except (ValueError, IndexError):
            duration_minutes = None
    else:
        duration_minutes = None

    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            code = parts[1]
            if code in ABA_CPT_MUE or code in HCPCS_CODES:
                try:
                    units = float(seg.get("Units/Days", "").strip())
                    if duration_minutes:
                        expected_units = duration_minutes // 15
                        if units > expected_units:
                            errors.append((f"Code {code} units ({units}) exceed calculated units ({expected_units}) based on {duration_minutes} minutes", "Adjust units to match session duration (15 min/unit)"))
                        elif units * 8 > duration_minutes:
                            errors.append((f"Code {code} with {units} units requires at least {units * 8} minutes; found {duration_minutes}", "Ensure each 15-minute unit has at least 8 minutes of face-to-face time"))
                    elif units > 0 and units * 15 < 8:  # Fallback if no DTP
                        errors.append((f"Code {code} with {units} units does not meet 8-minute minimum per unit", "Ensure each 15-minute unit includes at least 8 minutes of face-to-face time"))
                except ValueError:
                    pass  # Handled in check_mue_limits
    return errors

def check_modifiers(service_lines: List[Dict[str, str]]) -> List[Tuple[str, str]]:  # Rule 16
    """Validates modifiers in SV1 segments."""
    errors = []
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            code = parts[1]
            modifier = parts[2] if len(parts) > 2 else ""
            if modifier:
                if modifier not in VALID_MODIFIERS["RBT"] and modifier not in VALID_MODIFIERS["BT"] and modifier not in VALID_MODIFIERS["BCABA"] and modifier not in VALID_MODIFIERS["BCBA"] and modifier not in VALID_MODIFIERS["TELEHEALTH"]:
                    errors.append((f"Invalid modifier '{modifier}' for code {code}", "Use HM (RBT/BT), HN (BCABA), HO (BCBA), GT/95 (telehealth) modifiers"))
            # Remove CPT modifier requirement except for 97153
            elif code == "97153":  # Only 97153 requires modifier
                errors.append((f"Missing modifier for CPT {code}", "Add appropriate modifier: HM (RBT/BT), HN (BCABA), or HO (BCBA)"))
    return errors

def check_concurrent_billing(claim: Dict[str, List[str]], service_lines: List[Dict[str, str]]) -> List[Tuple[str, str]]:  # Rule 2
    """Checks for concurrent billing of direct therapy and supervision."""
    # Remove concurrent billing restriction - different CPT codes can overlap
    return []

def check_parent_training_overlap(claim: Dict[str, List[str]], service_lines: List[Dict[str, str]]) -> List[Tuple[str, str]]:  # Rule 13
    """Checks for overlap between 97156 and 97153/97154 for TRICARE claims."""
    # Only check TRICARE claims
    if not any(seg.get("Payer Name", "").upper().startswith("TRICARE") for seg in [map_segment(s) for s in claim["subscriber"]]):
        return []

    parent_code = {"97156"}
    direct_codes = {"97153", "97154"}
    cpt_codes = {seg["Service Identification"].split(":")[1] for seg in service_lines if "Service Identification" in seg}
    
    if cpt_codes & parent_code and cpt_codes & direct_codes:
        # Check for HS modifier on 97156
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] == "97156":
                modifier = parts[2] if len(parts) > 2 else ""
                if "HS" not in modifier:
                    return [("TRICARE: 97156 overlapping with 97153/97154 missing HS modifier", 
                            "For TRICARE claims, add HS modifier to 97156 when overlapping with direct therapy")]
    return []

def check_97153_xs_modifier(service_lines: List[Dict[str, str]], mapped_claim_segments: List[Dict[str, str]], group_npi: str) -> List[Tuple[str, str]]:
    """
    Checks that multiple 97153 lines on same DOS have exactly one XS modifier.
    Special rule for Minds in Motion (NPI **********): if same DOS, CPT, POS but different modifier, do not club.
    Skip for Behavioral Focus LLC (NPI **********) if insurance is TRIWEST.
    """
    errors = []
    dos_cpt_lines = {}

    # Set up logger
    logger = logging.getLogger("aba_validators.general_validator")
    logger.debug("Starting 97153 XS modifier check")

    # 🚫 Skip validation entirely if it's Minds in Motion
    if group_npi == "**********" or group_npi == "**********":
        logger.debug("Skipping 97153 XS modifier check due to Minds in Motion NPI")
        return []

    # 🚫 Skip validation for Behavioral Focus LLC if insurance is TRIWEST
    if group_npi == "**********":
        payer_name = next((seg.get("Name Last or Organization Name", "").upper() for seg in mapped_claim_segments if seg.get("Entity Identifier Code") == "PR"), "")
        if "TRIWEST" in payer_name:
            logger.debug("Skipping 97153 XS modifier check due to Behavioral Focus LLC and TRIWEST insurance")
            return []

    # For each SV1, get the following DTP segment (forward mapping)
    for idx, seg in enumerate(mapped_claim_segments):
        if "Service Identification" not in seg:
            continue

        parts = seg.get("Service Identification", "").split(":")
        if len(parts) < 2 or parts[1] != "97153":
            continue

        # Look for the next DTP segment after this SV1
        dos = None
        for j in range(idx + 1, len(mapped_claim_segments)):
            dtp_seg = mapped_claim_segments[j]
            if dtp_seg.get("Date/Time Qualifier") == "472" and dtp_seg.get("Date Time Period Format Qualifier") == "D8":
                dos = dtp_seg.get("Date Time Period")
                break
            if "Service Identification" in dtp_seg:
                break

        logger.debug(f"SV1 idx={idx}, CPT=97153, mapped DOS={dos}")
        if not dos:
            continue

        pos = seg.get("Place of Service", "")
        modifier = parts[2] if len(parts) > 2 else ""
        key = (dos, "97153", pos)
        dos_cpt_lines.setdefault(key, []).append(seg)

    # Log the grouping
    for (dos, _, pos), lines in dos_cpt_lines.items():
        logger.debug(f"Grouped DOS={dos}, CPT=97153, POS={pos}, count={len(lines)}")

    # Check XS modifier count for 97153
    for (dos, _, pos), lines in dos_cpt_lines.items():
        if len(lines) > 1:
            xs_count = sum(
                1 for seg in lines
                if len(seg.get("Service Identification", "").split(":")) > 2
                and seg.get("Service Identification").split(":")[2] == "XS"
            )
            logger.debug(f"Checking XS for DOS={dos}, POS={pos}: {len(lines)} lines, {xs_count} with XS")
            if xs_count != 1:
                errors.append((
                    f"Multiple lines for CPT 97153 on DOS {dos}: {len(lines)} lines, {xs_count} with XS",
                    "Ensure exactly one line has XS modifier for same DOS and CPT"
                ))

    return errors

def check_duplicate_cpt_billing(service_lines: List[Dict[str, str]], mapped_claim_segments: List[Dict[str, str]]) -> List[Tuple[str, str]]:
    """
    Checks for duplicate billing of non-97153 CPT codes on same date, POS, and modifier.
    If same DOS, same CPT, same POS but different modifiers, do NOT treat as duplicate.
    """
    errors = []
    dos_cpt_pos_mod_lines = {}

    # Find index of each SV1 segment
    sv1_indices = [i for i, seg in enumerate(mapped_claim_segments) if "Service Identification" in seg]

    # For each SV1, get the following DTP segment
    for sv1_idx in sv1_indices:
        sv1_seg = mapped_claim_segments[sv1_idx]

        # Find DTP segment that follows this SV1
        dtp_seg = None
        for i in range(sv1_idx + 1, len(mapped_claim_segments)):
            seg = mapped_claim_segments[i]
            if seg.get("Date/Time Qualifier") == "472" and seg.get("Date Time Period Format Qualifier") == "D8":
                dtp_seg = seg
                break
            if "Service Identification" in seg:
                break  # Stop if next SV1 starts

        if not dtp_seg:
            continue

        dos = dtp_seg.get("Date Time Period")
        if not dos:
            continue

        # Extract CPT, modifier, and POS
        parts = sv1_seg.get("Service Identification", "").split(":")
        if len(parts) < 2 or parts[1] == "97153":
            continue  # skip 97153

        cpt = parts[1]
        modifier = parts[2] if len(parts) > 2 else ""
        pos = sv1_seg.get("Place of Service", "")

        key = (dos, cpt, pos, modifier)
        dos_cpt_pos_mod_lines.setdefault(key, []).append(sv1_seg)

    # Check for duplicates per key (same DOS, CPT, POS, and modifier)
    for (dos, cpt, pos, modifier), lines in dos_cpt_pos_mod_lines.items():
        if len(lines) > 1:
            errors.append((
                f"Duplicate billing detected for CPT {cpt} on {dos} at POS {pos} with modifier '{modifier}'",
                "Ensure each CPT code with the same modifier is billed only once per date of service"
            ))

    return errors


def check_provider_info(mapped_all: List[Dict[str, str]]) -> List[Tuple[str, str]]:
    """Checks for valid provider information (NM1 with 82 or 85)."""
    errors = []
    provider_found = False
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") in ["82", "85"]:  # Rendering or billing provider
            id_code = seg.get("Identification Code", "").strip()
            if id_code:
                provider_found = True
                break
            else:
                errors.append((f"Provider NM1*{seg.get('Entity Identifier Code')} missing valid Identification Code", "Ensure the provider NM1 segment has a valid NPI"))
    if not provider_found and not errors:
        errors.append(("Missing provider information (NM1 with 82 or 85)", "Add a provider NM1 segment with a valid NPI"))
    return errors

def check_date_of_service(mapped_claim_segments: List[Dict[str, str]]) -> List[Tuple[str, str]]:  # Rule 14 (partial)
    """Checks for a valid DTP segment with a date."""
    dtp_found = any("Date Time Period" in seg and seg["Date Time Period"].strip() for seg in mapped_claim_segments)
    return [("Missing DTP segment with a valid date", "Add a DTP segment with a valid date in the claim")] if not dtp_found else []

def check_telehealth(service_lines: List[Dict[str, str]], group_npi: str = None, insurance_name: str = None) -> List[Tuple[str, str]]:  # Rule 6
    """Checks telehealth services for POS 02/10 with GT/95 modifiers, with exception for Embracing Autism (NPI **********) and KAISER insurance."""
    errors = []
    skip_gt_95 = False
    if group_npi == "**********" and insurance_name and insurance_name.upper().startswith("KAISER"):
        skip_gt_95 = True
    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            # Check all modifier fields (parts[2], parts[3], parts[4], ...)
            modifiers = set(parts[2:6])  # up to 4 modifiers, adjust as needed
            if pos in {"02", "10"} and not skip_gt_95 and not (modifiers & VALID_MODIFIERS["TELEHEALTH"]):
                errors.append((f"Telehealth POS {pos} missing GT/95 modifier", "Add GT or 95 modifier for telehealth services with POS 02 or 10"))
    return errors

def check_sbr_sequence(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:  # Rule 8
    """Checks Coordination of Benefits (COB) sequence in SBR segments."""
    mapped_subscriber = [map_segment(seg) for seg in claim["subscriber"]]
    sbr_sequences = [seg.get("Payer Responsibility Sequence Number Code", "") for seg in mapped_subscriber if "Payer Responsibility Sequence Number Code" in seg]
    if not sbr_sequences:
        return [("Missing SBR segment", "Add an SBR segment to indicate payer responsibility")]
    if "P" not in sbr_sequences:
        return [("Missing primary payer (SBR*P)", "Ensure at least one SBR segment indicates primary payer with 'P'")]
    valid_order = ["P", "S", "T", "A"]  # Primary, Secondary, Tertiary, Additional
    last_idx = -1
    for seq in sbr_sequences:
        if seq in valid_order:
            curr_idx = valid_order.index(seq)
            if curr_idx <= last_idx:
                return [(f"Invalid SBR sequence: {sbr_sequences}", "Ensure SBR sequence follows P, S, T, A order")]
            last_idx = curr_idx
    return []

def check_authorization(mapped_all: List[Dict[str, str]]) -> List[Tuple[str, str]]:  # Rule 10
    """Checks for pre-authorization number in REF*G1 segment."""
    auth_found = any(seg.get("Reference Identification Qualifier") == "G1" and seg.get("Reference Identification", "").strip() for seg in mapped_all)
    return [("Missing authorization number (REF*G1)", "Add a REF*G1 segment with a valid authorization number")] if not auth_found else []

def check_diagnosis_code(mapped_all: List[Dict[str, str]]) -> List[Tuple[str, str]]:  # Rule 18
    """Checks for the presence of a diagnosis code."""
    diag_found = any("Diagnosis Code" in seg and seg["Diagnosis Code"].strip() for seg in mapped_all)
    return [("Missing diagnosis code", "Add an appropriate ICD-10 code (e.g., F84.0) in the HI segment")] if not diag_found else []

def check_place_of_service(service_lines: List[Dict[str, str]]) -> List[Tuple[str, str]]:  # Rule 19
    """Checks for valid Place of Service (POS) codes."""
    errors = []
    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        if pos and pos not in VALID_POS_CODES:
            errors.append((f"Invalid POS code '{pos}'", "Use valid POS codes: 02, 03, 10, 11, 12, 99"))
    return errors

def validate_claim_group(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validates general ABA billing rules for a claim group by combining all claim rules."""
    
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    # Extract Group NPI (Entity Identifier Code == '85')
    group_npi = next((seg.get("Identification Code", "") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
    # Extract insurance name (Entity Identifier Code == 'PR')
    insurance_name = next((seg.get("Name Last or Organization Name", "") for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"), None)

    # Example: pass group_npi and insurance_name to rule functions that need it (update those functions as needed)
    return (
        check_clm_segment(mapped_claim_segments) +
        check_service_lines_presence(mapped_claim_segments) +
        check_service_line_format(service_lines) +
        check_valid_cpt_codes(service_lines, claim) +  # Rule 1, 20
        check_mue_limits(service_lines) +       # Rule 15
        check_face_to_face_time(service_lines, mapped_claim_segments) +  # Rule 3, 14
        # check_modifiers(service_lines) +        # Rule 16 (updated)
        check_provider_info(mapped_all) +
        check_date_of_service(mapped_claim_segments) +  # Rule 14 (partial)
        check_concurrent_billing(claim, service_lines) +  # Rule 2 (updated)
        check_telehealth(service_lines, group_npi, insurance_name) +       # Rule 6 (updated)
        check_sbr_sequence(claim) +             # Rule 8
        # check_authorization(mapped_all) +       # Rule 10
        check_duplicate_cpt_billing(service_lines, mapped_claim_segments) +  # Rule 12 (updated)
        check_97153_xs_modifier(service_lines, mapped_claim_segments, group_npi) +  # Example of passing group_npi
        check_parent_training_overlap(claim, service_lines) +  # Rule 13 (updated)
        check_diagnosis_code(mapped_all) +      # Rule 18
        check_place_of_service(service_lines)   # Rule 19
    )