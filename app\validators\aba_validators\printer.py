import os
from datetime import datetime

def print_python_files():
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Create output filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(current_dir, f"code_snapshot.txt")
    
    # Get all .py files in the directory
    python_files = [f for f in os.listdir(current_dir) if f.endswith('.py')]
    
    with open(output_file, 'w', encoding='utf-8') as outfile:
        outfile.write(f"Code Snapshot - Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        outfile.write("=" * 80 + "\n\n")
        
        for py_file in sorted(python_files):
            file_path = os.path.join(current_dir, py_file)
            outfile.write(f"File: {py_file}\n")
            outfile.write("-" * 80 + "\n\n")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as infile:
                    content = infile.read()
                    outfile.write(content)
                    outfile.write("\n\n" + "=" * 80 + "\n\n")
            except Exception as e:
                outfile.write(f"Error reading file: {str(e)}\n\n")
    
    print(f"Code snapshot has been written to: {output_file}")

if __name__ == "__main__":
    print_python_files()