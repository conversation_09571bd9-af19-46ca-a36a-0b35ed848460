import pandas as pd

def rule1(row: pd.Series) -> dict:
    # If CPT is one of 00731, 00811, 00812, modifier must include "AA"
    if row['Proc'] in {"00731", "00811", "00812"}:
        if "AA" not in str(row['Mod']):
            return {
                "error": "Missing AA modifier",
                "correction": "Add AA modifier"
            }
    return {}

def rule2(row: pd.Series) -> dict:
    # If CPT is 00811 or 00812 and insurance is NJ Medicare,
    # then modifier should include both "QS" and "AA"
    if row['Proc'] in {"00811", "00812"} and "nj medicare" in row['Insurance'].lower():
        mod = str(row['Mod'])
        if "QS" not in mod or "AA" not in mod:
            return {
                "error": "Modifier QS & AA required for NJ Medicare",
                "correction": "Add modifiers QS and AA"
            }
    return {}

def rule3(row: pd.Series) -> dict:
    # For CPT 00812, the Dx (or another field) should indicate "DX Z12.11"
    if row['Proc'] == "00812":
        # Here we assume the diagnosis field should contain "Z12.11"
        if "z12.11" not in row['Dx'].lower():
            return {
                "error": "DX Z12.11 missing for CPT 00812",
                "correction": "Add DX Z12.11"
            }
    return {}
    