# Python specific
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd
*.so
.python-version

# Virtual environments
venv/
ENV/
env/
.env/
venv.bak/
.env.bak/

# IDE specific
.vscode/
.idea/
*.swp
*.swo
*.sublime-workspace
*.sublime-project

# Log files
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Project specific ignores
output.txt
*.db
*.sqlite
*.sqlite3

# Testing
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/
.pytest_cache/

# FastAPI specific
instance/
.webassets-cache

# Byte-compiled / optimized / DLL files
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Specific to your project structure
app/routers/__pycache__/
app/validators/__pycache__/
app/validators/aba_validators/__pycache__/
app/validators/daq_validators/__pycache__/
app/__pycache__/

# Local development
local_settings.py
settings.ini
.env.local
.env.development
.env.test
.env.production

read_and_save.py
.env