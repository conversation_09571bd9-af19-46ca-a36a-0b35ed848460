from typing import List, Tuple, Dict
from ..utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
PROVIDER_NPI = "**********"  # Jessica (BCBA) - using Group NPI as specified
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)

# Rate constants
MAGNOLIA_RATES = {
    "97151": 50.00,
    "97155": 50.00,
    "97156": 50.00,
    "97153": 35.00
}

# Important dates
ALLIANCE_AUTH_CUTOFF = datetime(2025, 1, 31)  # No auth required until this date
TELEHEALTH_UPDATE_DATE = datetime(2025, 2, 5)  # MCO telehealth billing update
ALLIANCE_MODIFIER_UPDATE_DATE = datetime(2025, 4, 16)  # Alliance 95 modifier update

# Healthy Blue NC taxonomy codes
HEALTHY_BLUE_GROUP_TAXONOMY = "251S00000X"
HEALTHY_BLUE_INDIVIDUAL_TAXONOMY = "103K00000X"

# MCO plans that require GT modifier for telehealth
MCO_PLANS = ["VAYA", "ALLIANCE", "HEALTHYBLUE", "TRILLIUM", "WELLCARE", "CAROLINA COMPLETE"]

# Patients with specific auth requirements
NO_AUTH_97151_PATIENTS = [
    "MASON SWIGGET",
    "ZURI AHAVA", 
    "CALEN PERSON"
]

def validate_magnolia_jessica_bcba_billing(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: All claims must be billed under Jessica (BCBA) using Group NPI."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Check billing provider NPI
    billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
    if billing_npi != GROUP_NPI:
        errors.append((
            f"Billing NPI {billing_npi} should be Jessica's Group NPI {GROUP_NPI}",
            f"Use Jessica's Group NPI {GROUP_NPI} for all Magnolia Milestone claims"
        ))
    
    # Check service line rendering provider
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        rendering_npi = seg.get("Rendering Provider Identifier", None)
        if rendering_npi and rendering_npi != GROUP_NPI:
            errors.append((
                f"Service line uses Rendering NPI {rendering_npi} instead of Jessica's Group NPI {GROUP_NPI}",
                f"Use Jessica's Group NPI {GROUP_NPI} for all service lines"
            ))
    
    return errors





def validate_magnolia_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validate CPT codes have correct per-unit rate as per Magnolia Milestone rate schedule."""
    errors = []
    segments = claim.get("claim_segments", [])
    # print(f"Total claim segments: {len(segments)}")
    mapped_segments = [map_segment(seg) for seg in segments if seg]
    # print(f"Total mapped segments: {len(mapped_segments)}")
    service_lines = [seg for seg in mapped_segments if seg and "Service Identification" in seg]
    # print(f"Service lines found: {len(service_lines)}")

    for seg in service_lines:
        service_id = seg.get("Service Identification", "")
        # print(f"\nProcessing service line: {service_id}")
        parts = service_id.split(":")
        # print(f"Parts extracted: {parts}")

         # Skip if not enough parts
        if len(parts) < 2:
            continue

        cpt = parts[1].strip()
        # print(f"Extracted CPT code: '{cpt}'")
        # modifiers = [p.strip() for p in parts[2:]]
        # print(f"Extracted modifiers: {modifiers}")
        # modifier = modifiers[0] if modifiers else ""
        # print(f"Using modifier: '{modifier}'")
        
        expected_rate = MAGNOLIA_RATES.get(cpt)
        # print(f"Expected rate for CPT {cpt}: ${expected_rate}")

        total_charge_str = seg.get("Monetary Amount", "").strip()
        # print(f"Total charge string: '{total_charge_str}'")
        units_str = seg.get("Units/Days", "").strip()
        # print(f"Units string: '{units_str}'")

        total_charge = float(total_charge_str)
        # print(f"Total charge converted to float: {total_charge}")
        units = float(units_str)
        # print(f"Units converted to float: {units}")

        actual_rate = round(total_charge / units, 2)
        # print(f"Actual rate calculated: ${actual_rate}")
        if actual_rate != expected_rate:
            errors.append((
                f"Incorrect rate for CPT {cpt}: charged ${actual_rate}/unit, expected ${expected_rate}/unit",
                f"Set per-unit charge for CPT {cpt} to ${expected_rate}"
            ))

    return errors

def validate_magnolia_healthy_blue_taxonomy(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 5: Healthy Blue NC requires group and individual taxonomy codes."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_healthy_blue = any("HEALTHY BLUE" in p or "HEALTHYBLUE" in p for p in [payer_name, payer_code])
    if not is_healthy_blue:
        return []
    
    # Check group taxonomy (33b) in PRV with PRV03
    group_taxonomy = next((seg.get("Reference Identification") for seg in mapped_all 
                          if seg.get("Provider Code") == "BI" and seg.get("Reference Identification Qualifier") == "PXC"), None)
    if group_taxonomy != HEALTHY_BLUE_GROUP_TAXONOMY:
        errors.append((
            f"Healthy Blue missing/incorrect group taxonomy; found '{group_taxonomy}', expected '{HEALTHY_BLUE_GROUP_TAXONOMY}'",
            f"Add group taxonomy {HEALTHY_BLUE_GROUP_TAXONOMY} in PRV*BI*PXC for Healthy Blue NC"
        ))
    
    # Check individual taxonomy (24J) in PRV with PRV03
    ind_taxonomy = next((seg.get("Reference Identification") for seg in mapped_all 
                        if seg.get("Provider Code") == "PE" and seg.get("Reference Identification Qualifier") == "PXC"), None)
    if ind_taxonomy != HEALTHY_BLUE_INDIVIDUAL_TAXONOMY:
        errors.append((
            f"Healthy Blue missing/incorrect individual taxonomy; found '{ind_taxonomy}', expected '{HEALTHY_BLUE_INDIVIDUAL_TAXONOMY}'",
            f"Add individual taxonomy {HEALTHY_BLUE_INDIVIDUAL_TAXONOMY} in PRV*PE*PXC for Healthy Blue NC"
        ))
    
    return errors


def validate_magnolia_optum_ssn_billing(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 6: Optum requires SSN billing instead of Tax ID (when implemented)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_optum = any("OPTUM" in p for p in [payer_name, payer_code])
    if is_optum:
        # This is a future implementation note
        errors.append((
            "Optum claim detected - verify SSN billing setup",
            "Optum requires individual SSN billing instead of Tax ID - confirm setup when billing details available"
        ))
    
    return errors


def validate_magnolia_trillium_gt_modifier_group_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 9: Trillium requires GT modifier for telehealth and Group NPI in 24J and 33a."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_trillium = any("TRILLIUM" in p for p in [payer_name, payer_code])
    if not is_trillium:
        return []
    
    # Check Group NPI in billing provider (33a)
    billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
    if billing_npi != GROUP_NPI:
        errors.append((
            f"Trillium billing NPI {billing_npi} should be Group NPI {GROUP_NPI}",
            f"Use Group NPI {GROUP_NPI} in Box 33a for Trillium"
        ))
    
    # Check Group NPI in service lines (24J)
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        rendering_npi = seg.get("Rendering Provider Identifier", None)
        if rendering_npi and rendering_npi != GROUP_NPI:
            errors.append((
                f"Trillium service line NPI {rendering_npi} should be Group NPI {GROUP_NPI}",
                f"Use Group NPI {GROUP_NPI} in Box 24J for Trillium"
            ))
        
        # Check GT modifier for telehealth
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            pos = seg.get("Place of Service", "").strip()
            modifier = parts[2] if len(parts) > 2 else ""
            
            # Assuming telehealth based on common POS codes
            if pos in {"11", "12", "03"} and "GT" not in modifier:
                errors.append((
                    f"Trillium telehealth CPT {parts[1]} missing GT modifier",
                    "Use GT modifier for Trillium telehealth sessions"
                ))
    
    return errors



def validate_magnolia_alliance_no_auth_until_cutoff(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 11: Alliance does not require auth until 01/31/2025."""
    # This is more of an informational rule for billing staff
    # We'll include it as a note for claims after the cutoff date
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_alliance = any("ALLIANCE" in p for p in [payer_name, payer_code])
    if not is_alliance:
        return []
    
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date > ALLIANCE_AUTH_CUTOFF:
        errors.append((
            f"Alliance claim on {dos} may require authorization after 01/31/2025",
            "Verify authorization requirements for Alliance claims post-01/31/2025"
        ))
    
    return errors




def validate_magnolia_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Magnolia Milestone-specific validation rules."""
    return (
        validate_magnolia_jessica_bcba_billing(claim) +
        validate_magnolia_rates(claim) +
        validate_magnolia_healthy_blue_taxonomy(claim) +
        validate_magnolia_optum_ssn_billing(claim) +
        validate_magnolia_trillium_gt_modifier_group_npi(claim) +
        validate_magnolia_alliance_no_auth_until_cutoff(claim)
    )