from typing import List, <PERSON><PERSON>, Dict
from ..utils import map_segment
from zoneinfo import ZoneInfo
from datetime import datetime, timezone

GROUP_NAME = "MIND ORIENTATION LICENSED BEHAVIOR"
GROUP_NPI = **********

IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)



def validate_medicaid_claim_group_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 1: Medicaid claims must use Group NPI (**********) under 'MIND ORIENTATION LICENSED BEHAVIOR'."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    
    is_medicaid = False
    for sbr_seg in mapped_claim_segments:
        if sbr_seg.get("Insurance Type Code", "").upper() == "MEDICAID":
            is_medicaid = True
            break
    
    if is_medicaid:
        billing_npi = next((seg.get("Identification Code") for seg in mapped_claim_segments if seg.get("Entity Identifier Code") == "85"), None)
        group_name = next((seg.get("Name Last or Organization Name") for seg in mapped_claim_segments if seg.get("Entity Identifier Code") == "PR"), "").upper()
        
        for sbr_seg in mapped_claim_segments:
            if sbr_seg.get("Insurance Type Code", "").upper() == "MEDICAID":
                insured_group_name = sbr_seg.get("Insured Group Name", "").upper()
                if insured_group_name != GROUP_NAME:
                    errors.append((
                        f"Medicaid claim has Insured Group Name {insured_group_name} instead of '{GROUP_NAME}'",
                        f"Ensure Medicaid claims use Insured Group Name '{GROUP_NAME}'"
                    ))
                break
        
        if billing_npi != GROUP_NPI or group_name != GROUP_NAME:
            errors.append((
                f"Medicaid claim uses Billing NPI {billing_npi} or Group Name {group_name} instead of Group NPI {GROUP_NPI} and Group Name '{GROUP_NAME}'",
                f"Ensure Medicaid claims use Group NPI {GROUP_NPI} and Group Name '{GROUP_NAME}'"
            ))
    
    return errors



RENDERING_PROVIDER_NPI = "198211600"
RENDERING_PROVIDER_LAST_NAME = "LESSER MALKA"

def validate_bbs_bcba_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: All claims should be submitted under the respective patient’s BCBA provider (Rendering Provider: LESSER MALKA, NPI#: 198211600)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Check if the Rendering Provider matches the required criteria
    rendering_provider_is_bcba = any(
        seg.get("Name Last or Organization Name", "").upper() == RENDERING_PROVIDER_LAST_NAME and 
        seg.get("Identification Code") == RENDERING_PROVIDER_NPI
        for seg in mapped_all if seg.get("Entity Identifier Code") == "82"
    )
    
    if not rendering_provider_is_bcba:
        errors.append((
            f"Claim does not have the correct BCBA Rendering Provider (LAST NAME: {RENDERING_PROVIDER_LAST_NAME}, NPI: {RENDERING_PROVIDER_NPI})",
            f"Ensure the Rendering Provider is {RENDERING_PROVIDER_LAST_NAME} with NPI {RENDERING_PROVIDER_NPI}"
        ))
        
    return errors



def validate_mind_orientation_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all BCBA-specific validation rules."""
    return (
        validate_medicaid_claim_group_npi(claim) +
        validate_bbs_bcba_provider(claim)
    ) 
