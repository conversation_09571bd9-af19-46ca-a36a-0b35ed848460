from typing import List, Tu<PERSON>, Dict
from .utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
PROVIDER_NPI = "**********"  # Jasmine Wright
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
XS_MODIFIER_START_DATE = datetime(2023, 8, 8)  # From update on 08/08/2023

def validate_growth_street_amerigroup_npi_tax(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 1: Amerigroup requires <PERSON>'s NPI and practice Tax ID."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]

    is_amerigroup = any(
        "AMERIGROUP" in seg.get("Payer Name", "").upper() or
        "AMERIGROUP" in seg.get("Application Receiver's Code", "").upper() or
        "AMERIGROUP" in seg.get("Name Last or Organization Name", "").upper()
        for seg in mapped_all
    )
    if not is_amerigroup:
        return []

    billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
    if billing_npi != PROVIDER_NPI:
        errors.append((
            f"Amerigroup claim Billing NPI {billing_npi} does not match Jasmine Wright's NPI {PROVIDER_NPI}",
            f"Use Jasmine Wright's NPI {PROVIDER_NPI} in NM1*85 for Amerigroup"
        ))

    tax_id = next((seg.get("Reference Identification") for seg in mapped_all if seg.get("Reference Identification Qualifier") == "EI"), None)
    if tax_id != TAX_ID:
        errors.append((
            f"Amerigroup claim Tax ID {tax_id} does not match practice Tax ID {TAX_ID}",
            f"Use practice Tax ID {TAX_ID} in REF*EI for Amerigroup"
        ))

    return errors

def validate_growth_street_telehealth_pos_02(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    print("validate_growth_street_telehealth_pos_02", "#" * 50)
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            modifier = parts[2] if len(parts) > 2 else ""
            if pos in ["02", "10"] and "95" not in modifier:
                errors.append((
                    f"Telehealth POS {pos} missing modifier 95 for CPT {parts[1]}",
                    "Add modifier 95 for POS 02 or 10 per Growth Street rules"
                ))
    return errors

def validate_growth_street_same_dos_cpt_diff_pos(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: Same DOS & CPT with different POS requires XS modifier post-08/08/2023."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE

    if service_lines:
        dos_cpt_pos_map = {}
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2:
                cpt = parts[1]
                modifier = parts[2] if len(parts) > 2 else ""
                pos = seg.get("Place of Service", "Unknown")
                key = (dos, cpt)
                dos_cpt_pos_map.setdefault(key, []).append((pos, modifier))

        for (dos, cpt), pos_modifiers in dos_cpt_pos_map.items():
            unique_pos = set(pos for pos, _ in pos_modifiers)
            if len(unique_pos) > 1:
                xs_count = sum(1 for _, mod in pos_modifiers if "XS" in mod)
                if xs_count < 1:
                    errors.append((
                        f"Same DOS {dos} and CPT {cpt} with different POS ({', '.join(unique_pos)}) missing XS modifier",
                        "Add XS modifier to one line for same DOS and CPT with different POS"
                    ))
    return errors

def validate_growth_street_tricare_97153_97156_overlap(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Change: TRICARE 97153/97156 overlap requires HS modifier on 97156."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_tricare = any(seg.get("Payer Name", "").upper().startswith("TRICARE") or "TRICARE" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_tricare:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos_cpt_map = {}
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in {"97153", "97156"}:
            dos = next((s.get("Date Time Period") for s in mapped_all if "Date/Time Qualifier" in s and s.get("Date/Time Qualifier") == "472"), "Unknown")
            modifier = parts[2] if len(parts) > 2 else ""
            dos_cpt_map.setdefault(dos, []).append((parts[1], modifier))

    for dos, cpts in dos_cpt_map.items():
        cpt_set = {cpt for cpt, _ in cpts}
        if "97153" in cpt_set and "97156" in cpt_set:
            for cpt, modifier in cpts:
                if cpt == "97156" and "HS" not in modifier:
                    errors.append((
                        f"TRICARE 97156 on {dos} overlapping with 97153 missing HS modifier",
                        "Add HS modifier to 97156 when overlapping with 97153 on same DOS"
                    ))

    return errors
  
def validate_growth_street_bcbsnj_97153_rate_increase(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    print("validate_growth_street_bcbsnj_97153_rate_increase", "~" * 50)
    errors = []
    rate_update_date = datetime(2024, 4, 15)
    prev_rate = 12.88
    new_rate = 13.88

    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_bcbsnj = any(
        (
            "BCBS NEW JERSEY (HORIZON)" in seg.get("Payer Name", "").upper() or
            "BCBS NEW JERSEY (HORIZON)" in seg.get("Application Receiver's Code", "").upper() or
            "BCBS NEW JERSEY (HORIZON)" in seg.get("Name Last or Organization Name", "").upper() or
            ("BCBS" in seg.get("Name Last or Organization Name", "").upper() and "HORIZON" in seg.get("Name Last or Organization Name", "").upper() and "NJ" in seg.get("Name Last or Organization Name", "").upper()) or
            ("HORIZON" in seg.get("Name Last or Organization Name", "").upper() and "NJ" in seg.get("Name Last or Organization Name", "").upper())
        )
        for seg in mapped_all
    )
    if not is_bcbsnj:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) < 2:
            continue
        cpt = parts[1]
        if cpt != "97153":
            continue

        units = seg.get("Units/Days", "").strip()
        monetary_amount = seg.get("Monetary Amount", "0").strip()

        units_val = float(units)
        monetary_amount_val = float(monetary_amount)
        monetary_amount_per_unit = monetary_amount_val / units_val


        dos = seg.get("Date Time Period")

        dos_date = datetime.strptime(dos, "%Y%m%d")

        expected_rate = new_rate if dos_date >= rate_update_date else prev_rate
        if monetary_amount_per_unit != expected_rate:
            errors.append((
                f"Horizon BCBSNJ CPT 97153 on {dos} billed at ${monetary_amount_per_unit:.2f}/unit (expected ${expected_rate:.2f}/unit)",
                f"Update rate to ${expected_rate:.2f}/unit for CPT 97153 as per BCBSNJ rules"
            ))

    return errors


def validate_growth_street_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Growth Street-specific validation rules."""
    return (
        validate_growth_street_amerigroup_npi_tax(claim) +
        validate_growth_street_telehealth_pos_02(claim) +
        validate_growth_street_same_dos_cpt_diff_pos(claim) +
        validate_growth_street_tricare_97153_97156_overlap(claim) +
        validate_growth_street_bcbsnj_97153_rate_increase(claim)
    )