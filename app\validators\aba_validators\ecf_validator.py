from typing import List, Tu<PERSON>, Dict
from .utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "95-1690988"
GROUP_NPI = "**********"
PROVIDER_NPI = "**********"  # Dr. <PERSON><PERSON> Jordan
LAYLA_NPI = "**********"  # Placeholder - replace with actual NPI for Layla Farah
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
MODIFIER_ADD_ON_DATE = datetime(2024, 7, 11)  # From update on 07/11/2024
LAYLA_BILLING_DATE = datetime(2025, 3, 4)  # From update on 03/04/2025

def validate_ecf_lacare_h0031_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 1: LA Care H0031 requires HC (Victoria/Paloma) or HP (<PERSON>ee) modifiers post-07/11/2024."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_lacare = any(seg.get("Payer Name", "").upper().startswith("LA CARE") or "LACARE" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_lacare:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= MODIFIER_ADD_ON_DATE:
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] == "H0031":
                modifier = parts[2] if len(parts) > 2 else ""
                rendering_npi = seg.get("Rendering Provider Identifier", "Unknown")
                if modifier not in {"HC", "HP"}:
                    errors.append((
                        f"LA Care H0031 on {dos} missing HC/HP modifier; found '{modifier}'",
                        "Use HC (Master's RBT: Victoria/Paloma) or HP (Denee) for H0031 post-07/11/2024"
                    ))
                elif modifier == "HP" and rendering_npi != PROVIDER_NPI:
                    errors.append((
                        f"LA Care H0031 on {dos} with HP modifier but Rendering NPI {rendering_npi} != {PROVIDER_NPI}",
                        "HP modifier reserved for Dr. Denee Jordan (NPI: **********)"
                    ))
    
    return errors

def validate_ecf_nova_martin_commercial(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: Log Nova Martin claims for commercial rate (partial implementation)."""
    errors = []
    mapped_patient = [map_segment(seg) for seg in claim["patient"]]
    patient_name = next((seg.get("Name Last or Organization Name", "").upper() + "_" + seg.get("Name First", "").upper() for seg in mapped_patient if "Name Last or Organization Name" in seg), "")
    
    if "NOVA_MARTIN" in patient_name:
        errors.append((
            f"Patient Nova Martin detected",
            "Bill using commercial rate per update (manual verification required)"
        ))
    
    return errors

def validate_ecf_billing_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 3: Claims after 03/04/2025 should be submitted under Layla Farah, not Denee Jordan."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE
    
    if dos_date >= LAYLA_BILLING_DATE:
        rendering_provider = next((seg.get("Rendering Provider Identifier") for seg in mapped_all if "Rendering Provider Identifier" in seg), None)
        if rendering_provider == PROVIDER_NPI:
            errors.append((
                f"Claim dated {dos} submitted under Dr. Denee Jordan",
                "Claims after 03/04/2025 should be submitted under Layla Farah per update"
            ))
    
    return errors

def validate_ecf_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all ECF-specific validation rules."""
    return (
        validate_ecf_lacare_h0031_modifier(claim) +
        validate_ecf_nova_martin_commercial(claim) +
        validate_ecf_billing_provider(claim)
    )