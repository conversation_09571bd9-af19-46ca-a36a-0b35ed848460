from typing import List, <PERSON><PERSON>, Dict
from .utils import map_segment

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
PROVIDER_NAME = "HAYLEY JONES"
PROVIDER_NPI = "**********"
REQUIRED_MODIFIER = "HO"


def validate_new_heights_ho_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: For provider 'Hayley Jones' (NPI **********), require HO modifier on all service lines. Only one error per claim."""
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    rendering_providers = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "82"]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    for seg in service_lines:
        rendering_npi = None
        if "Rendering Provider Identifier" in seg:
            rendering_npi = seg["Rendering Provider Identifier"]
        else:
            rendering_npi = next((rp.get("Identification Code") for rp in rendering_providers), None)

        if rendering_npi == PROVIDER_NPI:
            service_id = seg.get("Service Identification", "")
            parts = service_id.split(":")
            modifiers = [p.strip() for p in parts[2:6] if p.strip()]
            if REQUIRED_MODIFIER not in modifiers:
                return [(
                    f"Missing required modifier '{REQUIRED_MODIFIER}' for {PROVIDER_NAME} (NPI: {PROVIDER_NPI}).",
                    f"Add '{REQUIRED_MODIFIER}' modifier for {PROVIDER_NAME} (NPI: {PROVIDER_NPI})."
                )]
    return []


def validate_new_heights_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all New Heights-specific validation rules."""
    return (
        validate_new_heights_ho_modifier(claim)
    ) 