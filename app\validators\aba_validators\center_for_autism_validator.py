from typing import List, <PERSON><PERSON>, Dict
from .utils import map_segment

# Practice-specific constants
TAX_ID = "56-2302852"
GROUP_NPI = "**********"
PROVIDER_NPIS = {
    "Luz Mancia": "**********"
}

def validate_center_for_autism_telehealth_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validates telehealth services use 95 modifier."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt = parts[1]
            modifier = parts[2] if len(parts) > 2 else ""
            # Assuming POS 02 indicates telehealth
            if pos == "02" and "95" not in modifier:
                errors.append((
                    f"Telehealth CPT {cpt} with POS {pos} and modifier '{modifier}'",
                    "Use 95 modifier for telehealth services"
                ))
    return errors

def validate_center_for_autism_same_dos_different_pos(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validates same DOS and CPT with different POS uses XS modifier."""
    errors = []

    # Get all segments from claim
    all_segments = (
        claim["billing_provider"] +
        claim["subscriber"] +
        claim["patient"] +
        claim["claim_segments"]
    )

    service_lines = []
    current_lx = None

    for i, segment in enumerate(all_segments):
        if segment.startswith("LX*"):
            current_lx = segment.split("*")[1].rstrip("~")
            j = i + 1
            current_service = None
            current_dos = None
            current_pos = None

            while j < len(all_segments) and not all_segments[j].startswith("LX*"):
                seg = all_segments[j]

                if seg.startswith("SV1*"):
                    parts = seg.split("*")
                    if len(parts) > 1:
                        current_service = parts[1]
                        service_parts = current_service.split(":")
                        if len(service_parts) >= 7:
                            current_pos = service_parts[6]

                elif seg.startswith("DTP*472*"):
                    parts = seg.split("*")
                    if len(parts) >= 4:
                        current_dos = parts[3].rstrip("~")
                        if current_lx and current_service and current_dos:
                            service_lines.append({
                                "lx": current_lx,
                                "service": current_service,
                                "dos": current_dos,
                                "pos": current_pos or ""
                            })
                        break

                j += 1

    if not service_lines:
        return []

    # Group by DOS and CPT
    dos_cpt_pos_map = {}
    for line in service_lines:
        parts = line["service"].split(":")
        if len(parts) >= 2:
            cpt = parts[1]
            modifier = parts[2] if len(parts) > 2 and parts[2] else ""
            dos = line["dos"]
            pos = line["pos"]
            lx = line["lx"]

            key = (dos, cpt)
            dos_cpt_pos_map.setdefault(key, []).append({
                "pos": pos,
                "modifier": modifier,
                "lx": lx
            })

    # Check for violations
    for (dos, cpt), entries in dos_cpt_pos_map.items():
        if len(entries) > 1:
            unique_pos = set(entry["pos"] for entry in entries if entry["pos"])
            if len(unique_pos) > 1:
                has_xs = any("XS" in entry["modifier"] for entry in entries)
                if not has_xs:
                    # Use unique, sorted LX numbers in error message
                    lx_numbers = sorted(set(entry["lx"] for entry in entries))
                    errors.append((
                        f"CPT {cpt} on DOS {dos} with multiple POS (LX: {', '.join(lx_numbers)})",
                        "Use XS modifier for at least one line when same DOS and CPT has different POS"
                    ))

    return errors

def validate_center_for_autism_same_dos_cpt_pos_different_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validates same DOS, CPT, POS with different provider uses XP modifier."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)

    if not dos:
        return []

    dos_cpt_pos_provider_map = {}
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt = parts[1]
            modifier = parts[2] if len(parts) > 2 else ""
            pos = seg.get("Place of Service", "").strip()
            provider_npi = seg.get("Rendering Provider Identifier", "Unknown")
            key = (dos, cpt, pos)
            dos_cpt_pos_provider_map.setdefault(key, []).append((provider_npi, modifier))

    for (dos, cpt, pos), provider_modifiers in dos_cpt_pos_provider_map.items():
        unique_providers = set(provider for provider, _ in provider_modifiers)
        if len(unique_providers) > 1:  # Same DOS, CPT, POS, different providers
            for provider, modifier in provider_modifiers:
                if "XP" not in modifier:
                    errors.append((
                        f"CPT {cpt} on DOS {dos} with POS {pos} and modifier '{modifier}' for provider {provider}",
                        "Use XP modifier for same DOS, CPT, POS with different provider"
                    ))
    return errors

def validate_center_for_autism_patient_specific_cpt_changes(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validates patient-specific CPT code changes."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    patient_name = next((seg.get("Patient Name") for seg in mapped_all if "Patient Name" in seg), "").upper()
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt = parts[1]
            # Dominick Hernandez - Healthnet: 9-code to H-code
            if "DOMINICK HERNANDEZ" in patient_name and "HEALTHNET" in payer_name:
                if cpt.startswith("9"):
                    errors.append((
                        f"CPT {cpt} for Dominick Hernandez with Healthnet",
                        "Change 9-code to H-code (e.g., 97153 to H2019) per billing specs"
                    ))
            # JUSTYN WASHINGTON - MHN: H-code to 9-code
            elif "JUSTYN WASHINGTON" in patient_name and "MHN" in payer_name:
                if cpt.startswith("H"):
                    errors.append((
                        f"CPT {cpt} for JUSTYN WASHINGTON with MHN",
                        "Change H-code to 9-code (e.g., H2019 to 97153) per billing specs"
                    ))
    return errors

def validate_center_for_autism_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Center For Autism And Related Services-specific validation rules."""
    return (
        validate_center_for_autism_telehealth_modifier(claim) +
        validate_center_for_autism_same_dos_different_pos(claim) +
        validate_center_for_autism_same_dos_cpt_pos_different_provider(claim) +
        validate_center_for_autism_patient_specific_cpt_changes(claim)
    )