# app/routers/daq.py
from fastapi import APIRouter, UploadFile, File, HTTPException, Request, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from datetime import date

import collections

# Import the “core” functions (no FastAPI decorators) from daq_validators/main.py
from app.validators.daq_validators.main import (
    process_file,
    get_daily_count,
    get_daily_stats,
    send_daily_report as daq_send_daily_report,
    init_db as daq_init_db,
    scheduler as daq_scheduler,
    reset_daily_count as daq_reset_daily_count
)

from app.utils import verify_daq_token

# Templates folder (shared with the unified application)
templates = Jinja2Templates(directory="app/templates")

router = APIRouter(prefix="/daq", tags=["DAQ"])

@router.get("/", response_class=HTMLResponse)
async def daq_home(request: Request):
    """
    DAQ Validator Home Page:
    Renders a form where the user can upload a file.
    """
    return templates.TemplateResponse("daq_home.html", {"request": request})

@router.post("/process", response_class=HTMLResponse)
async def process_daq_file(request: Request, file: UploadFile = File(...)):
    """
    1) Call process_file(...) which returns a dict {"results": [...], "message": "..."}.
    2) Render daq_results.html with that context.
    """
    try:
        result = await process_file(file)

        return templates.TemplateResponse(
            "daq_results.html",
            {
                "request": request,
                "message": result.get("message", ""),
                "results": result.get("results", [])
            }
        )
    except HTTPException as he:
        # re‐raise to let FastAPI handle it
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/daily-count", response_class=JSONResponse)
async def daq_daily_count():
    """
    Return {"validated_files": <int>} for today.
    """
    return {"validated_files": get_daily_count()}

@router.get("/daily-stats", response_class=JSONResponse)
async def daq_daily_stats():
    """
    Return the full stats dict for today, e.g.:
        {
          "total_validations": 5,
          "file_stats": [
              { "filename": "...", "total_errors": 3, "error_breakdown": {...} },
              ...
          ]
        }
    """
    return get_daily_stats()

@router.post("/trigger-daily-report", response_class=JSONResponse, dependencies=[Depends(verify_daq_token)])
async def daq_trigger_report():
    """
    Manually fire off the daily email (for testing).
    """
    try:
        daq_send_daily_report()
        return {"message": "Daily report triggered successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to trigger report: {e}")

@router.post("/reset-database", response_class=JSONResponse)
async def daq_reset_db():
    """
    Re‐run init_db() to rebuild tables (empty or recreate).
    """
    try:
        daq_init_db()
        return {"message": "Database reset successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset database: {e}")
