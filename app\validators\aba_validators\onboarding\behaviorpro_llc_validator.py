from typing import List, <PERSON><PERSON>, Dict
from ..utils import map_segment

PROVIDER_NPI_BP = "**********"
GROUP_NPI: "**********"
def validate_behaviorpro_billing_for_bcba_sessions(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    Behaviorpro Rule 3 extended:
    - If sessions are seen by <PERSON> (BCBA, NPI **********),
      then all claims must be billed under her NPI.
    - If sessions are seen by others, no billing enforcement here (or implement separate logic).
    """
    errors = []
    EXPECTED_NPI = "**********"

    mapped_all = [map_segment(seg) for seg in (
        claim.get("billing_provider", []) +
        claim.get("subscriber", []) +
        claim.get("patient", []) +
        claim.get("claim_segments", [])
    )]
    # print(f"Total claim segments mapped: {len(mapped_all)}")

    billing_npi = next(
        (seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"),
        None
    )
    # print(f"Billing NPI found: {billing_npi}")

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    rendering_npis = {seg.get("Rendering Provider Identifier") for seg in service_lines if seg.get("Rendering Provider Identifier")}
    # print(f"Rendering NPIs found in service lines: {rendering_npis}")

    sessions_seen_by_anna = EXPECTED_NPI in rendering_npis

    if sessions_seen_by_anna:
        if billing_npi != EXPECTED_NPI:
            errors.append((
                f"Claim billed with NPI {billing_npi} but sessions seen by Anna Alinea require billing NPI {EXPECTED_NPI}",
                f"Use Anna Alinea's NPI ({EXPECTED_NPI}) in NM1*85 when sessions are seen by her"
            ))

        # All rendering NPIs must be Anna's if sessions are seen by her
        for npi in rendering_npis:
            if npi != EXPECTED_NPI:
                errors.append((
                    f"Service line rendered by NPI {npi} but sessions seen by Anna Alinea require rendering NPI {EXPECTED_NPI}",
                    f"Use Anna Alinea's NPI ({EXPECTED_NPI}) in SV1 for all service lines if sessions are seen by her"
                ))

    return errors

def validate_behaviorpro_flat_rate_50(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validate that each unit is billed at a flat rate of $50 (Behaviorpro Rule 5 addition)."""
    errors = []
    segments = claim.get("claim_segments") or []
    mapped_segments = [mapped for mapped in (map_segment(seg) for seg in segments if seg) if mapped]
    # print(f"Total claim segments mapped: {len(mapped_segments)}")

    service_lines = [seg for seg in mapped_segments if seg and "Service Identification" in seg]
    # print(f"Service lines found: {len(service_lines)}")

    for seg in service_lines:
        service_id = seg.get("Service Identification", "")
        # print(f"\nProcessing service line: {service_id}")

        total_charge_str = seg.get("Monetary Amount", "").strip()
        units_str = seg.get("Units/Days", "").strip()
        # print(f"Total charge string: '{total_charge_str}', Units string: '{units_str}'")

        if not total_charge_str or not units_str:
            # print("Skipping: Missing total charge or units")
            continue

        total_charge = float(total_charge_str)
        units = float(units_str)


        per_unit_rate = round(total_charge / units, 2)
        # print(f"Calculated per-unit rate: {per_unit_rate}")

        expected_rate = 50.00
        if per_unit_rate != expected_rate:
            error_msg = (
                f"Incorrect per-unit rate for service line {service_id}: "
                f"charged {per_unit_rate}/unit, expected {expected_rate}/unit"
            )
            errors.append((error_msg))
            print(f"Error appended: {error_msg}")
        
    return errors



def validate_behaviorpro_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Behaviorpro-specific validation rules."""
    return (
        validate_behaviorpro_billing_for_bcba_sessions(claim) +
        validate_behaviorpro_flat_rate_50(claim)
    )
