from typing import List, <PERSON><PERSON>, Dict
from .utils import map_segment

# Practice-specific constants
TAX_ID = "87-3324345"
GROUP_NPI = "**********"
PROVIDER_NPIS = {
    "<PERSON> Shams": "**********",
    "<PERSON><PERSON><PERSON>bley": "**********"
}

def validate_starlight_autism_rendering_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: All claims must be billed under <PERSON> (NPI **********) or <PERSON><PERSON><PERSON> (NPI **********)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    # Get rendering provider from NM1*82 segment
    rendering_npi = next((seg.get("Identification Code") for seg in mapped_all 
                        if seg.get("Entity Identifier Code") == "82"), "Unknown")

    allowed_npis = {
        "Sarah Shams": "**********",
        "<PERSON><PERSON><PERSON>bley": "**********"
    }

    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt = parts[1]
            if rendering_npi not in allowed_npis.values():
                errors.append((
                    f"CPT {cpt} billed with Rendering NPI {rendering_npi}",
                    f"Bill under Sarah Shams (NPI: {allowed_npis['Sarah Shams']}) or Alyson Sibley (NPI: {allowed_npis['Alyson Sibley']}) per Starlight rules"
                ))
    return errors

def validate_starlight_autism_no_modifiers_97151_97155_97156(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: No HO, HM, HN modifiers for 97151, 97155, and 97156."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    no_modifier_cpts = {"97151", "97155", "97156"}
    forbidden_modifiers = {"HO", "HM", "HN"}

    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in no_modifier_cpts:
            modifier = parts[2] if len(parts) > 2 else ""
            if modifier in forbidden_modifiers:
                errors.append((
                    f"CPT {parts[1]} has modifier '{modifier}'",
                    f"Remove modifier {modifier} from CPT {parts[1]} per Starlight rules"
                ))
    return errors


def validate_starlight_autism_bcbchp_payer_id(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: Ensure Blue Cross Community Health Plan has payer ID MCDIL"""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["claim_segments"]]

    for seg in mapped_all:
        if seg.get("Information Receiver Name") == "Blue Cross Community Health Plan":
            payer_id = seg.get("Payer ID", "")
            if payer_id != "MCDIL":
                errors.append((
                    f"Payer ID is {payer_id}",
                    "BCCHP Payer ID must be 'MCDIL' per Starlight rules"
                ))
    return errors


THERAPIST_MODIFIER_MAP = {
    "HM": {"Humza Khandaker", "Simra Khan", "Zoya Faig-Balagia", "Syria Harper", "Salma Amer"},
    "HN": {"Ashley O’Leary", "Moorehead, E'moni", "Shackleford, Jada"},
    "HO": {"Alyson Sibley", "SARAH SHAMS", "Geena Villotti", "Sabreen Sheikhali", "Kirksy, Mya"}
}



def validate_starlight_autism_97153_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: CPT 97153 must include correct modifier based on rendering provider"""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    print("mapped_All:",mapped_all)
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    rendering_provider = next((seg.get("Name", "").upper() for seg in mapped_all if seg.get("Entity Identifier Code") == "82"), "")
    print("rendering_provider:",rendering_provider,'*'*50)

    provider_to_modifier = {
        name.upper(): mod for mod, names in THERAPIST_MODIFIER_MAP.items() for name in names
    }

    expected_modifier = provider_to_modifier.get(rendering_provider)
    print("Expected modifier:",expected_modifier,'*"*50')
    if not expected_modifier:
        errors.append((
            f"Rendering provider {rendering_provider} not recognized",
            "Rendering provider must be one of the known therapists for CPT 97153 modifier mapping"
        ))
        return errors

    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        print("parts:",parts,'*'*50)
        if len(parts) >= 2 and parts[1] == "97153":
            actual_modifier = parts[2] if len(parts) > 2 else ""
            if actual_modifier != expected_modifier:
                errors.append((
                    f"CPT 97153 billed with modifier '{actual_modifier}' for provider {rendering_provider}",
                    f"Expected modifier '{expected_modifier}' for {rendering_provider}"
                ))

    return errors



def validate_starlight_autism_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Starlight Autism Therapy-specific validation rules."""
    return (
        validate_starlight_autism_rendering_provider(claim)
        # validate_starlight_autism_no_modifiers_97151_97155_97156(claim)
    )