import pandas as pd
from datetime import datetime
import logging

def rule1(row: pd.Series) -> dict:
    insurance_list = ["NJ Medicare", "NY Medicare", "Aetna HMO", "GHI", "UHC", "Oxford", 
                      "Health plus", "Touchstone", "NY Empire BCBS"]
    if row['Proc'] == "99243" and any(ins in row['Insurance'] for ins in insurance_list):
        return {
            "error": f"CPT 99243 not allowed for {row['Insurance']} insurance",
            "correction": "Change to 99203 (new patient) or 99213 (follow-up)"
        }
    return {}

def rule2(row: pd.Series) -> dict:
    # Add logging to debug the rule
    # logging.debug(f"Checking rule2: Proc={row.get('Proc')}, Mod={row.get('Mod')}, Insurance={row.get('Insurance')}")
    insurance = row['Insurance'].lower()
    # Only for NJ and NY Medicare, but not for Retired Railroad Medicare or Aetna Medicare HMO
    if "59" in str(row['Mod']):
        if (
            ("nj medicare" in insurance or "ny medicare" in insurance)
            and "retired railroad medicare" not in insurance
            and "aetna medicare hmo" not in insurance
        ):
            # logging.info(f"Rule2 triggered for Encounter ID {row.get('Encounter ID')}: Mod={row.get('Mod')}, Insurance={row.get('Insurance')}")
            return {
                "error": f"Modifier 59 invalid with {row['Insurance']} insurance",
                "correction": "Replace with modifier XU"
            }
    return {}

def rule3(group: pd.DataFrame) -> dict:
    if group['Proc'].astype(str).str.startswith("646").any() and not (group['Proc'] == "J0585").any():
        return {
            "error": "CPT 646XX billed without required companion code J0585",
            "correction": "Add code J0585"
        }
    return {}

def rule4(group: pd.DataFrame) -> dict:
    if (group['Proc'] == "J3301").any() and not group['Proc'].isin(["11900", "11901", "96372"]).any():
        return {
            "error": "CPT J3301 lacks required administration code (11900/11901)",
            "correction": "Add CPT 11900 or 11901"
        }
    return {}

def rule5(row: pd.Series) -> dict:
    if row['Proc'] == "95044":
        try:
            if float(row['Units']) != 36:
                return {
                    "error": f"CPT 95044 has incorrect units {row['Units']} instead of 36",
                    "correction": "Set units to 36"
                }
        except (ValueError, TypeError):
            return {
                "error": "CPT 95044 has invalid unit format",
                "correction": "Set units to 36"
            }
    return {}

def rule6(row: pd.Series) -> dict:
    pathology_codes = {"88305", "88342", "88341", "88312", "88313", "88364", "88365"}
    if row['Proc'] in pathology_codes and str(row['Mod']) != "26":
        return {
            "error": f"Pathology CPT {row['Proc']} has wrong modifier '{row['Mod']}'",
            "correction": "Use modifier 26"
        }
    return {}

def rule7(row: pd.Series) -> dict:
    if row['Proc'] == "88341":
        try:
            if float(row['Units']) < 1:
                return {
                    "error": f"CPT 88341 has incorrect units {row['Units']} instead of 1",
                    "correction": "Set units to 1"
                }
        except (ValueError, TypeError):
            return {
                "error": "CPT 88341 has invalid unit format",
                "correction": "Set units to 1"
            }
    return {}

def rule8(row: pd.Series) -> dict:
    eval_codes = {"99211", "99212", "99213", "99214", "99215"}
    if row['Proc'] in eval_codes and str(row['POS']) != "11":
        return {
            "error": f"CPT {row['Proc']} billed with incorrect POS {row['POS']}",
            "correction": "Set Place of Service to 11"
        }
    return {}

# Provider Update Rules
def rule9(row: pd.Series) -> dict:
    # Rule 1: WASSERMAN, KRISTY - Only NJ Medicare
    if "wasserman, kristy" in str(row['Provider']).lower() and "nj medicare" not in row['Insurance'].lower():
        return {
            "error": "Provider WASSERMAN, KRISTY is par only with NJ Medicare",
            "correction": "Verify insurance or provider assignment"
        }
    return {}

def rule10(row: pd.Series) -> dict:
    # Rule 2: MOHS for David Kiken in specific locations
    if "mohs" in str(row['Description']).lower() and "kiken, david" not in str(row['Provider']).lower():
        return {
            "error": "MOHS charges must be billed under David Kiken",
            "correction": "Change provider to David Kiken"
        }
    return {}

def rule11(row: pd.Series) -> dict:
    # Rule 3: UHC claims for David Kiken (Provider#44)
    if "kiken, david" in str(row['Provider']).lower() and "uhc" in row['Insurance'].lower():
        if "44" not in str(row.get('Provider ID', '')):
            return {
                "error": "UHC MOHS claims require Provider#44",
                "correction": "Update Provider ID to 44"
            }
    return {}

def rule12(row: pd.Series) -> dict:
    # Rule 4: David DeVinck needs referring provider-210
    if "devinck, david" in str(row['Provider']).lower() and "medicare" in row['Insurance'].lower():
        if "210" not in str(row.get('Referring Provider', '')):
            return {
                "error": "Missing referring provider-210",
                "correction": "Add referring provider-210"
            }
    return {}

# Modifier Update Rules
def rule13(row: pd.Series) -> dict:
    # Replace LT/RT with 50 for Medicare
    if "medicare" in row['Insurance'].lower():
        mods = str(row['Mod']).lower()
        if 'lt' in mods and 'rt' in mods:
            return {
                "error": "Use modifier 50 instead of LT/RT for Medicare",
                "correction": "Replace LT/RT with 50"
            }
    return {}

def rule14(row: pd.Series) -> dict:
    # 98968 requires POS 02 and no 95 modifier
    if row['Proc'] == "98968":
        if str(row['POS']) != "02":
            return {
                "error": "CPT 98968 requires POS 02",
                "correction": "Update Place of Service to 02"
            }
        if "95" in str(row['Mod']):
            return {
                "error": "Remove 95 modifier for telehealth CPT",
                "correction": "Delete 95 modifier"
            }
    return {}

# Units Update Rules
def rule15(row: pd.Series) -> dict:
    # Handle 88342 units > 2
    if row['Proc'] == "88342":
        try:
            if float(row['Units']) > 2:
                return {
                    "error": "88342 units > 2 requires split billing",
                    "correction": "Use 88342 (1 unit) + 88341 (additional units)"
                }
        except (ValueError, TypeError):
            pass
    return {}

def rule16(row: pd.Series) -> dict:
    # J0585 requires 100 units
    if row['Proc'] == "J0585":
        try:
            if float(row['Units']) != 100:
                return {
                    "error": "J0585 requires 100 units",
                    "correction": "Set units to 100"
                }
        except (ValueError, TypeError):
            return {
                "error": "Invalid units for J0585",
                "correction": "Set units to 100"
            }
    return {}

# Location Update Rules
def rule17(row: pd.Series) -> dict:
    # Tax ID validation for locations
    location = str(row.get('Location', '')).lower()
    insurance = row['Insurance'].lower()
    
    if "staten island" in location and "ny empire bcbs" in insurance:
        if "274" not in str(row.get('Tax ID', '')):
            return {
                "error": "Invalid Tax ID for Staten Island",
                "correction": "Use Tax ID-274"
            }
    return {}

def rule18(row: pd.Series) -> dict:
    # Special case for Jason Cervera
    if "jason cervera" in str(row['Patient Name']).lower():
        if "horizon bcbs" in row['Insurance'].lower() and "point pleasant" not in str(row.get('Location', '')).lower():
            return {
                "error": "Invalid location for Jason Cervera",
                "correction": "Use Point Pleasant location"
            }
    return {}

# CPT Update Rule
def rule19(row: pd.Series) -> dict:
    # Deleted codes after 2025
    deleted_codes = {"99441", "99442", "99443"}
    if row['Proc'] in deleted_codes:
        try:
            service_date = datetime.strptime(row['Service Date'], '%m-%d-%y')
            if service_date >= datetime(2025, 1, 1):
                return {
                    "error": "Deleted code - use 9800X series",
                    "correction": "Use 98008-98011 (new) or 98012-98015 (established)"
                }
        except:
            pass
    return {}

# Insurance Update Rule
def rule20(row: pd.Series) -> dict:
    # Dual plan requirements
    if "dual" in row['Insurance'].lower() and "medicaid" not in str(row.get('Secondary Insurance', '')).lower():
        return {
            "error": "Missing Medicaid secondary for dual plan",
            "correction": "Add AmeriChoice as secondary"
        }
    return {}
