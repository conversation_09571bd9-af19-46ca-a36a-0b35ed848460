import logging
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from typing import Dict, <PERSON>, Tuple, Set
import uvicorn
import collections
import json
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
import os
from datetime import datetime, date
from dotenv import load_dotenv
import sqlite3

from app.validators.aba_validators.adaptive_validator import validate_adaptive_claim_rules
from app.validators.aba_validators.onboarding.Epic_therapists_validator import validate_epic_practice_rules
from app.validators.aba_validators.onboarding.Zagar_pc_validator import validate_zagar_practice_rules
from app.validators.aba_validators.onboarding.miabelle_aba_validator import validate_miabelle_practice_rules
from app.validators.aba_validators.onboarding.pata_rehabilitation_validator import validate_pata_rehabilitation_practice_rules
from app.validators.aba_validators.onboarding.paving_pathways import validate_pp_practice_rules
from app.validators.aba_validators.tebra.elite_therapy_validator import validate_elite_therapy_practice_rules
from app.validators.aba_validators.onboarding.absolute_autism_validator import validate_absolute_autism_practice_rules
from .new_heights_validator import validate_new_heights_practice_rules

from .utils import map_segment, parse_hierarchy
from .general_validator import validate_file_structure, validate_claim_group
from .abcil_validator import validate_abcil_practice_rules
from .bbs_validator import validate_bbs_practice_rules
from .adbc_validator import validate_adbc_practice_rules
from .asd_life_validator import validate_asd_practice_rules
from .ecf_validator import validate_ecf_practice_rules
from .green_pediatrics_validator import validate_green_pediatrics_practice_rules
from .growth_street_validator import validate_growth_street_practice_rules
from .able_kids_validator import validate_able_kids_practice_rules
from .lassen_aba_validator import validate_lassen_aba_practice_rules
from .peak_therapy_validator import validate_peak_therapy_practice_rules
from .phos_inc_validator import validate_phos_inc_practice_rules
from .signature_behavioral_validator import validate_signature_behavioral_practice_rules
from .snh_validator import validate_snh_practice_rules
from .starlight_autism_validator import validate_starlight_autism_practice_rules
from .thweatt_aba_validator import validate_thweatt_aba_practice_rules
from .center_for_autism_validator import validate_center_for_autism_practice_rules
from .onboarding.behavioral_focus_validator import validate_behavioral_focus_practice_rules
from .onboarding.embracing_autism_validator import validate_embracing_autism_focus_practice_rules
from .onboarding.honey_bee_validator import validate_honey_bee_practice_rules
from .onboarding.hampton_roads_validators import validate_hampton_road_practice_rules
from .onboarding.siza_validator import validate_siza_practice_rules
from .onboarding.tempo_applied_behavior import validate_tempo_practice_rules
from .onboarding.reframing_behavior_validator import validate_reframing_behavior_practice_rules
from .onboarding.behaviorpro_llc_validator import validate_behaviorpro_practice_rules
from .onboarding.blessed_aba_validator import validate_blessed_practice_rules
from .onboarding.magnolia_milestone import validate_magnolia_practice_rules
from .onboarding.mind_in_motion_validator import validate_mimd_in_motion_practice_rules
from .onboarding.mind_orientation import validate_mind_orientation_practice_rules


# Registry mapping practice NPIs to their validator functions and default severity
PRACTICE_VALIDATORS = {
    "**********": (validate_abcil_practice_rules, "Medium"),  # ABCIL
    "**********": (validate_bbs_practice_rules, "High"),      # BBS
    "**********": (validate_adbc_practice_rules, "High"),     # ADBC
    "**********": (validate_asd_practice_rules, "High"),      # ASD Life
    "**********": (validate_ecf_practice_rules, "High"),      # ECF
    "**********": (validate_green_pediatrics_practice_rules, "High"),  # Green Pediatrics
    "**********": (validate_growth_street_practice_rules, "High"),     # Growth Street
    "**********": (validate_able_kids_practice_rules, "Medium"),       # Able Kids
    "**********": (validate_lassen_aba_practice_rules, "Medium"),      # Lassen ABA
    "**********": (validate_peak_therapy_practice_rules, "Medium"),    # Peak Therapy
    "**********": (validate_phos_inc_practice_rules, "Medium"),        # Phos Inc
    "**********": (validate_signature_behavioral_practice_rules, "Medium"),  # Signature Behavioral
    "1831701523": (validate_snh_practice_rules, "Medium"),             # SNH
    "1861152662": (validate_starlight_autism_practice_rules, "Medium"), # Starlight Autism
    "1609507961": (validate_thweatt_aba_practice_rules, "Medium"),     # Thweatt ABA
    "1326468844": (validate_center_for_autism_practice_rules, "Medium"), # Center For Autism
    "1700411642": (validate_behavioral_focus_practice_rules, "Low"),  # Behavioral Focus
    "1477391001": (validate_behaviorpro_practice_rules, "Low"),  # behaviorpro
    "1558897843": (validate_blessed_practice_rules, "High"),  # blessed
    "1700974458": (validate_zagar_practice_rules, "Low"),  # zagar 
    "1942934377": (validate_embracing_autism_focus_practice_rules, "Low"),  # Embracing Autism ABA
    "1457888620": (validate_epic_practice_rules, "Low"),  # epic therapists
    "1881286383": (validate_hampton_road_practice_rules, "Medium"),  # Hampton road Therapy
    "1871198648": (validate_honey_bee_practice_rules, "Medium"),  # Honey Bee Therapy
    "1174380877": (validate_magnolia_practice_rules, "Medium"),  # magnolia 
    "1437885308": (validate_miabelle_practice_rules, "Low"),  # miabelle ABA
    "1447006812": (validate_mimd_in_motion_practice_rules, "High"),  # mind in motion
    "1841868940": (validate_mind_orientation_practice_rules, "Medium"),  # mind orientation
    "1811449572": (validate_pata_rehabilitation_practice_rules, "Low"),  # pata rehabilitation
    "1275872731": (validate_pp_practice_rules, "Medium"),  # paving pathways inc
    "1730868266": (validate_reframing_behavior_practice_rules, "Low"),  # Reframing Behavior
    "1275289134": (validate_siza_practice_rules, "Medium"),  # siza Therapy
    "1780451732": (validate_tempo_practice_rules, "Medium"),  # Tempo Therapy
    "1548692395": (validate_adaptive_claim_rules, "Medium"),  # elite Therapy
    "1255962007": (validate_elite_therapy_practice_rules, "Medium"),  # elite Therapy
    "1699470252": (validate_new_heights_practice_rules, "Medium"),  # New Heights
    "xxxxxxxxx": (validate_absolute_autism_practice_rules, "Medium"),  # Absolute Autism
}

load_dotenv()

# Configuration from .env
SMTP_SERVER = os.getenv("SMTP_SERVER")
SMTP_PORT = int(os.getenv("SMTP_PORT"))
SENDER_EMAIL = os.getenv("SENDER_EMAIL")
SENDER_LOGIN = os.getenv("SENDER_LOGIN")
SENDER_PASSWORD = os.getenv("SENDER_PASSWORD")
RECEIVER_EMAILS = ["<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>"]
HOST = os.getenv("HOST")
PORT = int(os.getenv("PORT"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

app = FastAPI()
# app.mount("/static", StaticFiles(directory="static"), name="static")
# templates = Jinja2Templates(directory="templates")

scheduler = BackgroundScheduler()

# Database connection helper
def get_db_connection():
    conn = sqlite3.connect('validation_counts.db')
    conn.row_factory = sqlite3.Row
    return conn

# Initialize the database with a table for daily counts
def init_db():
    conn = get_db_connection()
    # ABA‐specific daily count table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS aba_daily_counts (
            date TEXT PRIMARY KEY,
            count INTEGER
        )
    ''')
    # ABA‐specific file stats table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS aba_file_validation_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT,
            practice_name   TEXT,
            validation_time DATETIME,
            total_errors INTEGER,
            error_breakdown TEXT
        )
    ''')
    conn.commit()
    conn.close()

def save_validation_stats(filename: str, practice_name: str, total_errors: int, error_breakdown: Dict[str, int]):
    conn = get_db_connection()
    conn.execute('''
        INSERT INTO aba_file_validation_stats
          (filename, practice_name, validation_time, total_errors, error_breakdown)
        VALUES (?,       ?,               ?,            ?,            ?)
    ''', (
        filename,
        practice_name,
        datetime.now().isoformat(),
        total_errors,
        json.dumps(dict(error_breakdown))
    ))
    conn.commit()
    conn.close()


def get_daily_stats(date: str) -> Dict:
    conn = get_db_connection()
    
    count_row = conn.execute('SELECT count FROM aba_daily_counts WHERE date = ?', (date,)).fetchone()
    total_validations = count_row['count'] if count_row else 0
    
    file_stats = conn.execute('''
        SELECT filename, practice_name, total_errors, error_breakdown
          FROM aba_file_validation_stats
         WHERE DATE(validation_time) = DATE(?)
    ''', (date,)).fetchall()
    
    conn.close()
    
    def safe_json_loads(data):
        if not data or data.strip() == '':
            return {}
        try:
            return json.loads(data)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in error_breakdown: '{data}', error: {str(e)}")
            return {}
    
    return {
        'total_validations': total_validations,
        'file_stats': [
            {
                'filename': row['filename'],
                'practice_name': row['practice_name'],
                'total_errors': row['total_errors'],
                'error_breakdown': safe_json_loads(row['error_breakdown'])
            } for row in file_stats
        ]
    }

# Get the current day's validation count
def get_daily_count():
    today = date.today().isoformat()
    conn = get_db_connection()
    row = conn.execute('SELECT count FROM aba_daily_counts WHERE date = ?', (today,)).fetchone()
    conn.close()
    return row['count'] if row else 0

# Increment the daily count
def increment_daily_count():
    today = date.today().isoformat()
    conn = get_db_connection()
    row = conn.execute('SELECT count FROM aba_daily_counts WHERE date = ?', (today,)).fetchone()
    new_count = (row['count'] if row else 0) + 1
    conn.execute('''
        INSERT OR REPLACE INTO aba_daily_counts (date, count)
        VALUES (?, ?)
    ''', (today, new_count))
    conn.commit()
    conn.close()

def reset_daily_count():
    today = date.today().isoformat()
    conn = get_db_connection()
    conn.execute('DELETE FROM aba_daily_counts WHERE date < ?', (today,))
    conn.commit()
    conn.close()
    logger.info(f"ABA daily_counts pruned to {today}")


# Email sending function
def send_daily_report():
    """Sends detailed daily validation report using AWS SES"""
    try:
        today = date.today().isoformat()
        stats = get_daily_stats(today)
        
        msg = MIMEMultipart('alternative')
        msg["From"] = SENDER_EMAIL
        msg["To"] = ", ".join(RECEIVER_EMAILS)
        msg["Subject"] = f"Daily ABA Validation Report - {today}"

        # Plain text fallback
        plain_body = f"""
        Daily ABA Validation Report - {today}
        {'=' * 40}
        Summary:
        - Total files validated: {stats['total_validations']}
        - Total files with errors: {sum(1 for f in stats['file_stats'] if f['total_errors'] > 0)}
        - Total errors detected: {sum(f['total_errors'] for f in stats['file_stats'])}
        
        File Details:
        """
        for file_stat in stats['file_stats']:
            plain_body += f"\nFile: {file_stat['filename']}\n- Practice: {file_stat['practice_name']}\n- Total errors: {file_stat['total_errors']}\n- Error breakdown:"
            for error_type, count in file_stat['error_breakdown'].items():
                plain_body += f"\n  - {error_type}: {count}"
        
        error_counts = collections.defaultdict(int)
        for file_stat in stats['file_stats']:
            for error_type, count in file_stat['error_breakdown'].items():
                error_counts[error_type] += count
        plain_body += f"\n\nError Type Analysis:\n{'-' * 40}"
        for error_type, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True):
            plain_body += f"\n- {error_type}: {count} occurrences"
        plain_body += f"\n\n{'=' * 40}\nGenerated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # HTML body (similar to theralympics implementation)
        html_body = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Daily ABA Validation Report</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 20px;
                }}
                .container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: #fff;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                }}
                h1 {{
                    color: #2c3e50;
                    border-bottom: 2px solid #eee;
                    padding-bottom: 10px;
                }}
                h2 {{
                    color: #34495e;
                    margin-top: 20px;
                }}
                .summary {{
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                }}
                th, td {{
                    padding: 12px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }}
                th {{
                    background-color: #f8f9fa;
                    font-weight: bold;
                }}
                .error-count {{
                    color: #dc3545;
                    font-weight: bold;
                }}
                details {{
                    margin: 10px 0;
                    padding: 10px;
                    background: #f8f9fa;
                    border-radius: 5px;
                }}
                summary {{
                    cursor: pointer;
                    font-weight: bold;
                    color: #2c3e50;
                }}
                .file-details {{
                    margin-top: 10px;
                    padding: 10px;
                    background: #fff;
                    border-radius: 3px;
                }}
                .error-analysis {{
                    margin-top: 30px;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 5px;
                }}
                .footer {{
                    margin-top: 30px;
                    padding-top: 15px;
                    border-top: 1px solid #eee;
                    color: #666;
                    font-size: 0.9em;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Daily ABA Validation Report - {today}</h1>
                <div class="summary">
                    <h2>Summary</h2>
                    <table>
                        <tr><th>Total Files Validated</th><td>{stats['total_validations']}</td></tr>
                        <tr><th>Files with Errors</th><td>{sum(1 for f in stats['file_stats'] if f['total_errors'] > 0)}</td></tr>
                        <tr><th>Total Errors Detected</th><td class="error-count">{sum(f['total_errors'] for f in stats['file_stats'])}</td></tr>
                    </table>
                </div>
                <h2>File Details</h2>
                {"".join([
                    f'''
                    <details>
                        <summary>File: {file_stat['filename']} (Practice: {file_stat['practice_name']}) ({file_stat['total_errors']} errors)</summary>
                        <div class="file-details">
                            <p>Total Errors: <span class="error-count">{file_stat['total_errors']}</span></p>
                            <table>
                                <tr><th>Error Type</th><th>Count</th></tr>
                                {"".join([f'<tr><td>{error_type}</td><td>{count}</td></tr>' for error_type, count in file_stat['error_breakdown'].items()])}
                            </table>
                        </div>
                    </details>
                    ''' for file_stat in stats['file_stats']
                ])}
                <div class="error-analysis">
                    <h2>Error Type Analysis</h2>
                    <table>
                        <tr><th>Error Type</th><th>Occurrences</th></tr>
                        {"".join([f'<tr><td>{error_type}</td><td>{count}</td></tr>' for error_type, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True)])}
                    </table>
                </div>
                <div class="footer">
                    Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                </div>
            </div>
        </body>
        </html>
        """

        msg.attach(MIMEText(plain_body, 'plain'))
        msg.attach(MIMEText(html_body, 'html'))

        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(SENDER_LOGIN, SENDER_PASSWORD)
            server.sendmail(SENDER_EMAIL, RECEIVER_EMAILS, msg.as_string())
            logger.info(f"Successfully sent daily ABA validation report for {today}")
    except Exception as e:
        logger.error(f"Failed to send daily ABA validation report: {str(e)}")
        raise

@app.post("/validate-837")
async def validate_837(file: UploadFile = File(...)):
    """Endpoint to validate an 837 EDI file for ABA services."""
    try:
        content = await file.read()
        content = content.decode('utf-8').strip()
        if not content:
            raise HTTPException(status_code=400, detail="Uploaded file is empty")
        
        segments = [seg.strip() for seg in content.split('~') if seg.strip()]
        all_errors = validate_file_structure(segments)
        structured_errors = [{"claim_id": "N/A", "error": err[0], "solution": err[1], "severity": "High"} for err in all_errors]
        
        claims = parse_hierarchy(segments)
        practice_name = "Unknown"
        if not claims:
            structured_errors.append({
                "claim_id": "N/A",
                "error": "No claims found in the file",
                "solution": "Ensure the file contains valid claim data (HL segments with level 22 or 23)",
                "severity": "High"
            })
        else:
            for claim in claims:
                claim_id = next((seg.get("Claim Submitter's Identifier") for seg in [map_segment(seg) for seg in claim["claim_segments"]] if "Claim Submitter's Identifier" in seg), "Unknown")
                if claim_id == "Unknown":
                    continue
                
                # General validation (applies to all practices)
                general_errors = validate_claim_group(claim)
                for error, solution in general_errors:
                    structured_errors.append({
                        "claim_id": claim_id,
                        "error": error,
                        "solution": solution,
                        "severity": "High"
                    })
                
                # Map all segments once for efficiency
                mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
                
                # Apply practice-specific validations using the registry
                for npi, (validator_fn, severity) in PRACTICE_VALIDATORS.items():
                    if any(seg.get("Identification Code") == npi for seg in mapped_all if "Identification Code" in seg):
                        practice_name = npi  # Use NPI as practice_name, or map to a friendly name if available
                        for error, solution in validator_fn(claim):
                            structured_errors.append({
                                "claim_id": claim_id,
                                "error": error,
                                "solution": solution,
                                "severity": severity
                            })

        # (A) increment today's validated‐file count
        increment_daily_count()

        # (B) build an error‐breakdown and save this file's stats
        error_breakdown = collections.Counter(
            err["error"].split(":")[0].strip()
            for err in structured_errors
        )
        if structured_errors:
            return {"errors": structured_errors,
                    "practice_name": practice_name}
        return {"message": "No errors found"}

    except UnicodeDecodeError:
        raise HTTPException(status_code=400, detail="File must be a valid UTF-8 encoded text file")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Root endpoint to serve an upload form."""
    return HTMLResponse(content="<h1>837 ABA Validator API</h1><p>Use /validate-837 to POST your file.</p>")

async def   validate_837_content(content: str) -> dict:
    """Core validation logic extracted from endpoint"""
    try:
        segments = [seg.strip() for seg in content.split('~') if seg.strip()]
        all_errors = validate_file_structure(segments)
        structured_errors = [{"claim_id": "N/A", "error": err[0], "solution": err[1], "severity": "High"} for err in all_errors]
        
        claims = parse_hierarchy(segments)
        practice_name = "Unknown"
        if not claims:
            structured_errors.append({
                "claim_id": "N/A",
                "error": "No claims found in the file",
                "solution": "Ensure the file contains valid claim data (HL segments with level 22 or 23)",
                "severity": "High"
            })
        else:
            for claim in claims:
                claim_id = next((seg.get("Claim Submitter's Identifier") for seg in [map_segment(seg) for seg in claim["claim_segments"]] if "Claim Submitter's Identifier" in seg), "Unknown")
                if claim_id == "Unknown":
                    continue
                
                # General validation (applies to all practices)
                general_errors = validate_claim_group(claim)
                for error, solution in general_errors:
                    structured_errors.append({
                        "claim_id": claim_id,
                        "error": error,
                        "solution": solution,
                        "severity": "High"
                    })
                
                # Map all segments once for efficiency
                mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
                
                # Apply practice-specific validations using the registry
                for npi, (validator_fn, severity) in PRACTICE_VALIDATORS.items():
                    if any(seg.get("Identification Code") == npi for seg in mapped_all if "Identification Code" in seg):
                        practice_name = npi  # Use NPI as practice_name, or map to a friendly name if available
                        for error, solution in validator_fn(claim):
                            structured_errors.append({
                                "claim_id": claim_id,
                                "error": error,
                                "solution": solution,
                                "severity": severity
                            })
        return {"errors": structured_errors, "practice_name": practice_name} if structured_errors else {"message": "No errors found", "practice_name": practice_name}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

from apscheduler.triggers.cron import CronTrigger

@app.on_event("startup")
def startup_event():
    # ensure ABA tables exist
    init_db()

    # schedule the ABA daily‐report email at 23:50 Asia/Kolkata
    scheduler.add_job(
        send_daily_report,
        trigger=CronTrigger(hour=23, minute=50, timezone="Asia/Kolkata"),
        name="aba_daily_report",
    )
    scheduler.add_job(
        reset_daily_count,
        trigger=CronTrigger(hour=0, minute=0, timezone="Asia/Kolkata"),
        name="aba_reset_daily_count",
    )

    scheduler.start()


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)