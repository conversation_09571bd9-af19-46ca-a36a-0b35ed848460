from typing import List, Tu<PERSON>, Dict
from ..utils import map_segment
from datetime import datetime, timedelta, timezone
from zoneinfo import ZoneInfo

# Constants for Zagar's practice
ZAGAR_NPI = "**********"
DEFAULT_RATE = "250"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)

def validate_zagar_provider_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Zagar Rule: All claims must use <PERSON><PERSON> <PERSON>'s NPI (**********)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]

    billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
    if billing_npi != ZAGAR_NPI:
        errors.append((
            f"Claim uses Billing NPI {billing_npi} instead of Zagar NPI {ZAGAR_NPI}",
            f"Use Zagar's NPI {ZAGAR_NPI} in NM1*85"
        ))

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        rendering_npi = seg.get("Rendering Provider Identifier")
        if rendering_npi and rendering_npi != ZAGAR_NPI:
            errors.append((
                f"Service line uses Rendering NPI {rendering_npi} instead of Zagar NPI {ZAGAR_NPI}",
                f"Use Zagar NPI {ZAGAR_NPI} in SV1"
            ))
    return errors




def validate_zagar_rate_assignment(claim: Dict[str, List[str]], muster_rates: Dict[str, str]) -> List[Tuple[str, str]]:
    """Zagar Rule: Use rate from Muster (Zagar/ASD Life), else default to $250."""
    errors = []
    service_lines = [map_segment(seg) for seg in claim["claim_segments"] if seg.startswith("SV1")]

    for seg in service_lines:
        service_id = seg.get("Service Identification", "")
        cpt = service_id.split(":")[1] if ":" in service_id else ""
        rate = seg.get("Monetary Amount", "")
        expected_rate = muster_rates.get(cpt, DEFAULT_RATE)
        if rate != expected_rate:
            errors.append((
                f"Incorrect rate for CPT {cpt}: found {rate}, expected {expected_rate}",
                f"Use rate {expected_rate} from Muster or default to {DEFAULT_RATE}"
            ))
    return errors





def validate_zagar_practice_rules(claim: Dict[str, List[str]], muster_rates: Dict[str, str]) -> List[Tuple[str, str]]:
    """Aggregates all Zagar-specific claim validation rules."""
    return (
        validate_zagar_provider_npi(claim) +
        validate_zagar_rate_assignment(claim, muster_rates)
    )
