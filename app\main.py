from fastapi import <PERSON><PERSON><PERSON>, Request, Depends
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from app.routers import aba, daq
from app.routers.theralympics import router as theralympics_router

from apscheduler.triggers.cron import CronTrigger

from app.validators.theralympics.main import (
    init_db as theralympics_init_db,
    scheduler as theralympics_scheduler,
    send_daily_report as theralympics_send_daily_report,
    reset_daily_count as theralympics_reset_daily_count,
)
from app.validators.aba_validators.main import (
    init_db as aba_init_db,
    scheduler as aba_scheduler,
    send_daily_report as aba_send_daily_report
)
from app.validators.daq_validators.main import (
    init_db as daq_init_db,
    scheduler as daq_scheduler,
    send_daily_report as daq_send_daily_report,
    reset_daily_count as daq_reset_daily_count
)
from app.utils import verify_general_token

app = FastAPI(title="Unified ABA/DAQ Validator")

app.mount("/static", StaticFiles(directory="app/static"), name="static")
templates = Jinja2Templates(directory="app/templates")

@app.on_event("startup")
def on_startup():
    # Initialize all three validator‐databases
    theralympics_init_db()
    aba_init_db()
    daq_init_db()

    # Schedule TheraLympics daily report + reset
    theralympics_scheduler.add_job(
        theralympics_send_daily_report,
        trigger=CronTrigger(hour=23, minute=55, timezone="Asia/Kolkata"),
        name="theralympics_daily_report",
    )
    theralympics_scheduler.add_job(
        theralympics_reset_daily_count,
        trigger=CronTrigger(hour=0, minute=0, timezone="Asia/Kolkata"),
        name="theralympics_reset_daily_count",
    )
    theralympics_scheduler.start()

    # Schedule ABA daily report + reset
    aba_scheduler.add_job(
        aba_send_daily_report,
        trigger=CronTrigger(hour=23, minute=50, timezone="Asia/Kolkata"),
        name="aba_daily_report",
    )
    aba_scheduler.start()

    # Schedule DAQ daily report + reset
    daq_scheduler.add_job(
        daq_send_daily_report,
        trigger=CronTrigger(hour=23, minute=45, timezone="Asia/Kolkata"),
        name="daq_daily_report",
    )
    daq_scheduler.add_job(
        daq_reset_daily_count,
        trigger=CronTrigger(hour=0, minute=0, timezone="Asia/Kolkata"),
        name="daq_reset_daily_count",
    )
    daq_scheduler.start()

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("home.html", {"request": request})


app.include_router(aba.router)
app.include_router(daq.router)
app.include_router(theralympics_router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
