from typing import Dict, List, Set

# Segment element names dictionary for EDI parsing
segment_element_names = {
    "ISA": ["Authorization Information Qualifier", "Authorization Information", "Security Information Qualifier", "Security Information", "Interchange ID Qualifier", "Interchange Sender ID", "Interchange ID Qualifier", "Interchange Receiver ID", "Interchange Date", "Interchange Time", "Interchange Control Standards Identifier", "Interchange Control Version Number", "Interchange Control Number", "Acknowledgment Requested", "Usage Indicator", "Component Element Separator"],
    "GS": ["Functional Identifier Code", "Application Sender's Code", "Application Receiver's Code", "Date", "Time", "Group Control Number", "Responsible Agency Code", "Version/Release/Industry Identifier Code"],
    "ST": ["Transaction Set Identifier Code", "Transaction Set Control Number", "Implementation Convention Reference"],
    "BHT": ["Hierarchical Structure Code", "Transaction Set Purpose Code", "Reference Identification", "Date", "Time", "Transaction Type Code"],
    "NM1": ["Entity Identifier Code", "Entity Type Qualifier", "Name Last or Organization Name", "Name First", "Name Middle", "Name Prefix", "Name Suffix", "Identification Code Qualifier", "Identification Code", "Entity Relationship Code", "Entity Identifier Code"],
    "PER": ["Contact Function Code", "Name", "Communication Number Qualifier", "Communication Number", "Communication Number Qualifier", "Communication Number", "Communication Number Qualifier", "Communication Number"],
    "HL": ["Hierarchical ID Number", "Hierarchical Parent ID Number", "Hierarchical Level Code", "Hierarchical Child Code"],
    "PRV": ["Provider Code", "Reference Identification Qualifier", "Reference Identification", "State or Province Code"],
    "SBR": ["Payer Responsibility Sequence Number Code", "Individual Relationship Code", "Insured Group or Policy Number", "Insured Group Name", "Insurance Type Code", "Coordination of Benefits Code", "Yes/No Condition or Response Code", "Employment Status Code", "Claim Filing Indicator Code"],
    "DMG": ["Date Time Period Format Qualifier", "Date Time Period", "Gender Code", "Marital Status Code", "Race or Ethnicity Code", "Citizenship Status Code", "Country Code"],
    "CLM": ["Claim Submitter's Identifier", "Monetary Amount", "Claim Frequency Type Code", "Yes/No Condition or Response Code", "Provider Accept Assignment Code", "Yes/No Condition or Response Code", "Release of Information Code", "Patient Status Code"],
    "REF": ["Reference Identification Qualifier", "Reference Identification", "Description"],
    "HI": ["Diagnosis Code", "Diagnosis Code2", "Diagnosis Code3", "Diagnosis Code4", "Diagnosis Code5", "Diagnosis Code6"],
    "LX": ["Assigned Number"],
    "SV1": ["Service Identification", "Monetary Amount", "Service Type Code", "Units/Days", "Place of Service", "Procedure Code", "Procedure Modifier", "Procedure Modifier2", "Quantity", "Unit or Basis for Measurement Code", "Service Type Code2", "Monetary Amount2", "Yes/No Condition or Response Code"],
    "DTP": ["Date/Time Qualifier", "Date Time Period Format Qualifier", "Date Time Period"],
    "N3": ["Address Information", "Additional Address Information"],
    "N4": ["City Name", "State or Province Code", "Postal Code"],
    "SE": ["Number of Included Segments", "Transaction Set Control Number"],
    "GE": ["Number of Transaction Sets Included", "Group Control Number"],
    "IEA": ["Number of Included Groups", "Interchange Control Number"]
}

seen_service_dates = set()

def map_segment(segment: str) -> Dict[str, str]:
    """Maps an EDI segment to a dictionary using predefined element names."""
    parts = segment.split('*')
    seg_id = parts[0]

    if seg_id == "DTP":
        if len(parts) < 4:
            return {}

        qualifier = parts[1]       
        date_format = parts[2]   
        date_value = parts[3]      

        if qualifier == '472' and date_format == 'RD8':
            if date_value in seen_service_dates:
                start_date = date_value.split('-')[0]

                parts[2] = 'D8'         
                parts[3] = start_date
            else:
                seen_service_dates.add(date_value)

    mapped = {"Segment ID": parts[0]}
    mapped = {}
    if seg_id in segment_element_names:
        names = segment_element_names[seg_id]
        for i, value in enumerate(parts[1:]):
            key = names[i] if i < len(names) else f"Element_{i+1}"
            mapped[key] = value
    else:
        mapped = {f"Element_{i}": elem for i, elem in enumerate(parts[1:], start=1)}

    mapped["Segment ID"] = seg_id
    return mapped

def parse_hierarchy(segments: List[str]) -> List[Dict[str, any]]:
    """Parses EDI segments into a hierarchical structure of claims."""
    claims = []
    hl_context = {}
    current_segments = []

    for segment in segments:
        if segment.startswith("HL"):
            if current_segments and hl_context:
                last_hl_id = list(hl_context.keys())[-1]
                hl_level = hl_context[last_hl_id]["level"]
                if hl_level in ["22", "23"]:
                    claim_segments = []
                    for seg in current_segments:
                        if seg.startswith("CLM"):
                            if claim_segments:
                                claims.append(build_claim(hl_context, last_hl_id, claim_segments))
                            claim_segments = [seg]
                        elif claim_segments:
                            claim_segments.append(seg)
                    if claim_segments:
                        claims.append(build_claim(hl_context, last_hl_id, claim_segments))
                current_segments = []

            parts = segment.split("*")
            hl_id = parts[1]
            hl_parent = parts[2] if parts[2] else None
            hl_level = parts[3]
            hl_child = parts[4].replace("~", "")
            hl_context[hl_id] = {
                "level": hl_level,
                "parent": hl_parent,
                "segments": [segment]
            }
        elif hl_context:
            last_hl_id = list(hl_context.keys())[-1]
            hl_context[last_hl_id]["segments"].append(segment)
            current_segments.append(segment)

    if current_segments and hl_context:
        last_hl_id = list(hl_context.keys())[-1]
        hl_level = hl_context[last_hl_id]["level"]
        if hl_level in ["22", "23"]:
            claim_segments = []
            for seg in current_segments:
                if seg.startswith("CLM"):
                    if claim_segments:
                        claims.append(build_claim(hl_context, last_hl_id, claim_segments))
                    claim_segments = [seg]
                elif claim_segments:
                    claim_segments.append(seg)
            if claim_segments:
                claims.append(build_claim(hl_context, last_hl_id, claim_segments))

    return claims

def build_claim(hl_context: Dict[str, Dict], hl_id: str, claim_segments: List[str]) -> Dict[str, any]:
    """Builds a claim dictionary from hierarchical context and claim segments."""
    claim_hl = hl_context[hl_id]
    hl_level = claim_hl["level"]
    subscriber_hl = claim_hl if hl_level == "22" else hl_context.get(claim_hl["parent"], None)
    billing_provider_hl = None
    parent_id = claim_hl["parent"] if hl_level == "23" else subscriber_hl["parent"] if subscriber_hl else None
    while parent_id and parent_id in hl_context:
        if hl_context[parent_id]["level"] == "20":
            billing_provider_hl = hl_context[parent_id]
            break
        parent_id = hl_context[parent_id]["parent"]

    return {
        "billing_provider": billing_provider_hl["segments"] if billing_provider_hl else [],
        "subscriber": subscriber_hl["segments"] if subscriber_hl else [],
        "patient": claim_hl["segments"] if hl_level == "23" else subscriber_hl["segments"] if subscriber_hl else [],
        "claim_segments": claim_segments
    }

def extract_diagnosis_codes(mapped_all: List[Dict[str, str]]) -> Set[str]:
    """Extracts and normalizes diagnosis codes from mapped segments."""
    diag_set = set()
    for seg in mapped_all:
        for key, value in seg.items():
            if key.startswith("Diagnosis Code") and value:
                if ":" in value:
                    code = value.split(":")[-1].strip()
                else:
                    code = value.strip()
                norm = code.replace(" ", "").replace(".", "").upper()
                diag_set.add(norm)
    return diag_set

def has_secondary_insurance(claim: Dict[str, List[str]]) -> bool:
    """Checks if the claim has secondary insurance."""
    mapped_subscriber = [map_segment(seg) for seg in claim["subscriber"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_subscriber + mapped_claim_segments:
        if seg.get("Payer Responsibility Sequence Number Code") == "S":
            return True
    return False