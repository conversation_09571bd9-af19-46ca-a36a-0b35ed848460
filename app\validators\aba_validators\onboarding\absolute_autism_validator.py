# app/validators/aba_validators/onboarding/absolute_autism_validator.py
from typing import List, <PERSON><PERSON>, Dict
from ..utils import map_segment

def _split_sv1(seg: Dict[str, str]) -> Tuple[str, str]:
    """Helper to pull CPT code and modifier out of SV1."""
    parts = seg["Service Identification"].split(":")
    code     = parts[1] if len(parts)>=2 else ""
    modifier = parts[2] if len(parts)>=3 else ""
    return code, modifier

def validate_gt_95_pos_restrictions(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: Modifiers GT, 95 should not be used when POS 11, 12 is billed."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    
    # Get POS from claim segments
    pos = None
    for seg in mapped:
        if "Place of Service" in seg:
            pos = seg["Place of Service"]
            break
    
    if pos in ["11", "12"]:
        svc = [s for s in mapped if "Service Identification" in s]
        for seg in svc:
            code, mod = _split_sv1(seg)
            if mod in ["GT", "95"]:
                errors.append((
                    f"Modifier {mod} should not be used with POS {pos}.",
                    f"Remove modifier {mod} for POS {pos}."
                ))
    
    return errors

def validate_presbyterian_turquoise_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rules 3 & 4: Presbyterian Turquoise care insurance modifier requirements."""
    errors = []
    mapped = [map_segment(s) for s in claim["subscriber"] + claim["claim_segments"]]
    
    # Get payer name
    payer = next(
        (seg["Name Last or Organization Name"].upper() for seg in mapped if "Name Last or Organization Name" in seg),
        ""
    )
    
    if "PRESBYTERIAN TURQUOISE CARE" in payer:
        svc = [s for s in mapped if "Service Identification" in s]
        for seg in svc:
            code, mod = _split_sv1(seg)
            if code == "97153" and mod != "U1":
                errors.append((
                    "Presbyterian Turquoise care: 97153 must have U1 modifier.",
                    "Use U1 modifier for 97153."
                ))
            if code in ["97151", "97155", "97156"] and mod != "U3":
                errors.append((
                    f"Presbyterian Turquoise care: {code} must have U3 modifier.",
                    f"Use U3 modifier for {code}."
                ))
    
    return errors

def validate_bcbs_nm_turquoise_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rules 5 & 6: BCBS NM TURQUOISE CARE modifier requirements."""
    errors = []
    mapped = [map_segment(s) for s in claim["subscriber"] + claim["claim_segments"]]
    
    # Get payer name
    payer = next(
        (seg["Name Last or Organization Name"].upper() for seg in mapped if "Name Last or Organization Name" in seg),
        ""
    )
    
    if "BLUE CROSS BLUE SHIELD" in payer:
        svc = [s for s in mapped if "Service Identification" in s]
        for seg in svc:
            code, mod = _split_sv1(seg)
            if code == "97154" and mod != "U4":
                errors.append((
                    "BCBS NM TURQUOISE CARE: 97154 must have U4 modifier.",
                    "Use U4 modifier for 97154."
                ))
            if code == "97153" and mod != "U4":
                errors.append((
                    "BCBS NM TURQUOISE CARE: 97153 must have U4 modifier.",
                    "Use U4 modifier for 97153."
                ))
    
    return errors

def validate_bcbs_ho_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 7: BCBS HO modifier requirements."""
    errors = []
    mapped = [map_segment(s) for s in claim["subscriber"] + claim["claim_segments"]]
    
    # Get payer name
    payer = next(
        (seg["Name Last or Organization Name"].upper() for seg in mapped if "Name Last or Organization Name" in seg),
        ""
    )
    
    if "BLUE CROSS BLUE SHIELD" in payer:
        svc = [s for s in mapped if "Service Identification" in s]
        for seg in svc:
            code, mod = _split_sv1(seg)
            if code == "97155" and mod != "U4":
                errors.append((
                    "BCBS HO: 97155 must have U4 modifier.",
                    "Use U4 modifier for 97155."
                ))
    
    return errors

def validate_katrina_gittins_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 8: If we receive rendering provider as KATRINA GITTINS and NPI **********, claim should be billed with same provider information."""
    errors = []
    mapped_rendering = [map_segment(s) for s in claim.get("rendering_provider", [])]
    
    # Check if KATRINA GITTINS is the rendering provider
    katrina_found = False
    for seg in mapped_rendering:
        first = seg.get("Name First", "").strip().upper()
        last = seg.get("Name Last or Organization Name", "").strip().upper()
        npi = seg.get("Identification Code", "").strip()
        
        if first == "KATRINA" and last == "GITTINS" and npi == "**********":
            katrina_found = True
            break
    
    if katrina_found:
        # Verify the claim billing provider matches
        mapped_billing = [map_segment(s) for s in claim.get("billing_provider", [])]
        billing_matches = False
        
        for seg in mapped_billing:
            first = seg.get("Name First", "").strip().upper()
            last = seg.get("Name Last or Organization Name", "").strip().upper()
            npi = seg.get("Identification Code", "").strip()
            
            if first == "KATRINA" and last == "GITTINS" and npi == "**********":
                billing_matches = True
                break
        
        if not billing_matches:
            errors.append((
                "Rendering provider KATRINA GITTINS (NPI **********) must match billing provider.",
                "Set billing provider to KATRINA GITTINS, NPI **********."
            ))
    
    return errors

def validate_absolute_autism_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combine all Absolute Autism LLC rules that can be EDI-checked."""
    return (
        validate_gt_95_pos_restrictions(claim) +
        validate_presbyterian_turquoise_modifiers(claim) +
        validate_bcbs_nm_turquoise_modifiers(claim) +
        validate_bcbs_ho_modifiers(claim) +
        validate_katrina_gittins_provider(claim)
    ) 
    