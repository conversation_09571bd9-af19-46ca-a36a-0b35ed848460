import pandas as pd

def rule1(row: pd.Series) -> dict:
    proc = str(row['Proc']).strip()  # Ensure Proc is a string
    insurance = str(row['Insurance']).strip().lower()  # Ensure Insurance is a string and lowercase
    print(f"DEBUG rule1 - Proc: '{proc}', Insurance: '{insurance}'")  # Enhanced debug output
    
    if proc in {"S9088", "S9083"}:
        print(f"DEBUG rule1 - CPT match found: {proc}")
        if "nj medicare" in insurance:
            print("DEBUG rule1 - Matched NJ Medicare")
        if insurance == "medicaid":
            print("DEBUG rule1 - Matched Medicaid")
        if "well medicare" in insurance:
            print("DEBUG rule1 - Matched Well Medicare")
        
        if ("nj medicare" in insurance or 
            insurance == "medicaid" or 
            "well medicare" in insurance):
            print("DEBUG rule1 - Rule triggered")
            return {
                "error": "Non-billable: S9088/S9083 for NJ Medicare/Medicaid",
                "correction": "Remove these codes for these insurances"
            }
    print("DEBUG rule1 - No match")
    return {}

def rule2(row: pd.Series) -> dict:
    # Rule 2: 99072, 99070, S9083, or S9088 are non-billable for United Healthcare or Oxford
    if row['Proc'] in {"99072", "S9083", "S9088", "99051", "A9150", "A4565", "97803"} and row['Insurance'].strip().lower() in {"united healthcare", "oxford"}:
        return {
            "error": "Non-billable: Code not allowed for United Healthcare/Oxford",
            "correction": "Remove these codes for these insurances"
        }
    return {}

def rule3(row: pd.Series) -> dict:
    # Rule 3: CPT 86326 should be replaced with 86325
    if row['Proc'] == "86326":
        return {
            "error": "Replace CPT 86326 with CPT 86325",
            "correction": "Use CPT 86325 instead"
        }
    return {}

def rule4(row: pd.Series) -> dict:
    # Rule 4: CPT 87628 should be replaced with 87625
    if row['Proc'] == "87628":
        return {
            "error": "Replace CPT 87628 with CPT 87625",
            "correction": "Use CPT 87625 instead"
        }
    return {}

def rule5(row: pd.Series) -> dict:
    # Rule 5: CPT 87877 should be replaced with 87880
    if row['Proc'] == "87877":
        return {
            "error": "Replace CPT 87877 with CPT 87880",
            "correction": "Use CPT 87880 instead"
        }
    return {}

def rule6(row: pd.Series) -> dict:
    # Rule 6: S9083 must be non-billable for Horizon BCBS - Federal
    if row['Proc'] == "S9083" and row['Insurance'].strip().lower() == "horizon bcbs - federal":
        return {
            "error": "Non-billable: S9083 for Horizon BCBS - Federal",
            "correction": "Remove this code"
        }
    return {}

def rule7(row: pd.Series) -> dict:
    # Rule 7: 90788 or 90772 should be replaced with 96372
    if row['Proc'] in {"90788", "90772"}:
        return {
            "error": "Replace with CPT 96372",
            "correction": "Use CPT 96372 instead"
        }
    return {}

def rule8(row: pd.Series) -> dict:
    # Rule 8: For Horizon Blue CrossBL, if CPT is S9083 or 87426, Dx should include Z71.1 and Z20.822
    if row['Proc'] in {"S9083", "87426"} and row['Insurance'].strip().lower() == "horizon blue crossbl":
        if not ("z71.1" in row['Dx'].lower() and "z20.822" in row['Dx'].lower()):
            return {
                "error": "DX Z71.1, Z20.822 required for Horizon Blue CrossBL",
                "correction": "Add DX Z71.1 and Z20.822"
            }
    return {}

# def rule9(row: pd.Series) -> dict:
#     # List of CPT codes that require the QW modifier
#     cpt_codes_requiring_qw = {"87426", "87811", "81025", "81003", "87635", "87804"}

#     # Check if the procedure code is in the list and does not have the QW modifier
#     if str(row['Proc']) in cpt_codes_requiring_qw:
#         if "qw" not in str(row['Mod']).lower():
#             return {
#                 "error": f"QW modifier required for CPT {row['Proc']}",
#                 "correction": "Add QW modifier"
#             }
#     return {}

def rule10(row: pd.Series) -> dict:
    # Rule 10: For Horizon Blue CrossBL, if CPT is S9083, modifier must include CS
    if row['Proc'] == "S9083" and row['Insurance'].strip().lower() == "horizon blue crossbl":
        if "cs" not in str(row['Mod']).lower():
            return {
                "error": "CS modifier required for CPT S9083",
                "correction": "Add CS modifier"
            }
    return {}

def rule11(row: pd.Series) -> dict:
    # Rule 11: CPT 90715 must be billed with Admin code 90471
    proc = str(row['Proc']).strip()
    description = str(row.get('Description', '')).lower()
    
    if proc == "90715":
        print(f"DEBUG rule11 - Checking 90715 with description: '{description}'")  # Debug output
        if "90471" not in description:
            return {
                "error": "Admin code 90471 missing for CPT 90715",
                "correction": "Add Admin code 90471"
            }
    return {}

def rule12(group: pd.DataFrame) -> dict:
    # Rule 12 (group-level): If any row in the encounter has one of these J codes and description "Injection"
    # then at least one row in the group must have CPT 96372.
    j_codes = {"J3420", "J1100", "J1094", "J1885", "J2405"}
    has_j_injection = group.apply(lambda r: r['Proc'] in j_codes and "injection" in r['Description'].lower(), axis=1).any()
    has_96372 = (group['Proc'] == "96372").any()
    if has_j_injection and not has_96372:
        return {
            "error": "Missing accompanying CPT 96372 for Injection",
            "correction": "Add CPT 96372"
        }
    return {}

def rule13(row: pd.Series) -> dict:
    # Rule 13: 99072 can only be used for BCBS insurance, not for other commercial insurances
    proc = str(row['Proc']).strip()
    insurance = str(row['Insurance']).strip().lower()
    if proc == "99072":
        # Allow only for BCBS (Blue Cross Blue Shield) insurances
        if "bcbs" not in insurance and "blue cross" not in insurance:
            return {
                "error": "CPT 99072 is only allowed for BCBS insurance",
                "correction": "Remove CPT 99072 for non-BCBS insurance"
            }
    return {}
