from typing import List, Tu<PERSON>, Dict
from .utils import map_segment
from datetime import datetime
from zoneinfo import ZoneInfo

# Practice-specific constants
GROUP_NPI = "**********"  # Functional Skills Group Group NPI
PRACTICE_TAX_ID = "*********"
BCCHP_PROVIDER_NPI = "Kiah Bouie NPI here"  # Add the correct NPI for Kiah Bouie
MERIDIAN_TREATING_PROVIDER_NPI = "Meridian Treating Provider NPI here"
CURRENT_DATE = datetime.now().replace(tzinfo=None)  # Current date

def validate_bbs_bcchp_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 1: BCCHP claims should be submitted under <PERSON><PERSON>'s NPI."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    is_bcchp = any(seg.get("Name Last or Organization Name", "").upper() == "BCCHP" for seg in mapped_all if seg.get("Entity Identifier Code") == "PR")
    
    if is_bcchp:
        billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
        if billing_npi != BCCHP_PROVIDER_NPI:
            errors.append((
                f"BCCHP claim uses Billing NPI {billing_npi} instead of Kiah Bouie’s NPI {BCCHP_PROVIDER_NPI}",
                f"Use Kiah Bouie’s NPI {BCCHP_PROVIDER_NPI} for BCCHP claims"
            ))
    return errors

def validate_bbs_meridian_treating_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: Meridian claims should be submitted under the treating provider's NPI and taxonomy."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    is_meridian = any(seg.get("Name Last or Organization Name", "").upper() == "MERIDIAN" for seg in mapped_all if seg.get("Entity Identifier Code") == "PR")
    
    if is_meridian:
        billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
        treating_npi = next((seg.get("Rendering Provider Identifier") for seg in mapped_all if seg.get("Entity Identifier Code") == "82"), None)
        
        if billing_npi != MERIDIAN_TREATING_PROVIDER_NPI:
            errors.append((
                f"Meridian claim uses Billing NPI {billing_npi} instead of Treating Provider NPI {MERIDIAN_TREATING_PROVIDER_NPI}",
                f"Use Treating Provider NPI {MERIDIAN_TREATING_PROVIDER_NPI} for Meridian claims"
            ))
        
        # Validate the taxonomy if provided
        treating_taxonomy = next((seg.get("Provider Taxonomy Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "82"), None)
        if treating_taxonomy:
            # Check if taxonomy is correct for Meridian (Add the correct taxonomy here)
            valid_taxonomies = ["XXXX", "YYYY"]  # Add valid taxonomies for Meridian
            if treating_taxonomy not in valid_taxonomies:
                errors.append((
                    f"Meridian claim uses invalid Taxonomy {treating_taxonomy} for Treating Provider NPI {MERIDIAN_TREATING_PROVIDER_NPI}",
                    "Ensure correct Taxonomy code for Meridian claims"
                ))
    
    return errors

def validate_bbs_only_9_codes(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: Only 9-codes are valid, no H-codes should be submitted."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    # print(f"Mapped Claim Segments: {mapped_all}")
    # Check for any codes starting with 'H' in the Service Identification
    for seg in mapped_all:
        if "Service Identification" in seg:
            service_id = seg["Service Identification"]
            # print(f"Checking Service Identification: {service_id}")
            # Extract the actual code (after HC: or other prefixes)
            parts = service_id.split(":")
            # print(f"Split Service Identification: {parts}")
            if len(parts) > 1:
                code = parts[1]  # This should be the code like 97153
                # print(f"Extracted Code: {code}")
                if code.startswith("H"):
                    errors.append((
                        f"Invalid code: {code}. Code must start with '9', not 'H'.",
                        "Ensure the code starts with '9' instead of 'H'."
                    ))

    return errors



def validate_bbs_functional_skills_group_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Functional Skills Group-specific validation rules."""
    return (
        validate_bbs_bcchp_provider(claim) +
        validate_bbs_meridian_treating_provider(claim) +
        validate_bbs_only_9_codes(claim)
    )
