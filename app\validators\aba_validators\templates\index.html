<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ABA EDI Validator</title>
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ url_for('static', path='favicon.png') }}"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM"
      crossorigin="anonymous"
    />
    <style>
      /* Define CSS variables for light theme (default) */
      :root {
        --bg-color: #f8f9fa;
        --text-color: #212529;
        --card-bg: #ffffff;
        --table-bg: #ffffff;
        --table-text: #212529;
        --table-header-bg: #343a40;
        --table-header-text: #ffffff;
        --alert-success-bg: #d4edda;
        --alert-success-text: #155724;
        --btn-primary-bg: #007bff;
        --btn-primary-hover: #0056b3;
      }
      /* Override variables for dark theme */
      body.dark-theme {
        --bg-color: #212529;
        --text-color: #f8f9fa;
        --card-bg: #343a40;
        --table-bg: #343a40;
        --table-text: #f8f9fa;
        --table-header-bg: #495057;
        --table-header-text: #f8f9fa;
        --alert-success-bg: #155724;
        --alert-success-text: #d4edda;
        --btn-primary-bg: #0056b3;
        --btn-primary-hover: #003d82;
      }
      /* Apply variables to elements */
      body {
        background-color: var(--bg-color);
        color: var(--text-color);
        font-family: "Arial", sans-serif;
      }
      .container {
        max-width: 900px;
        margin-top: 50px;
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .card {
        background-color: var(--card-bg);
      }
      .table {
        background-color: var(--table-bg);
        color: var(--table-text);
      }
      .table thead {
        background-color: var(--table-header-bg);
        color: var(--table-header-text);
      }
      .table-container {
        margin-top: 20px;
      }
      .alert-success {
        margin-top: 20px;
        background-color: var(--alert-success-bg);
        color: var(--alert-success-text);
      }
      .btn-primary {
        background-color: var(--btn-primary-bg);
        border: none;
      }
      .btn-primary:hover {
        background-color: var(--btn-primary-hover);
      }
      td {
        white-space: normal;
        word-wrap: break-word;
        max-width: 300px;
      }
      .logo {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 25px;
      }
      .logo img {
        width: 25%;
      }
      .theme-toggle {
        position: absolute;
        top: 20px;
        right: 20px;
      }
      .daily-counter {
        position: absolute;
        top: 20px;
        left: 20px;
        z-index: 10; /* Ensure it stays above other elements */
      }

      .counter-badge {
        display: inline-block;
        padding: 8px 16px;
        font-size: 1.1rem;
        font-weight: bold;
        background-color: var(--btn-primary-bg);
        color: white;
        border-radius: 25px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease, background-color 0.3s ease;
      }

      .counter-badge:hover {
        background-color: var(--btn-primary-hover);
        transform: scale(1.05);
      }
      @keyframes pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.1);
        }
        100% {
          transform: scale(1);
        }
      }
      .counter-badge.updated {
        animation: pulse 0.5s ease;
      }
    </style>
  </head>
  <body>
    <!-- Daily counter -->
    <div class="daily-counter">
      <span id="counterBadge" class="counter-badge">Loading...</span>
    </div>
    <!-- Theme toggle button -->
    <div class="theme-toggle">
      <button id="themeToggleBtn" class="btn btn-secondary">
        Toggle Theme
      </button>
    </div>
    <div class="logo">
      <img
        src="{{ url_for('static', path='therapypm-logo.svg') }}"
        alt="Logo"
      />
    </div>
    <div class="container">
      <div class="header">
        <h1 class="display-5 fw-bold">837 EDI Validator</h1>
        <p class="lead">Upload your 837 EDI file to validate claims</p>
      </div>
      <div class="card p-4 shadow-sm">
        <form id="uploadForm" enctype="multipart/form-data">
          <div class="mb-3">
            <label for="fileInput" class="form-label">Select EDI File</label>
            <input
              type="file"
              class="form-control"
              id="fileInput"
              name="file"
              accept=".txt"
              required
            />
          </div>
          <button type="submit" class="btn btn-primary w-100">Validate</button>
        </form>
      </div>
      <div id="results" class="table-container">
        <!-- Table will be populated here -->
      </div>
      <div id="successMessage" class="alert alert-success d-none" role="alert">
        No errors found in the uploaded file!
      </div>
    </div>

    <!-- Bootstrap JS and custom script -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz"
      crossorigin="anonymous"
    ></script>
    <script>
      // Theme toggle functionality
      const themeToggleBtn = document.getElementById("themeToggleBtn");
      const body = document.body;

      // Load saved theme from localStorage
      const savedTheme = localStorage.getItem("theme");
      if (savedTheme) {
        body.classList.add(savedTheme);
      }

      // Toggle theme on button click
      themeToggleBtn.addEventListener("click", () => {
        if (body.classList.contains("dark-theme")) {
          body.classList.remove("dark-theme");
          localStorage.setItem("theme", "");
        } else {
          body.classList.add("dark-theme");
          localStorage.setItem("theme", "dark-theme");
        }
      });

      // Fetch and display daily counter
      async function updateDailyCounter() {
        try {
          const response = await fetch("/get-daily-count");
          const data = await response.json();
          const badge = document.getElementById("counterBadge");
          badge.textContent = `${data.count} Files Today (${data.date})`;
          badge.classList.add("updated");
          setTimeout(() => badge.classList.remove("updated"), 500);
        } catch (err) {
          console.error("Error fetching daily count:", err);
          document.getElementById("counterBadge").textContent = "Error";
        }
      }

      // Update counter on page load
      document.addEventListener("DOMContentLoaded", () => {
        updateDailyCounter();
      });

      // Existing form submission logic
      document
        .getElementById("uploadForm")
        .addEventListener("submit", async (e) => {
          e.preventDefault();
          const fileInput = document.getElementById("fileInput");
          const file = fileInput.files[0];
          if (!file) return;

          const formData = new FormData();
          formData.append("file", file);

          try {
            const response = await fetch("/validate-837", {
              method: "POST",
              body: formData,
            });
            const data = await response.json();

            const resultsDiv = document.getElementById("results");
            const successDiv = document.getElementById("successMessage");
            resultsDiv.innerHTML = "";
            successDiv.classList.add("d-none");

            if (data.errors && data.errors.length > 0) {
              const table = `
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th scope="col">Claim#</th>
                                        <th scope="col">Errors</th>
                                        <th scope="col">Severity</th>
                                        <th scope="col">Solution</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.errors
                                      .map(
                                        (error) => `
                                        <tr>
                                            <td>${error.claim_id}</td>
                                            <td>${error.error}</td>
                                            <td><span class="badge bg-${
                                              error.severity === "High"
                                                ? "danger"
                                                : "warning"
                                            }">${error.severity}</span></td>
                                            <td>${
                                              error.solution ||
                                              "No solution provided"
                                            }</td>
                                        </tr>
                                    `
                                      )
                                      .join("")}
                                </tbody>
                            </table>
                        </div>
                    `;
              resultsDiv.innerHTML = table;
            } else if (data.message) {
              successDiv.classList.remove("d-none");
            }
            updateDailyCounter();
          } catch (err) {
            alert("Error uploading file: " + err.message);
          }
        });
    </script>
  </body>
</html>
