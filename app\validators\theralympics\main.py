import logging
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from typing import Dict, <PERSON>, Tuple, Set
import uvicorn
import collections
import json
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
import os
from datetime import datetime, date
from dotenv import load_dotenv


load_dotenv()

# Configuration from .env
SMTP_SERVER = os.getenv("SMTP_SERVER")
SMTP_PORT = int(os.getenv("SMTP_PORT"))
SENDER_EMAIL = os.getenv("SENDER_EMAIL")
SENDER_LOGIN = os.getenv("SENDER_LOGIN")
SENDER_PASSWORD = os.getenv("SENDER_PASSWORD")
RECEIVER_EMAILS = ["<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
HOST = os.getenv("HOST")
PORT = int(os.getenv("PORT"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)
app = FastAPI()
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")


import sqlite3
from datetime import date

# Database connection helper
def get_db_connection():
    conn = sqlite3.connect('validation_counts.db')
    conn.row_factory = sqlite3.Row
    return conn

# Initialize the database with a table for daily counts
def init_db():
    conn = get_db_connection()
    conn.execute('''
        CREATE TABLE IF NOT EXISTS daily_counts (
            date TEXT PRIMARY KEY,
            count INTEGER
        )
    ''')
    conn.execute('''
        CREATE TABLE IF NOT EXISTS file_validation_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT,
            validation_time DATETIME,
            total_errors INTEGER,
            error_breakdown TEXT
        )
    ''')
    conn.commit()
    conn.close()

def save_validation_stats(filename: str, total_errors: int, error_breakdown: Dict[str, int]):
    conn = get_db_connection()
    conn.execute('''
        INSERT INTO file_validation_stats 
        (filename, validation_time, total_errors, error_breakdown)
        VALUES (?, ?, ?, ?)
    ''', (filename, datetime.now().isoformat(), total_errors, json.dumps(dict(error_breakdown))))
    conn.commit()
    conn.close()

def get_daily_stats(date: str) -> Dict:
    conn = get_db_connection()
    
    count_row = conn.execute('SELECT count FROM daily_counts WHERE date = ?', (date,)).fetchone()
    total_validations = count_row['count'] if count_row else 0
    
    file_stats = conn.execute('''
        SELECT filename, total_errors, error_breakdown 
        FROM file_validation_stats 
        WHERE DATE(validation_time) = DATE(?)
    ''', (date,)).fetchall()
    
    conn.close()
    
    def safe_json_loads(data):
        if not data or data.strip() == '':
            return {}
        try:
            return json.loads(data)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in error_breakdown: '{data}', error: {str(e)}")
            return {}
    
    return {
        'total_validations': total_validations,
        'file_stats': [
            {
                'filename': row['filename'],
                'total_errors': row['total_errors'],
                'error_breakdown': safe_json_loads(row['error_breakdown'])
            } for row in file_stats
        ]
    }

# Get the current day's validation count
def get_daily_count():
    today = date.today().isoformat()
    conn = get_db_connection()
    row = conn.execute('SELECT count FROM daily_counts WHERE date = ?', (today,)).fetchone()
    conn.close()
    return row['count'] if row else 0

# Increment the daily count
def increment_daily_count():
    today = date.today().isoformat()
    conn = get_db_connection()
    row = conn.execute('SELECT count FROM daily_counts WHERE date = ?', (today,)).fetchone()
    new_count = (row['count'] if row else 0) + 1
    conn.execute('INSERT OR REPLACE INTO daily_counts (date, count) VALUES (?, ?)', (today, new_count))
    conn.commit()
    conn.close()

# Email sending function
def send_daily_report():
    """Sends detailed daily validation report using AWS SES"""
    try:
        today = date.today().isoformat()
        stats = get_daily_stats(today)
        
        msg = MIMEMultipart('alternative')
        msg["From"] = SENDER_EMAIL
        msg["To"] = ", ".join(RECEIVER_EMAILS)
        msg["Subject"] = f"Daily Theralympics Validation Report - {today}"

        # Plain text fallback
        plain_body = f"""
        Daily Theralympics Validation Report - {today}
        {'=' * 40}
        Summary:
        - Total files validated: {stats['total_validations']}
        - Total files with errors: {sum(1 for f in stats['file_stats'] if f['total_errors'] > 0)}
        - Total errors detected: {sum(f['total_errors'] for f in stats['file_stats'])}
        
        File Details:
        """
        for file_stat in stats['file_stats']:
            plain_body += f"\nFile: {file_stat['filename']}\n- Total errors: {file_stat['total_errors']}\n- Error breakdown:"
            for error_type, count in file_stat['error_breakdown'].items():
                plain_body += f"\n  - {error_type}: {count}"
        
        error_counts = collections.defaultdict(int)
        for file_stat in stats['file_stats']:
            for error_type, count in file_stat['error_breakdown'].items():
                error_counts[error_type] += count
        plain_body += f"\n\nError Type Analysis:\n{'-' * 40}"
        for error_type, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True):
            plain_body += f"\n- {error_type}: {count} occurrences"
        plain_body += f"\n\n{'=' * 40}\nGenerated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # Enhanced HTML body (unchanged, included for completeness)
        html_body = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f4f7fa;
                    color: #333;
                }}
                .container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: #ffffff;
                    border-radius: 10px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    padding: 20px;
                }}
                h1 {{
                    color: #1a73e8;
                    font-size: 28px;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #1a73e8;
                    padding-bottom: 10px;
                }}
                .summary {{
                    background-color: #e8f0fe;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                }}
                .summary table {{
                    width: 100%;
                    border-collapse: collapse;
                }}
                .summary th, .summary td {{
                    padding: 10px;
                    text-align: left;
                }}
                .summary th {{
                    background-color: #d1e3ff;
                    color: #1a73e8;
                }}
                details {{
                    margin-bottom: 15px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: #fff;
                }}
                summary {{
                    padding: 12px;
                    cursor: pointer;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                    font-weight: bold;
                    color: #34495e;
                }}
                summary:hover {{
                    background-color: #f1f1f1;
                }}
                details[open] summary {{
                    border-bottom: 1px solid #ddd;
                }}
                .file-details {{
                    padding: 15px;
                }}
                .file-details table {{
                    width: 100%;
                    border-collapse: collapse;
                }}
                .file-details th, .file-details td {{
                    padding: 8px;
                    border-bottom: 1px solid #eee;
                }}
                .file-details th {{
                    background-color: #f4f7fa;
                    color: #555;
                }}
                .error-count {{
                    color: #e74c3c;
                    font-weight: bold;
                }}
                .error-analysis {{
                    margin-top: 20px;
                }}
                .error-analysis table {{
                    width: 100%;
                    border-collapse: collapse;
                }}
                .error-analysis th, .error-analysis td {{
                    padding: 10px;
                    border-bottom: 1px solid #ddd;
                }}
                .error-analysis th {{
                    background-color: #ecf0f1;
                    color: #34495e;
                }}
                .footer {{
                    font-size: 12px;
                    color: #7f8c8d;
                    text-align: center;
                    margin-top: 20px;
                    padding-top: 10px;
                    border-top: 1px solid #eee;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Daily Therealymics Validation Report - {today}</h1>
                <div class="summary">
                    <h2>Summary</h2>
                    <table>
                        <tr><th>Total Files Validated</th><td>{stats['total_validations']}</td></tr>
                        <tr><th>Files with Errors</th><td>{sum(1 for f in stats['file_stats'] if f['total_errors'] > 0)}</td></tr>
                        <tr><th>Total Errors Detected</th><td class="error-count">{sum(f['total_errors'] for f in stats['file_stats'])}</td></tr>
                    </table>
                </div>
                <h2>File Details</h2>
                {"".join([
                    f'''
                    <details>
                        <summary>File: {file_stat['filename']} ({file_stat['total_errors']} errors)</summary>
                        <div class="file-details">
                            <p>Total Errors: <span class="error-count">{file_stat['total_errors']}</span></p>
                            <table>
                                <tr><th>Error Type</th><th>Count</th></tr>
                                {"".join([f'<tr><td>{error_type}</td><td>{count}</td></tr>' for error_type, count in file_stat['error_breakdown'].items()])}
                            </table>
                        </div>
                    </details>
                    ''' for file_stat in stats['file_stats']
                ])}
                <div class="error-analysis">
                    <h2>Error Type Analysis</h2>
                    <table>
                        <tr><th>Error Type</th><th>Occurrences</th></tr>
                        {"".join([f'<tr><td>{error_type}</td><td>{count}</td></tr>' for error_type, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True)])}
                    </table>
                </div>
                <div class="footer">
                    Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                </div>
            </div>
        </body>
        </html>
        """

        msg.attach(MIMEText(plain_body, 'plain'))
        msg.attach(MIMEText(html_body, 'html'))

        # Send email using AWS SES
        # response = ses_client.send_raw_email(
        #     Source=SENDER_EMAIL,
        #     Destinations=RECEIVER_EMAILS,
        #     RawMessage={
        #         'Data': msg.as_string()
        #     }
        # )

        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(SENDER_LOGIN, SENDER_PASSWORD)
            server.sendmail(SENDER_EMAIL, RECEIVER_EMAILS, msg.as_string())
            logger.info(f"Successfully sent daily report to {', '.join(RECEIVER_EMAILS)} via SMTP")
        
        logger.info(f"Successfully sent daily report to {', '.join(RECEIVER_EMAILS)}.")
    
    except Exception as e:
        logger.error(f"Failed to send daily report: {str(e)}")
        raise
# Initialize scheduler
scheduler = BackgroundScheduler()

def reset_daily_count():
    today = date.today().isoformat()
    conn = get_db_connection()
    conn.execute('DELETE FROM daily_counts WHERE date < ?', (today,))
    conn.commit()
    conn.close()
    logger.info(f"Daily count reset for {today}")

@app.on_event("startup")
def startup_event():
    init_db()
    scheduler.add_job(
        send_daily_report,
        trigger=CronTrigger(hour=23, minute=55, timezone="Asia/Kolkata"),
        name="daily_report"
    )
    scheduler.add_job(
        reset_daily_count,
        trigger=CronTrigger(hour=0, minute=0, timezone="Asia/Kolkata"),
        name="reset_daily_count"
    )
    scheduler.start()

# Segment element names dictionary
segment_element_names = {
    "ISA": ["Authorization Information Qualifier", "Authorization Information", "Security Information Qualifier", "Security Information", "Interchange ID Qualifier", "Interchange Sender ID", "Interchange ID Qualifier", "Interchange Receiver ID", "Interchange Date", "Interchange Time", "Interchange Control Standards Identifier", "Interchange Control Version Number", "Interchange Control Number", "Acknowledgment Requested", "Usage Indicator", "Component Element Separator"],
    "GS": ["Functional Identifier Code", "Application Sender's Code", "Application Receiver's Code", "Date", "Time", "Group Control Number", "Responsible Agency Code", "Version/Release/Industry Identifier Code"],
    "ST": ["Transaction Set Identifier Code", "Transaction Set Control Number", "Implementation Convention Reference"],
    "BHT": ["Hierarchical Structure Code", "Transaction Set Purpose Code", "Reference Identification", "Date", "Time", "Transaction Type Code"],
    "NM1": ["Entity Identifier Code", "Entity Type Qualifier", "Name Last or Organization Name", "Name First", "Name Middle", "Name Prefix", "Name Suffix", "Identification Code Qualifier", "Identification Code", "Entity Relationship Code", "Entity Identifier Code"],
    "PER": ["Contact Function Code", "Name", "Communication Number Qualifier", "Communication Number", "Communication Number Qualifier", "Communication Number", "Communication Number Qualifier", "Communication Number"],
    "HL": ["Hierarchical ID Number", "Hierarchical Parent ID Number", "Hierarchical Level Code", "Hierarchical Child Code"],
    "PRV": ["Provider Code", "Reference Identification Qualifier", "Reference Identification", "State or Province Code"],
    "SBR": ["Payer Responsibility Sequence Number Code", "Individual Relationship Code", "Insured Group or Policy Number", "Insured Group Name", "Insurance Type Code", "Coordination of Benefits Code", "Yes/No Condition or Response Code", "Employment Status Code", "Claim Filing Indicator Code"],
    "DMG": ["Date Time Period Format Qualifier", "Date Time Period", "Gender Code", "Marital Status Code", "Race or Ethnicity Code", "Citizenship Status Code", "Country Code"],
    "CLM": ["Claim Submitter's Identifier", "Monetary Amount", "Claim Frequency Type Code", "Yes/No Condition or Response Code", "Provider Accept Assignment Code", "Yes/No Condition or Response Code", "Release of Information Code", "Patient Status Code"],
    "REF": ["Reference Identification Qualifier", "Reference Identification", "Description"],
    "HI": ["Diagnosis Code", "Diagnosis Code2", "Diagnosis Code3", "Diagnosis Code4", "Diagnosis Code5", "Diagnosis Code6"],
    "LX": ["Assigned Number"],
    "SV1": ["Service Identification", "Monetary Amount", "Service Type Code", "Units/Days", "Place of Service", "Procedure Code", "Procedure Modifier", "Procedure Modifier2", "Quantity", "Unit or Basis for Measurement Code", "Service Type Code2", "Monetary Amount2", "Yes/No Condition or Response Code"],
    "DTP": ["Date/Time Qualifier", "Date Time Period Format Qualifier", "Date Time Period"],
    "N3": ["Address Information", "Additional Address Information"],
    "N4": ["City Name", "State or Province Code", "Postal Code"],
    "SE": ["Number of Included Segments", "Transaction Set Control Number"],
    "GE": ["Number of Transaction Sets Included", "Group Control Number"],
    "IEA": ["Number of Included Groups", "Interchange Control Number"]
}

# Helper Functions
def map_segment(segment: str) -> Dict[str, str]:
    parts = segment.split('*')
    seg_id = parts[0]
    mapped = {}
    if seg_id in segment_element_names:
        names = segment_element_names[seg_id]
        for i, value in enumerate(parts[1:]):
            key = names[i] if i < len(names) else f"Element_{i+1}"
            mapped[key] = value
    else:
        mapped = {f"Element_{i}": elem for i, elem in enumerate(parts[1:], start=1)}
    return mapped

def parse_hierarchy(segments: List[str]) -> List[Dict[str, any]]:
    claims = []
    hl_context = {}
    current_segments = []

    for segment in segments:
        if segment.startswith("HL"):
            if current_segments and hl_context:
                last_hl_id = list(hl_context.keys())[-1]
                hl_level = hl_context[last_hl_id]["level"]
                if hl_level in ["22", "23"]:
                    claim_segments = []
                    for seg in current_segments:
                        if seg.startswith("CLM"):
                            if claim_segments:
                                claims.append(build_claim(hl_context, last_hl_id, claim_segments))
                            claim_segments = [seg]
                        elif claim_segments:
                            claim_segments.append(seg)
                    if claim_segments:
                        claims.append(build_claim(hl_context, last_hl_id, claim_segments))
                current_segments = []

            parts = segment.split("*")
            hl_id = parts[1]
            hl_parent = parts[2] if parts[2] else None
            hl_level = parts[3]
            hl_child = parts[4].replace("~", "")
            hl_context[hl_id] = {
                "level": hl_level,
                "parent": hl_parent,
                "segments": [segment]
            }
        elif hl_context:
            last_hl_id = list(hl_context.keys())[-1]
            hl_context[last_hl_id]["segments"].append(segment)
            current_segments.append(segment)

    if current_segments and hl_context:
        last_hl_id = list(hl_context.keys())[-1]
        hl_level = hl_context[last_hl_id]["level"]
        if hl_level in ["22", "23"]:
            claim_segments = []
            for seg in current_segments:
                if seg.startswith("CLM"):
                    if claim_segments:
                        claims.append(build_claim(hl_context, last_hl_id, claim_segments))
                    claim_segments = [seg]
                elif claim_segments:
                    claim_segments.append(seg)
            if claim_segments:
                claims.append(build_claim(hl_context, last_hl_id, claim_segments))

    return claims

def build_claim(hl_context: Dict[str, Dict], hl_id: str, claim_segments: List[str]) -> Dict[str, any]:
    claim_hl = hl_context[hl_id]
    hl_level = claim_hl["level"]
    subscriber_hl = claim_hl if hl_level == "22" else hl_context.get(claim_hl["parent"], None)
    billing_provider_hl = None
    parent_id = claim_hl["parent"] if hl_level == "23" else subscriber_hl["parent"] if subscriber_hl else None
    while parent_id and parent_id in hl_context:
        if hl_context[parent_id]["level"] == "20":
            billing_provider_hl = hl_context[parent_id]
            break
        parent_id = hl_context[parent_id]["parent"]

    return {
        "billing_provider": billing_provider_hl["segments"] if billing_provider_hl else [],
        "subscriber": subscriber_hl["segments"] if subscriber_hl else [],
        "patient": claim_hl["segments"] if hl_level == "23" else subscriber_hl["segments"] if subscriber_hl else [],
        "claim_segments": claim_segments
    }

def identify_insurance(claim: Dict[str, List[str]]) -> str:
    mapped_subscriber = [map_segment(seg) for seg in claim["subscriber"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]

    primary_sbr_index = None
    for i, seg in enumerate(mapped_subscriber):
        if seg.get("Payer Responsibility Sequence Number Code") == "P":
            primary_sbr_index = i
            break

    if primary_sbr_index is None:
        return "Unknown"

    primary_payer = None
    for seg in mapped_subscriber[primary_sbr_index + 1:]:
        if seg.get("Entity Identifier Code") == "PR":
            primary_payer = seg
            break
    if not primary_payer:
        for seg in mapped_claim_segments:
            if seg.get("Entity Identifier Code") == "PR":
                primary_payer = seg
                break
    if not primary_payer:
        return "Unknown"

    id_code = primary_payer.get("Identification Code", "").strip()
    name = primary_payer.get("Name Last or Organization Name", "").upper()

    subscriber_id = ""
    for seg in mapped_subscriber:
        if seg.get("Entity Identifier Code") == "IL":
            subscriber_id = seg.get("Identification Code", "").strip()
            break

    if "MEDICARE" in name:
        return "Medicare"
    elif id_code == "60054":
        return "Aetna"
    elif "BCBS MEDICAID" in name:
        return "BCBS Medicaid"
    elif "HEALTH FIRST" in name:
        return "Health First"
    elif "EMBLEM" in name or "GHI" in name:
        return "Emblem"
    elif "OXFORD" in name:
        return "Oxford"
    elif "NYSHIP" in name:
        return "NYSHIP"
    elif "MAGNACARE" in name:
        return "Magnacare"
    elif "UNITED HEALTHCARE" in name or "UHC" in name:
        if "COMMERCIAL" in name:
            return "UHC Commercial"
        else:
            return "UHC Community Plan"
    else:
        return "Unknown"

def extract_diagnosis_codes(mapped_all: List[Dict[str, str]]) -> Set[str]:
    diag_set = set()
    for seg in mapped_all:
        for key, value in seg.items():
            if key.startswith("Diagnosis Code") and value:
                # Split by ':' and take the code after the qualifier
                if ":" in value:
                    code = value.split(":")[-1].strip()
                else:
                    code = value.strip()
                # Normalize: remove spaces and periods, convert to uppercase
                norm = code.replace(" ", "").replace(".", "").upper()
                diag_set.add(norm)
    return diag_set

def has_secondary_insurance(claim: Dict[str, List[str]]) -> bool:
    mapped_subscriber = [map_segment(seg) for seg in claim["subscriber"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_subscriber + mapped_claim_segments:
        if seg.get("Payer Responsibility Sequence Number Code") == "S":
            return True
    return False

# Validation Functions

def validate_diagnosis_code_repetition_and_format(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]

    def has_duplicate_substring(s):
        length = len(s)
        for i in range(1, length // 2 + 1):
            if length % i == 0:
                part = s[:i]
                if part * (length // i) == s:
                    return True, part
        return False, None

    def has_multiple_alphabets(code):
        # Check if the code contains more than one alphabet
        alphabets = [char for char in code if char.isalpha()]
        return len(alphabets) > 1

    for seg in mapped_claim_segments:
        # Check all keys that look like diagnosis codes
        for key, value in seg.items():
            if key.startswith("Diagnosis Code") and value:
                # Value can be like "ABK:F840F840" or "ABF:F802"
                if ":" in value:
                    qualifier, code = value.split(":", 1)
                    is_dup, dup_part = has_duplicate_substring(code)
                    if is_dup:
                        correct_value = f"{qualifier}:{dup_part}"
                        errors.append(
                            (
                                f"Duplicate diagnosis code detected: '{value}'",
                                f"Replace with '{correct_value}' to remove duplication"
                            )
                        )
                    if has_multiple_alphabets(code):
                        errors.append(
                            (
                                f"Invalid diagnosis code format: '{value}'",
                                f"Diagnosis code '{code}' should contain only one alphabet"
                            )
                        )
    return errors

def validate_file_structure(segments: List[str]) -> List[Tuple[str, str]]:
    errors = []
    if not segments:
        errors.append(("File is empty", "Upload a valid 837 EDI file"))
        return errors
    if not segments[0].startswith("ISA"):
        errors.append(("File does not start with ISA segment", "Ensure the file begins with an ISA segment"))
    if not segments[-1].startswith("IEA"):
        errors.append(("File does not end with IEA segment", "Ensure the file ends with an IEA segment"))
    gs_count = sum(1 for seg in segments if seg.startswith("GS"))
    ge_count = sum(1 for seg in segments if seg.startswith("GE"))
    if gs_count < 1:
        errors.append(("Missing GS segment", "Add a GS segment to the file"))
    if ge_count < 1:
        errors.append(("Missing GE segment", "Add a GE segment to the file"))
    if gs_count != ge_count:
        errors.append((f"Mismatch in GS and GE counts: {gs_count} GS, {ge_count} GE", "Ensure each GS segment has a corresponding GE segment"))
    st_count = sum(1 for seg in segments if seg.startswith("ST"))
    se_count = sum(1 for seg in segments if seg.startswith("SE"))
    if st_count < 1:
        errors.append(("Missing ST segment", "Add an ST segment to the file"))
    if se_count < 1:
        errors.append(("Missing SE segment", "Add an SE segment to the file"))
    if st_count != se_count:
        errors.append((f"Mismatch in ST and SE counts: {st_count} ST, {se_count} SE", "Ensure each ST segment has a corresponding SE segment"))
    if not any(seg.startswith("BHT") for seg in segments):
        errors.append(("Missing BHT segment", "Add a BHT segment to the file"))
    return errors

def validate_claim_group(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    if has_secondary_insurance(claim):
        return []
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]

    if not any("Claim Submitter's Identifier" in seg for seg in mapped_claim_segments):
        errors.append(("Missing CLM segment", "Add a CLM segment to the claim with all required elements"))
    if not any("Service Identification" in seg for seg in mapped_claim_segments):
        errors.append(("Missing service lines (SV1 segment)", "Add at least one SV1 segment for the claim"))
    provider_found = False
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") in ["82", "85"]:
            id_code = seg.get("Identification Code", "").strip()
            if id_code:
                provider_found = True
                break
            else:
                errors.append((f"Provider NM1*{seg.get('Entity Identifier Code')} present but missing valid Identification Code", "Ensure the provider NM1 segment has a valid Identification Code"))
    if not provider_found:
        errors.append(("Missing provider information (NM1 with 82 or 85) with valid Identification Code", "Add a provider NM1 segment (82 or 85) with a valid Identification Code"))
    dtp_found = False
    for seg in mapped_claim_segments:
        if "Date Time Period" in seg and seg["Date Time Period"].strip():
            dtp_found = True
            break
    if not dtp_found:
        errors.append(("Missing DTP segment with a valid date", "Add a DTP segment with a valid date in the claim"))
    return errors

def validate_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    service_definitions = {
        "Speech": {"modifier": "GN", "evaluation": ["92521", "92522", "92523", "92524", "92610"], "treatment": ["92507", "92526", "92609"]},
        "OT": {"modifier": "GO", "evaluation": ["97165", "97166", "97167"], "treatment": ["97110", "97112", "97530", "97535"]},
        "PT": {"modifier": "GP", "evaluation": ["97161", "97162", "97163"], "treatment": ["97110", "97112", "97116", "97530"]}
    }
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    group_exception = any(seg.get("Entity Identifier Code") == "82" and seg.get("Identification Code") == "**********" for seg in mapped_all)
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            service_ident = seg["Service Identification"]
            parts = service_ident.split(":")
            if len(parts) < 3:
                errors.append((f"Invalid format: '{service_ident}'", "Ensure Service Identification has at least three elements separated by ':'"))
                continue
            cpt_code = parts[1]
            found_modifier = parts[2]
            service_type = next((stype for stype, defs in service_definitions.items() if cpt_code in defs["evaluation"] or cpt_code in defs["treatment"]), None)
            if service_type:
                expected_modifier = service_definitions[service_type]["modifier"]
                if service_type == "PT" and group_exception and found_modifier == "GO":
                    continue
                if found_modifier != expected_modifier:
                    errors.append((f"For CPT {cpt_code} ({service_type}), expected modifier '{expected_modifier}' but found '{found_modifier}'", f"Update the modifier for CPT {cpt_code} to '{expected_modifier}'"))
            else:
                errors.append((f"CPT code {cpt_code} is not recognized for Speech/OT/PT services", "Verify the CPT code and ensure it is correct for the service type"))
    return errors

def validate_pos_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg and seg.get("Place of Service") == "02":
            service_ident = seg["Service Identification"]
            parts = service_ident.split(":")
            if len(parts) < 4:
                errors.append((f"Service Identification '{service_ident}' does not contain enough elements for POS validation", "Ensure Service Identification has at least four elements for POS '02' services"))
            else:
                pos_modifier = parts[3]
                if pos_modifier != "95":
                    errors.append((f"For POS '02', expected '95' as 4th element in '{service_ident}', found '{pos_modifier}'", "Add '95' as the fourth element in the Service Identification for POS '02' services"))
    return errors

def validate_92609_92507_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    # Check if the patient is Matthew Solomito under Emblem insurance
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il = next((seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"), None)
    if nm1_il:
        first_name = nm1_il.get("Name First", "").upper()
        last_name = nm1_il.get("Name Last or Organization Name", "").upper()
        if identify_insurance(claim) == "Emblem" and "MATTHEW" in first_name and "SOLOMITO" in last_name:
            return errors  # Skip general validation for this patient

    # Existing general validation logic
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_entries = []
    for i, seg in enumerate(mapped_claim_segments):
        if "Service Identification" in seg:
            service_ident = seg["Service Identification"]
            parts = service_ident.split(":")
            if len(parts) < 3:
                errors.append((f"Invalid Service Identification format: '{service_ident}'", "Ensure Service Identification has at least three elements separated by ':'"))
                continue
            cpt_code = parts[1]
            service_date = next((mapped_claim_segments[j]["Date Time Period"] for j in range(i+1, len(mapped_claim_segments)) if "Date Time Period" in mapped_claim_segments[j]), None)
            if service_date:
                service_entries.append({"cpt": cpt_code, "date": service_date, "service_ident": service_ident})
    grouped = collections.defaultdict(list)
    for entry in service_entries:
        grouped[entry["date"]].append(entry)
    for date, entries in grouped.items():
        if any(e["cpt"] == "92609" for e in entries) and any(e["cpt"] == "92507" for e in entries):
            for e in entries:
                if e["cpt"] == "92507":
                    parts = e["service_ident"].split(":")
                    if "59" not in parts:
                        errors.append((f"On {date}: CPT 92507 with 92609 requires '59' modifier in '{e['service_ident']}'", f"Add '59' as a modifier in the Service Identification '{e['service_ident']}'"))
    return errors

def validate_ot_services(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    ot_cpt_codes = {"97110", "97112", "97530", "97535"}
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    ot_services = [seg for seg in mapped_claim_segments if "Service Identification" in seg and seg["Service Identification"].split(":")[1] in ot_cpt_codes]
    if ot_services:
        for svc in ot_services:
            if svc.get("Units/Days", "").strip() != "2":
                errors.append((f"OT service '{svc['Service Identification']}' has Units/Days='{svc.get('Units/Days')}', expected '2'", f"Update Units/Days to '2' for OT service '{svc['Service Identification']}'"))
        if not any(seg.get("Entity Identifier Code") == "82" and seg.get("Identification Code") == "**********" for seg in mapped_all):
            errors.append(("OT services require Jessica Hanna (NPI **********)", "Ensure the claim is billed under Jessica Hanna with NPI **********"))
    return errors

def validate_evaluation_treatment_units_and_price(claim: Dict[str, List[str]], insurance: str) -> List[Tuple[str, str]]:
    errors = []
    service_defs = {
        "Speech": {"evaluation": {"92521", "92522", "92523", "92524", "92610"}, "treatment": {"92507", "92526", "92609"}, "price_per_unit": 120},
        "OT": {"evaluation": {"97165", "97166", "97167"}, "treatment": {"97110", "97112", "97530", "97535"}, "price_per_unit": 100},
        "PT": {"evaluation": {"97161", "97162", "97163"}, "treatment": {"97110", "97112", "97116", "97530"}, "price_per_unit": 120}
    }
    if insurance == "Medicare":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    patient_name = "Unknown"
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") == "IL":
            first_name = seg.get("Name First", "").strip()
            last_name = seg.get("Name Last or Organization Name", "").strip()
            patient_name = f"{first_name} {last_name}".strip() if first_name or last_name else "Unknown"
            break
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            service_ident = seg["Service Identification"]
            parts = service_ident.split(":")
            if len(parts) < 3:
                errors.append((f"Invalid Service Identification format: '{service_ident}'", "Ensure Service Identification has at least three elements separated by ':'"))
                continue
            cpt_code = parts[1]
            try:
                units = float(seg.get("Units/Days", "").strip())
                amt = float(seg.get("Monetary Amount", "").strip())
            except ValueError:
                errors.append((f"Invalid Units/Days or Amount for '{service_ident}'", "Ensure Units/Days and Monetary Amount are valid numbers in '{service_ident}'"))
                continue
            service_type = next((stype for stype, defs in service_defs.items() if cpt_code in defs["evaluation"] or cpt_code in defs["treatment"]), None)
            if not service_type:
                continue
            if cpt_code in service_defs[service_type]["evaluation"]:
                if units != 1:
                    errors.append((f"Patient {patient_name} Primary Ins:({insurance}): Evaluation CPT {cpt_code} has {units} units; expected 1", f"Set Units/Days to 1 for CPT {cpt_code}"))
                if abs(amt - 250) > 0.001:
                    errors.append((f"Patient {patient_name} Primary Ins:({insurance}): Evaluation CPT {cpt_code} billed at ${amt}; expected $250", f"Adjust Monetary Amount to $250 for CPT {cpt_code}"))
                continue
            expected_amt = units * service_defs[service_type]["price_per_unit"]
            if abs(amt - expected_amt) > 0.001:
                errors.append((f"Patient {patient_name} Primary Ins({insurance}): Treatment CPT {cpt_code} with {units} units billed at ${amt}; expected ${expected_amt}", f"Adjust Monetary Amount to ${expected_amt} for {units} units of CPT {cpt_code}"))
    return errors

def validate_multile_speech_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != shared_insurances:
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    speech_cpt_codes = {"92507", "92609", "92526", "92521", "92522", "92523", "92524", "92610"}

    authorized_providers = {
        "**********": "Christiana M Neophytou",
        "**********": "Stephanie L Montano",
        "**********": "Jessica Hanna"
    }

    # Check if the claim involves speech CPT codes
    if any(
        "Service Identification" in seg and seg["Service Identification"].split(":")[1] in speech_cpt_codes
        for seg in mapped_claim_segments
    ):
        # Check if any authorized provider is present in the claim
        if not any(
            seg.get("Entity Identifier Code") == "82" and seg.get("Identification Code") in authorized_providers
            for seg in mapped_all
        ):
            provider_list = ", ".join(
                f"{name} (NPI {npi})" for npi, name in authorized_providers.items()
            )
            errors.append((
                "Aetna Speech services require billing under one of the authorized providers.",
                f"Ensure the claim is billed under one of the following providers: {provider_list}"
            ))

    return errors

def validate_aetna_speech_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Aetna":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    speech_cpt_codes = {"92507", "92609", "92526", "92521", "92522", "92523", "92524", "92610"}
    if any("Service Identification" in seg and seg["Service Identification"].split(":")[1] in speech_cpt_codes for seg in mapped_claim_segments):
        if not any(seg.get("Entity Identifier Code") == "82" and seg.get("Identification Code") == "**********" for seg in mapped_all):
            errors.append(("Aetna Speech services require Dr. Christiana (NPI **********)", "Ensure the claim is billed under Dr. Christiana with NPI **********"))
    return errors

def validate_aetna_samantha_dussel(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Aetna":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match_found = False
    for nm1_seg in nm1_il_segments:
        name_first = nm1_seg.get("Name First", "").upper()
        name_last = nm1_seg.get("Name Last or Organization Name", "").upper()
        if "SAMANTHA" in name_first or "DUSSEL" in name_last:
            name_match_found = True
            break
    if not name_match_found:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            cpt_code = parts[1]
            if cpt_code == "92509":
                errors.append(("For Aetna patient 'Samantha Dussel', do not bill CPT 92509", "Replace CPT 92509 with CPT 92507 or 92526"))
    return errors

def validate_aetna_jonathan_ortiz(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Aetna":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match_found = False
    for nm1_seg in nm1_il_segments:
        first = nm1_seg.get("Name First", "").upper()
        last = nm1_seg.get("Name Last or Organization Name", "").upper()
        if "JONATHAN" in first and "ORTIZ" in last:
            name_match_found = True
            break
    if not name_match_found:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            cpt_code = parts[1]
            if cpt_code == "92523" and "59" not in parts[2:]:
                errors.append(("For Aetna patient 'Jonathan Ortiz', CPT 92523 must include the '59' modifier", f"Add '59' modifier to the Service Identification for CPT 92523"))
    return errors

def validate_bcbs_medicaid_speech_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "BCBS Medicaid":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    speech_cpt_codes = {"92507", "92609", "92526", "92521", "92522", "92523", "92524", "92610"}
    if any("Service Identification" in seg and seg["Service Identification"].split(":")[1] in speech_cpt_codes for seg in mapped_claim_segments):
        if not any(seg.get("Entity Identifier Code") == "82" and seg.get("Identification Code") == "**********" for seg in mapped_all):
            errors.append(("BCBS Medicaid Speech requires Dr. Christiana Neophytou (NPI **********)", "Update the provider to Dr. Christiana Neophytou with NPI **********"))
    return errors

def validate_diagnoses_for_trigger_services(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "BCBS Medicaid":
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    trigger_cpt_codes = {"97530", "97110"}
    required_diagnoses = {"F802", "F840"}
    trigger_found = any("Service Identification" in seg and seg["Service Identification"].split(":")[1] in trigger_cpt_codes for seg in mapped_claim_segments)
    if trigger_found:
        diagnoses = {seg[key].split(":")[-1].strip().replace(".", "").upper() for seg in mapped_all for key in seg if key.startswith("Diagnosis Code") and seg[key]}
        missing = required_diagnoses - diagnoses
        if missing:
            errors.append((f"CPT 97530/97110 requires diagnoses F80.2 and F84.0; missing: {', '.join(missing)}", f"Add the missing diagnosis codes: {', '.join(missing)} to the claim"))
    return errors

def validate_bcbs_christian_sankar(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "BCBS Medicaid":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match_found = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "CHRISTIAN" in first and "SANKAR" in last:
            name_match_found = True
            break
    if not name_match_found:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            cpt_code = parts[1]
            if cpt_code in {"92507", "92606"}:
                errors.append((f"For BCBS patient 'Christian Sankar', do not bill CPT {cpt_code}. Use CPT 92609 instead", f"Replace CPT {cpt_code} with CPT 92609 and ensure CPT 92526 is included if appropriate"))
    return errors

def validate_92610_separation(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    other_codes = {"92523", "92524", "92521"}
    cpt_codes = {seg["Service Identification"].split(":")[1] for seg in mapped_claim_segments if "Service Identification" in seg}
    if "92610" in cpt_codes and cpt_codes & other_codes:
        errors.append((f"Health First: 92610 cannot be billed with {', '.join(cpt_codes & other_codes)}", f"Remove CPT 92610 or the conflicting CPT codes ({', '.join(cpt_codes & other_codes)}) from the claim"))
    return errors

def validate_claim_billed_under_dr_christiana(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    claim_id = next((seg.get("Claim Submitter's Identifier") for seg in [map_segment(seg) for seg in claim["claim_segments"]] if "Claim Submitter's Identifier" in seg), "Unknown")
    insurance = identify_insurance(claim)
    if insurance not in {"Health First", "Medicare", "NYSHIP"}:
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    speech_cpt_codes = {"92507", "92526", "92609", "92521", "92522", "92523", "92524", "92610"}
    ot_cpt_codes = {"97165", "97166", "97167", "97110", "97112", "97530", "97535"}
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    has_speech_service = any("Service Identification" in seg and seg["Service Identification"].split(":")[1] in speech_cpt_codes for seg in mapped_claim_segments)
    has_ot_service = any("Service Identification" in seg and seg["Service Identification"].split(":")[1] in ot_cpt_codes for seg in mapped_claim_segments)
    christiana_found = any(seg.get("Entity Identifier Code") == "82" and seg.get("Identification Code") == "**********" for seg in mapped_all)
    jessica_found = any(seg.get("Entity Identifier Code") == "82" and seg.get("Identification Code") == "**********" for seg in mapped_all)
    if insurance == "Health First" and has_speech_service:
        if not christiana_found:
            errors.append(("Health First speech services must be billed under Dr. Christiana (NPI **********)", "Update the provider to Dr. Christiana with NPI **********"))
    elif insurance in {"Medicare", "NYSHIP"}:
        if has_ot_service and not has_speech_service:
            if not jessica_found:
                errors.append((f"{insurance} OT service must be billed under Jessica Hanna (NPI **********)", "Update the provider to Jessica Hanna with NPI **********"))
        elif not christiana_found:
            errors.append((f"{insurance} claim must be billed under Dr. Christiana (NPI **********)", "Update the provider to Dr. Christiana with NPI **********"))
    return errors

def validate_pos_11_only(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Place of Service" in seg and seg["Place of Service"].strip() != "11":
            errors.append((f"Health First requires POS '11', found '{seg['Place of Service']}' in '{seg.get('Service Identification', 'N/A')}'", f"Update the Place of Service to '11' for the service '{seg.get('Service Identification', 'N/A')}'"))
    return errors

def validate_healthfirst_insurance_id_for_wyatt_jaden(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        id_code = seg.get("Identification Code", "").upper()
        if "WYATT" in first and "DIBLE" in last:
            if id_code != "FQ69574T":
                errors.append((f"For Health First patient 'Wyatt Michael Dible', insurance ID should be 'FQ69574T'. Found '{id_code}'", "Update the insurance ID to 'FQ69574T' for Wyatt Michael Dible"))
        elif "JADEN" in first and "ROSARIO" in last:
            if id_code != "GN90975H":
                errors.append((f"For Health First patient 'Jaden Rosario', insurance ID should be 'GN90975H'. Found '{id_code}'", "Update the insurance ID to 'GN90975H' for Jaden Rosario"))
    return errors

def validate_healthfirst_only_ot(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "LIAM" in first and "HORES" in last:
            name_match = True
            break
    if not name_match:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    allowed_ot = {"97165", "97166", "97167", "97110", "97112", "97530"}
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            cpt_code = parts[1]
            if cpt_code not in allowed_ot:
                errors.append((f"For Health First patient 'Liam Hores', only OT services should be billed. Found CPT {cpt_code}", f"Remove non-OT service CPT {cpt_code} from the claim or ensure only OT services are billed"))
    return errors

def validate_healthfirst_phoenix_prevalus_dx(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if ("PHOENIX" in first or "PHOENIX" in last) and ("PREVALUS" in first or "PREVALUS" in last):
            name_match = True
            break
    if not name_match:
        return errors
    diag_set = extract_diagnosis_codes(mapped_all)
    logger.info(f"Identified diagnosis codes for Phoenix Prevalus: {diag_set}")
    required = {"F802", "R6330"}
    missing = required - diag_set
    if missing:
        errors.append((
            f"For Health First patient 'Phoenix Prevalus', missing diagnosis codes: {', '.join(missing)} (required: F80.2, R63.30)",
            f"Add the missing diagnosis codes: {', '.join(missing)} to the claim"
        ))
    return errors

def validate_healthfirst_jacob_ortez_ronk_dx(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "JACOB" in first and "ORTEZ" in last and "RONK" in last:
            name_match = True
            break
    if not name_match:
        return errors
    dx_found = False
    for seg in mapped_all:
        for key, value in seg.items():
            if key.startswith("Diagnosis Code") and value:
                norm = value.replace(" ", "").replace(".", "").upper()
                if norm == "F8081":
                    dx_found = True
                    break
        if dx_found:
            break
    if not dx_found:
        errors.append(("For Health First patient 'Jacob Ortez Ronk', diagnosis code F80.81 must be billed", "Add diagnosis code F80.81 to the claim"))
    return errors

def validate_healthfirst_brianna_insurance(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        if "BRIANNA" in first:
            id_code = seg.get("Identification Code", "").upper()
            if id_code != "GW61719Y":
                errors.append((f"For Health First patient 'Brianna', insurance ID should be 'GW61719Y'. Found '{id_code}'", "Update the insurance ID to 'GW61719Y' for Brianna"))
    return errors

def validate_healthfirst_ryder_jacobson_remove_92507(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "RYDER" in first and "JACOBSON" in last:
            name_match = True
            break
    if not name_match:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            if parts[1] == "92507":
                errors.append(("For Health First patient 'Ryder Jacobson', CPT 92507 should be removed per updated billing instructions", "Remove CPT 92507 from the claim for Ryder Jacobson"))
    return errors

def validate_healthfirst_azeneth_ramos(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "AZENETH" in first and "RAMOS" in last:
            name_match = True
            break
    if not name_match:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            if parts[1] in {"92507", "92607"}:
                errors.append((f"For Health First patient 'Azeneth Ramos', CPT {parts[1]} should not be billed. Use CPT 92609/92526 instead", f"Replace CPT {parts[1]} with CPT 92609 and/or 92526 as appropriate"))
    return errors

def validate_healthfirst_corbin_anzaldi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "CORBIN" in first and "ANZALDI" in last:
            name_match = True
            break
    if not name_match:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            if parts[1] == "92606":
                errors.append(("For Health First patient 'Corbin Anzaldi', CPT 92606 should not be billed. Use CPT 92609/92526 instead", "Replace CPT 92606 with CPT 92609 and/or 92526 as appropriate"))
    return errors

def validate_healthfirst_kairo_jaco(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "KAIRO" in first and "JACO" in last:
            name_match = True
            break
    if not name_match:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_date = None
    for seg in mapped_claim_segments:
        if "Date Time Period" in seg:
            date_str = seg["Date Time Period"]
            if date_str.isdigit() and len(date_str) >= 8:
                service_date = date_str[:8]
                break
    if service_date is None or service_date < "20240828":
        return errors
    allowed = {"92609", "92526"}
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            if parts[1] not in allowed:
                errors.append((f"For Health First patient 'Kairo Jaco' with service date {service_date}, only CPT codes 92609 and 92526 are allowed. Found CPT {parts[1]}", f"Remove or replace CPT {parts[1]} with CPT 92609 or 92526"))
    return errors

def validate_healthfirst_erick_portillo_meza(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "ERICK" in first and "PORTILLO" in last and "MEZA" in last:
            name_match = True
            break
    if not name_match:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            if parts[1] == "92509":
                errors.append(("For Health First patient 'Erick Portillo Meza', CPT 92509 should not be billed. Use CPT 92609/92526 instead", "Replace CPT 92509 with CPT 92609 and/or 92526 as appropriate"))
    return errors

def validate_healthfirst_naira_chowdhury(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Health First":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "NAIRA" in first and "CHOWDHURY" in last:
            name_match = True
            break
    if not name_match:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            if parts[1] == "92606":
                errors.append(("For Health First patient 'Naira Chowdhury', CPT 92606 should not be billed. Use CPT 92526/92609 instead", "Replace CPT 92606 with CPT 92526 and/or 92609 as appropriate"))
    return errors

def validate_emblem_provider_based_on_specialty(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    if identify_insurance(claim) != "Emblem":
        return []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    
    # Extract CPT codes from the claim
    cpt_codes = {seg["Service Identification"].split(":")[1] for seg in mapped_claim_segments if "Service Identification" in seg}
    if not cpt_codes:
        return [("No service lines found in the claim", "Add at least one service line to the claim")]
    
    # Define specialty CPT codes
    speech_cpt = {"92507", "92526", "92609", "92521", "92522", "92523", "92524", "92610"}
    ot_cpt = {"97165", "97166", "97167", "97110", "97112", "97530", "97535"}
    
    # Determine specialty
    speech_only = all(cpt in speech_cpt for cpt in cpt_codes)
    ot_only = all(cpt in ot_cpt for cpt in cpt_codes)
    
    if speech_only:
        specialty = "speech"
        allowed_providers = {"**********", "**********"}  # Christiana and Stephanie
    elif ot_only:
        specialty = "occupational"
        allowed_providers = {"**********"}  # Jessica
    else:
        return [("Claim contains mixed or unrecognized CPT codes for Emblem", "Ensure all CPT codes are for a single specialty (speech or occupational therapy)")]
    
    # Get rendering provider NPI
    provider_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "82"), None)
    if not provider_npi:
        return [("Missing rendering provider (NM1*82) in the claim", "Add the rendering provider information to the claim")]
    
    # Validate provider based on specialty
    if provider_npi not in allowed_providers:
        if specialty == "speech":
            return [("For Emblem speech therapy claim, provider must be Christiana M Neophytou (**********) or Stephanie L Montano (**********)", "Update the provider to one of the allowed speech therapy providers")]
        elif specialty == "occupational":
            return [("For Emblem occupational therapy claim, provider must be Jessica Hanna (**********)", "Update the provider to Jessica Hanna with NPI **********")]
    return []

def validate_emblem_ghi_matthew_solomito(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Emblem":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match_found = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "MATTHEW" in first and "SOLOMITO" in last:
            name_match_found = True
            break
    if not name_match_found:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    has_92507 = False
    has_92609 = False
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            cpt_code = parts[1]
            if cpt_code == "92507":
                has_92507 = True
            elif cpt_code == "92609":
                has_92609 = True
    if has_92507 and has_92609:
        for seg in mapped_claim_segments:
            if "Service Identification" in seg:
                parts = seg["Service Identification"].split(":")
                if len(parts) < 2:
                    continue
                cpt_code = parts[1]
                if cpt_code == "92507" and "59" in parts[2:]:
                    errors.append(("For Emblem/GHI patient 'Matthew Solomito', do not use modifier '59' on CPT 92507 when billed with 92609", "Remove the '59' modifier from CPT 92507 when billed with CPT 92609"))
    return errors

def validate_emblem_ghi_madelyn_mulhall(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Emblem":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match_found = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "MADELYN" in first and "MULHALL" in last:
            name_match_found = True
            break
    if not name_match_found:
        return errors
    diag_set = extract_diagnosis_codes(mapped_all)
    if "F8081" not in diag_set:
        errors.append((
            "For Emblem/GHI patient 'Madelyn Mulhall', diagnosis F80.81 must be billed",
            "Add diagnosis code F80.81 to the claim"
        ))
    return errors

def validate_medicare_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Medicare":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    patient_name = "Unknown"
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") == "IL":
            first_name = seg.get("Name First", "").strip()
            last_name = seg.get("Name Last or Organization Name", "").strip()
            patient_name = f"{first_name} {last_name}".strip() if first_name or last_name else "Unknown"
            break
    if not any(seg.get("Entity Identifier Code") == "DN" and seg.get("Identification Code") for seg in mapped_all):
        errors.append(("Medicare requires referring provider information (Box 17 & 17B)", "Add referring provider information (Entity Identifier Code 'DN' with Identification Code) to the claim"))
    evaluation_cpts = {"92521", "92522", "92523", "92524", "97165", "97166", "97167", "97161", "97162", "97163", "92610"}
    treatment_cpts = {"92507", "92526", "92609", "97110", "97112", "97116", "97530"}
    ot_treatment_cpts = {"97110", "97112", "97530"}
    all_cpts = {seg["Service Identification"].split(":")[1] for seg in mapped_claim_segments if "Service Identification" in seg}
    is_ot_only = all_cpts.issubset(ot_treatment_cpts.union({"97165", "97166", "97167"}))
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            service_ident = seg["Service Identification"]
            parts = service_ident.split(":")
            if len(parts) < 3:
                errors.append((f"Invalid Service Identification format: '{service_ident}'", "Ensure Service Identification has at least three elements separated by ':'"))
                continue
            cpt_code = parts[1]
            modifiers = parts[2:] if len(parts) > 2 else []
            # if "KX" not in modifiers:
            #     errors.append((f"Medicare requires KX modifier for CPT {cpt_code}; found {modifiers}", f"Add the KX modifier to the Service Identification for CPT {cpt_code}"))
            try:
                units = float(seg.get("Units/Days", "").strip())
                amount = float(seg.get("Monetary Amount", "").strip())
            except ValueError:
                errors.append((f"Invalid Units/Days or Amount for '{service_ident}'", "Ensure Units/Days and Monetary Amount are valid numbers in the service line"))
                continue
            if cpt_code in evaluation_cpts:
                if units != 1:
                    errors.append((f"Patient {patient_name}: Medicare Evaluation CPT {cpt_code} billed with {units} units; expected 1", f"Set Units/Days to 1 for CPT {cpt_code}"))
                if abs(amount - 250.00) > 0.001:
                    errors.append((f"Patient {patient_name}: Medicare Evaluation CPT {cpt_code} billed at ${amount}; expected $250.00", f"Adjust Monetary Amount to $250.00 for CPT {cpt_code}"))
                continue
            elif cpt_code in treatment_cpts:
                if is_ot_only and cpt_code in ot_treatment_cpts:
                    expected_amount = units * 100.00
                elif cpt_code == "97530":
                    expected_amount = units * 100.00
                else:
                    expected_amount = units * 150.00
                if abs(amount - expected_amount) > 0.001:
                    errors.append((f"Patient {patient_name}: Medicare Treatment CPT {cpt_code} billed at ${amount} for {units} units; expected ${expected_amount:.2f}", f"Adjust Monetary Amount to ${expected_amount:.2f} for {units} units of CPT {cpt_code}"))
    return errors

def validate_oxford_92507_separately(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Oxford":
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    cpt_codes = {seg["Service Identification"].split(":")[1] for seg in mapped_claim_segments if "Service Identification" in seg}
    if "92507" in cpt_codes and "92526" in cpt_codes:
        errors.append(("Oxford: CPT 92507 and 92526 cannot be billed together", "Remove either CPT 92507 or CPT 92526 from the claim"))
    return errors

def validate_magnacare_penelope_manteiga_dx(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "Magnacare":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "PENELOPE" in first and "MANTEIGA" in last:
            name_match = True
            break
    if not name_match:
        return errors
    diag_set = extract_diagnosis_codes(mapped_all)
    required = {"R1312", "R636"}
    missing = required - diag_set
    if missing:
        errors.append((
            f"For Magnacare patient 'Penelope Manteiga', missing diagnosis codes: {', '.join(missing)} (required: R13.12, R63.6)",
            f"Add the missing diagnosis codes: {', '.join(missing)} to the claim"
        ))
    return errors

def validate_nyship_raphael_maramara_auth(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "NYSHIP":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "RAPHAEL" in first and "MARAMARA" in last:
            name_match = True
            break
    if not name_match:
        return errors
    auth_found = False
    for seg in mapped_all:
        if seg.get("Authorization Information"):
            auth = seg.get("Authorization Information").strip()
            if auth != "4502317892987":
                errors.append((f"For NYSHIP patient 'Raphael Maramara', authorization number must be '4502317892987'. Found '{auth}'", "Update the authorization number to '4502317892987'"))
            auth_found = True
            break
    if not auth_found:
        errors.append(("For NYSHIP patient 'Raphael Maramara', authorization number '4502317892987' is required but not found", "Add authorization number '4502317892987' to the claim"))
    return errors

def validate_nyship_jeremiah_adams_pos(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "NYSHIP":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "JEREMIAH" in first and "ADAMS" in last:
            name_match = True
            break
    if not name_match:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Place of Service" in seg:
            pos = seg["Place of Service"].strip()
            if pos != "12":
                errors.append((f"For NYSHIP patient 'Jeremiah Adams', Place of Service must be '12'. Found '{pos}'", "Update the Place of Service to '12'"))
    return errors

def validate_nyship_owen_seidita(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "NYSHIP":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "OWEN" in first and "SEIDITA" in last:
            name_match = True
            break
    if not name_match:
        return errors
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) < 2:
                continue
            cpt_code = parts[1]
            if cpt_code == "97533":
                errors.append(("For NYSHIP patient 'Owen Seidita', CPT 97533 should not be billed. Use CPT 97535 instead", "Replace CPT 97533 with CPT 97535 and ensure CPT 97530 is included if appropriate"))
    return errors

def validate_nyship_sean_thomas_dx(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "NYSHIP":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "SEAN" in first and "THOMAS" in last:
            name_match = True
            break
    if not name_match:
        return errors
    diag_set = extract_diagnosis_codes(mapped_all)
    if "F8081" not in diag_set:
        errors.append((
            "For NYSHIP patient 'Sean Thomas', diagnosis code F80.81 must be billed",
            "Add diagnosis code F80.81 to the claim"
        ))
    return errors

def validate_nyship_carter_rugen_dx(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "NYSHIP":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "CARTER" in first and "RUGEN" in last:
            name_match = True
            break
    if not name_match:
        return errors
    diag_set = extract_diagnosis_codes(mapped_all)
    required = {"F800", "R6330"}
    missing = required - diag_set
    if missing:
        errors.append((
            f"For NYSHIP patient 'Carter Rugen', missing diagnosis codes: {', '.join(missing)} (required: F80.0 and R63.30)",
            f"Add the missing diagnosis codes: {', '.join(missing)} to the claim"
        ))
    return errors

def validate_uhc_commercial_thomas_norris_dx(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "UHC Commercial":
        return errors

    # Map all segments for the claim
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]

    # Check if the patient is "Thomas Norris"
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "THOMAS" in first and "NORRIS" in last:
            name_match = True
            break
    if not name_match:
        return errors

    # Extract CPT codes from the claim
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    cpt_codes = {seg["Service Identification"].split(":")[1] for seg in mapped_claim_segments if "Service Identification" in seg}

    # Define speech therapy CPT codes
    speech_cpt_codes = {"92507", "92526", "92609", "92521", "92522", "92523", "92524", "92610"}

    # Check if the claim is for speech therapy
    is_speech_therapy = any(cpt in speech_cpt_codes for cpt in cpt_codes)
    if not is_speech_therapy:
        return errors  # Skip validation for non-speech therapy claims

    # Validate required diagnosis codes for speech therapy
    diag_set = extract_diagnosis_codes(mapped_all)
    required = {"F802", "F840", "R6330"}
    missing = required - diag_set
    if missing:
        errors.append((
            f"For UHC Commercial patient 'Thomas Norris', missing diagnosis codes: {', '.join(missing)} (required: F80.2, F84.0, R63.30)",
            f"Add the missing diagnosis codes: {', '.join(missing)} to the claim"
        ))

    return errors

def validate_uhc_community_gabriel_cruz(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) != "UHC Community Plan":
        return errors
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    nm1_il_segments = [seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"]
    if not nm1_il_segments:
        return errors
    name_match = False
    for seg in nm1_il_segments:
        first = seg.get("Name First", "").upper()
        last = seg.get("Name Last or Organization Name", "").upper()
        if "GABRIEL" in first and "CRUZ" in last:
            name_match = True
            break
    if not name_match:
        return errors
    return errors  # No errors, bill 92509/92526 as is

def validate_uhc_community_92526(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            service_ident = seg["Service Identification"]
            parts = service_ident.split(":")
            if len(parts) >= 2 and parts[1] == "92526":
                errors.append((
                    "For UHC Community Plan, CPT 92526 should not be billed",
                    "Remove CPT 92526 from the claim"
                ))
    return errors
 
async def validate_837_content(content: str, filename: str) -> dict:
    try:
        segments = [seg.strip() for seg in content.split('~') if seg.strip()]
        all_errors = validate_file_structure(segments)
        structured_errors = [{"claim_id": "N/A", "error": err[0], "solution": err[1], "severity": "High"} for err in all_errors]
        
        claims = parse_hierarchy(segments)
        if not claims:
            structured_errors.append({"claim_id": "N/A", "error": "No claims found in the file", "solution": "Ensure the file contains valid claim data (HL segments with level 22 or 23)", "severity": "High"})
        else:
            # Define general validations (applied to all claims)
            general_validations = [
                validate_claim_group,
                validate_modifiers,
                validate_pos_modifier,
                validate_92609_92507_modifiers,
                validate_ot_services,
                validate_diagnosis_code_repetition_and_format,
                validate_kx_modifier_for_patients,
                validate_multile_speech_provider,  # Added to run for all claims
            ]
            
            # Define insurance-specific validations
            insurance_validations = {
                "Aetna": [
                    validate_aetna_speech_provider,
                    validate_aetna_samantha_dussel,
                    validate_aetna_jonathan_ortiz
                ],
                "BCBS Medicaid": [
                    validate_bcbs_medicaid_speech_provider,
                    validate_diagnoses_for_trigger_services,
                    validate_bcbs_christian_sankar
                ],
                "Health First": [
                    validate_92610_separation,
                    validate_claim_billed_under_dr_christiana,
                    validate_pos_11_only,
                    validate_healthfirst_insurance_id_for_wyatt_jaden,
                    validate_healthfirst_only_ot,
                    validate_healthfirst_phoenix_prevalus_dx,
                    validate_healthfirst_jacob_ortez_ronk_dx,
                    validate_healthfirst_brianna_insurance,
                    validate_healthfirst_ryder_jacobson_remove_92507,
                    validate_healthfirst_azeneth_ramos,
                    validate_healthfirst_corbin_anzaldi,
                    validate_healthfirst_kairo_jaco,
                    validate_healthfirst_erick_portillo_meza,
                    validate_healthfirst_naira_chowdhury
                ],
                "Emblem": [
                    validate_emblem_provider_based_on_specialty,
                    validate_emblem_ghi_matthew_solomito,
                    validate_emblem_ghi_madelyn_mulhall
                ],
                "Medicare": [
                    validate_claim_billed_under_dr_christiana,
                    validate_medicare_rates
                ],
                "Oxford": [
                    # validate_oxford_92507_separately
                ],
                "Magnacare": [
                    validate_magnacare_penelope_manteiga_dx
                ],
                "NYSHIP": [
                    validate_claim_billed_under_dr_christiana,
                    validate_nyship_raphael_maramara_auth,
                    validate_nyship_jeremiah_adams_pos,
                    validate_nyship_owen_seidita,
                    validate_nyship_sean_thomas_dx,
                    validate_nyship_carter_rugen_dx
                ],
                "UHC Commercial": [
                    validate_shared_speech_provider,
                    validate_uhc_commercial_thomas_norris_dx
                ],
                "UHC Community Plan": [
                    validate_shared_speech_provider,
                    validate_uhc_community_gabriel_cruz,
                    validate_uhc_community_92526
                ],
                "American Specialty Health": [validate_shared_speech_provider],
                "BLUE CROSS BLUE SHIELD": [validate_shared_speech_provider],
                "Cigna": [validate_shared_speech_provider],
                "Emblem / GHI": [validate_shared_speech_provider],
                "HCP": [validate_shared_speech_provider],
                "Humana": [validate_shared_speech_provider],
                "L1199": [validate_shared_speech_provider],
                "Magnacare": [validate_shared_speech_provider],
                "Medicaid": [validate_shared_speech_provider],
                "Molina": [validate_shared_speech_provider],
                "Northwell HealthComp": [validate_shared_speech_provider],
                "Oxford": [validate_shared_speech_provider],
                "Tricare": [validate_shared_speech_provider],
                "Trustmark": [validate_shared_speech_provider],
                "UMR": [validate_shared_speech_provider, validate_umr_only_r_dx],
            }

            for claim in claims:
                # Extract claim_id from claim_segments
                claim_id = next((seg.get("Claim Submitter's Identifier") for seg in [map_segment(seg) for seg in claim["claim_segments"]] if "Claim Submitter's Identifier" in seg), "Unknown")
                
                # Skip if no CLM segment (for precision)
                if claim_id == "Unknown":
                    continue
                
                insurance = identify_insurance(claim)
                
                # Apply general validations
                applicable_general_validations = general_validations.copy()
                if insurance != "Medicare":
                    applicable_general_validations.append(lambda c: validate_evaluation_treatment_units_and_price(c, insurance))
                
                for validate_func in applicable_general_validations:
                    errors = validate_func(claim)
                    for error, solution in errors:
                        structured_errors.append({
                            "claim_id": claim_id,
                            "error": error,
                            "solution": solution,
                            "severity": "High"  # Fixed to match Snippet B
                        })
                
                # Apply insurance-specific validations
                if insurance in insurance_validations:
                    for validate_func in insurance_validations[insurance]:
                        errors = validate_func(claim)
                        for error, solution in errors:
                            structured_errors.append({
                                "claim_id": claim_id,
                                "error": error,
                                "solution": solution,
                                "severity": "High"
                            })
        
        error_breakdown = collections.Counter(err["error"].split(":")[0].strip()
                                            for err in structured_errors)
        # **here** we record using the passed-in filename
        save_validation_stats(
            filename=filename,
            total_errors=len(structured_errors),
            error_breakdown=dict(error_breakdown)
        )
        # for error in structured_errors:
        #     error_type = error["error"].split(":")[0].strip()
        #     error_breakdown[error_type] += 1
        # error_breakdown_dict = dict(error_breakdown)
        # save_validation_stats(
        #     filename=file.filename,
        #     total_errors=len(structured_errors),
        #     error_breakdown=error_breakdown_dict
        # )
        if structured_errors:
            return {"errors": structured_errors}
        else:
            return {"message": "No errors found"}


    except UnicodeDecodeError as e:
        logger.error(f"File decoding error: {str(e)}")
        raise HTTPException(status_code=400, detail="File must be a valid UTF-8 encoded text file")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# Run the app
@app.post("/validate-837")
async def validate_837(file: UploadFile = File(...)):
    content = (await file.read()).decode("utf-8").strip()
    if not content:
        raise HTTPException(400, "Uploaded file is empty")
    # pass file.filename into the validator
    return await validate_837_content(content, file.filename)

# ─── other endpoints, scheduler startup event ─────────────────────────────────
@app.get("/get-daily-count")
async def get_daily_count_endpoint():
    today = date.today().isoformat()
    return {"date": today, "count": get_daily_count()}

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/trigger-daily-report")
async def trigger_daily_report():
    send_daily_report()
    return {"message": "Manual report triggered"}

@app.delete("/reset-database")
async def reset_database(confirm: str = None):
    if confirm != "yes":
        raise HTTPException(400, "Please confirm reset with ?confirm=yes")
    if os.path.exists("validation_counts.db"):
        os.remove("validation_counts.db")
    init_db()
    return {"message": "Database reset successfully"}

scheduler = BackgroundScheduler()
@app.on_event("startup")
def startup_event():
    init_db()
    scheduler.add_job(send_daily_report,
                      CronTrigger(hour=23, minute=55, timezone="Asia/Kolkata"),
                      name="daily_report")
    scheduler.add_job(reset_daily_count,
                      CronTrigger(hour=0, minute=0, timezone="Asia/Kolkata"),
                      name="reset_daily_count")
    scheduler.start()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)






#------------------------------newly implemented code---------------------------------------------------------------

from typing import Dict, List, Set, Tuple

def validate_speech_provider(
    claim: Dict[str, List[str]],
    insurance_names: Set[str],
    valid_npis: Set[str],
    error_message: str,
    speech_cpt_codes: Set[str] = None
) -> List[Tuple[str, str]]:

    logger.info("TEST: validate_speech_provider called")
    errors = []

    claim_insurance = identify_insurance(claim)
    logger.info(f"Identified insurance: {claim_insurance}")

    if claim_insurance not in insurance_names:
        logger.info(f"Insurance '{claim_insurance}' not in {insurance_names}. Skipping validation.")
        return errors

    if speech_cpt_codes is None:
        speech_cpt_codes = {"92507", "92609", "92526", "92521", "92522", "92523", "92524", "92610"}

    logger.info(f"Using speech CPT codes: {speech_cpt_codes}")

    mapped_all = [
        map_segment(seg)
        for seg in claim.get("billing_provider", []) + claim.get("subscriber", []) +
                   claim.get("patient", []) + claim.get("claim_segments", [])
    ]
    logger.info(f"Total segments mapped: {len(mapped_all)}")

    mapped_claim_segments = [map_segment(seg) for seg in claim.get("claim_segments", [])]
    logger.info(f"Claim segments mapped: {len(mapped_claim_segments)}")

    # Check if any speech CPT code is present
    speech_cpt_present = any(
        "Service Identification" in seg and seg["Service Identification"].split(":")[1] in speech_cpt_codes
        for seg in mapped_claim_segments
    )
    logger.info(f"Speech CPT code present in claim segments: {speech_cpt_present}")

    if speech_cpt_present:
        # Check if a valid provider NPI is used
        valid_npi_used = any(
            seg.get("Entity Identifier Code") == "82" and seg.get("Identification Code") in valid_npis
            for seg in mapped_all
        )
        logger.info(f"Valid NPI used in claim: {valid_npi_used}")

        if not valid_npi_used:
            logger.info(f"Error: {error_message}")
            errors.append((error_message, f"Ensure the claim is billed under one of the allowed NPIs: {', '.join(valid_npis)}"))

    return errors


# Define your sets again (assuming these are unchanged)
aetna_insurances = {"Aetna"}

emblem_insurances = {"Emblem Medicaid"}

shared_insurances = {
    "American Specialty Health",
    "BLUE CROSS BLUE SHIELD",
    "Cigna",
    "Emblem / GHI",
    "HCP",
    "Humana",
    "L1199",
    "Magnacare",
    "Medicaid",
    "Molina",
    "Northwell HealthComp",
    "Oxford",
    "Tricare",
    "Trustmark",
    "UHC Commercial",
    "UHC Community plan",
    "UMR"
}

# Validators

def validate_aetna_speech_provider(claim):
    print("Validating Aetna speech provider...")
    return validate_speech_provider(
        claim=claim,
        insurance_names=aetna_insurances,
        valid_npis={"**********"},
        error_message="Aetna speech services must be billed under Dr. Christiana Neophytou (NPI **********)."
    )


def validate_emblem_speech_provider(claim):
    print("Validating Emblem Medicaid speech provider...")
    return validate_speech_provider(
        claim=claim,
        insurance_names=emblem_insurances,
        valid_npis={"**********"},
        error_message="Emblem Medicaid services must be billed under Dr. Stephanie L. Montano (NPI **********)."
    )

def validate_shared_speech_provider(claim):
    print("Validating shared speech provider...")
    return validate_speech_provider(
        claim=claim,
        insurance_names=shared_insurances,
        valid_npis={"**********", "**********", "**********"},
        error_message=(
            "Speech services must be billed under one of the following providers:\n"
            "- Dr. Christiana M. Neophytou (NPI **********)\n"
            "- Stephanie L. Montano (NPI **********)\n"
            "- Jessica Hanna (NPI **********)"
        )
    )


def validate_pos_11_only(claim: Dict[str, List[str]], insurers: List[str]) -> List[Tuple[str, str]]:
    errors = []
    if identify_insurance(claim) not in insurers:
        return errors

    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Place of Service" in seg and seg["Place of Service"].strip() != "11":
            errors.append((f"Health First requires POS '11', found '{seg['Place of Service']}' in '{seg.get('Service Identification', 'N/A')}'", f"Update the Place of Service to '11' for the service '{seg.get('Service Identification', 'N/A')}'"))
    return errors


pos_11_insurers = ["Health First", "Northwell HealthComp"]

def validate_umr_only_r_dx(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    For UMR insurance, only R-series diagnosis codes are allowed.
    If any diagnosis code does not start with 'R', return an error.
    """
    logger.info("Starting validation for UMR R-only DX codes" + "*" * 50)

    errors = []
    logger.info(f"Initialized errors list: {errors} {'#' * 50}")

    insurance = identify_insurance(claim)
    logger.info(f"Identified insurance: {insurance} {'@' * 50}")

    if insurance != "UMR":
        logger.info("Insurance is not UMR, exiting check early" + "!" * 50)
        return errors

    mapped_all = [map_segment(seg) for seg in (
        claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]
    )]
    logger.info(f"Mapped all segments: {mapped_all} {'-' * 50}")

    diag_set = extract_diagnosis_codes(mapped_all)
    logger.info(f"Extracted diagnosis codes: {diag_set} {'+' * 50}")

    non_r_dx = [dx for dx in diag_set if not dx.startswith("R")]
    logger.info(f"Diagnosis codes not starting with 'R': {non_r_dx} {'=' * 50}")

    if non_r_dx:
        error_msg = (
            f"UMR claims may only use R-series diagnosis codes. Found non-R codes: {', '.join(non_r_dx)}",
            "Remove or replace all non-R diagnosis codes with R-series codes for UMR claims."
        )
        errors.append(error_msg)
        logger.info(f"Added error to errors list: {error_msg} {'~' * 50}")

    logger.info(f"Final errors returned: {errors} {'%' * 50}")
    return errors

# List of patients who require KX modifier on all service lines
KX_REQUIRED_PATIENTS = {
    "ANDERS STRONSTAD",
    "ARNOLD LEVINE",
    "BALWANTBHA PATEL",
    "BRITTANY SCHIAVONE",
    "CARMELO RIZZO",
    "CHRISTOPHER VALDES",
    "CORYN VILLANTI",
    "CRISTIAN INOA",
    "DANIEL TOOHEY",
    "DANIEL STONER",
    "DONNA JOHNSON",
    "DOUGLAS KUNTZ",
    "DOUGLAS ZAHN",
    "FRANK CAPOLINO",
    "GEORGE BAZYILEVSKY",
    "GLENN MATTA",
    "HARVEY PASSO",
    "HENRY LOZANO",
    "JAMIE COATES",
    "JASON URBAN",
    "JOHN NACEWICZ",
    "JOSEPH KALAF",
    "LINDA MARLOWE",
    "MARC BLOISE",
    "MARGARET KATZ",
    "MARGARET VELEZ",
    "MATHILDA PALMERI",
    "MATTHEW BARNUM",
    "MATTHEW GAVOSTO",
    "MICHAEL MARTINSEN",
    "MICHAEL TIGHE",
    "MICHELLE KUNTZ",
    "MOHAMMAD KHAN",
    "MYLES SAVITT",
    "NANCY BROOKS",
    "PAMELA HAUSER",
    "PETER YABLONSKI",
    "RICHARD KUNTZ",
    "ROBERT DOHRENWEND",
    "ROBERT HINCK",
    "ROSE CARUSO",
    "SANDRA SIEGEL",
    "SEBASTIANA LUCA",
    "STEPHANIE ALTMAN",
    "SUSAN GRALLA",
    "SUZANNE KOSTEL",
    "THOMAS LAUDICINA",
    "WILLIAM FOSTER",
    "WILLIAM GIOVINGO"
}

def validate_kx_modifier_for_patients(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    # Find patient name (NM1*IL segment)
    mapped_all = [map_segment(seg) for seg in claim.get("billing_provider", []) + claim.get("subscriber", []) + claim.get("patient", []) + claim.get("claim_segments", [])]
    nm1_il = next((seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"), None)
    if not nm1_il:
        return errors
    first_name = nm1_il.get("Name First", "").strip().upper()
    last_name = nm1_il.get("Name Last or Organization Name", "").strip().upper()
    full_name = f"{first_name} {last_name}".strip()
    if full_name not in KX_REQUIRED_PATIENTS:
        return errors
    # Check each service line for KX modifier
    mapped_claim_segments = [map_segment(seg) for seg in claim.get("claim_segments", [])]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            modifiers = parts[2:] if len(parts) > 2 else []
            if "KX" not in modifiers:
                errors.append((f"Patient {full_name} requires KX modifier for CPT {parts[1] if len(parts)>1 else ''}; found {modifiers}", f"Add the KX modifier to the Service Identification for CPT {parts[1] if len(parts)>1 else ''}"))
    return errors