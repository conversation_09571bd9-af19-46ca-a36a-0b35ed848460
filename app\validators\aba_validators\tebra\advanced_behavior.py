
from typing import List, <PERSON><PERSON>, Dict
from datetime import datetime
from zoneinfo import ZoneInfo

from app.validators.aba_validators.utils import map_segment




from typing import Dict, List, <PERSON><PERSON>

def validate_abcil_tricare_97155_97156_units(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    Rule: TRICARE (TRICARE EAST, TRIWEST, TRICARE) - Limit 97155 and 97156 to 8 units per day.
    """
    errors = []

    # Map all segments
    mapped_all = [map_segment(seg) for seg in (
        claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]
    )]

    # Insurance detection: check for TRICARE keywords
    is_tricare = any(
        any(keyword in seg.get("Name Last or Organization Name", "").upper()
            for keyword in ["TRICARE EAST", "TRIWEST", "TRICARE"])
        for seg in mapped_all
    )

    if not is_tricare:
        return []

    # Extract service lines with their associated DOS
    service_lines = []
    current_dtp = None

    for seg in mapped_all:
        seg_id = seg.get("Segment ID")
        if seg_id == "DTP" and seg.get("Date/Time Qualifier") == "472":
            current_dtp = seg.get("Date Time Period", "Unknown")
        elif "Service Identification" in seg:
            cpt_info = seg["Service Identification"].split(":")
            if len(cpt_info) >= 2 and cpt_info[1] in {"97155", "97156"}:
                service_line = dict(seg)
                service_line["CPT"] = cpt_info[1]
                service_line["DOS"] = current_dtp or "Unknown"
                service_lines.append(service_line)
                current_dtp = None  # reset after use

    # Aggregate units per CPT per DOS
    dos_units = {}
    for line in service_lines:
        dos = line["DOS"]
        cpt = line["CPT"]
        units = float(line.get("Units/Days").strip())

        dos_units.setdefault(dos, {}).setdefault(cpt, 0.0)
        dos_units[dos][cpt] += units

    # Validate against threshold
    for dos, cpt_data in dos_units.items():
        for cpt, total_units in cpt_data.items():
            if total_units > 8:
                errors.append((
                    f"TRICARE {cpt} exceeds 8 units on DOS {dos}; found {total_units}",
                    "Limit 97155/97156 to 8 units per day for TRICARE (TRICARE EAST, TRIWEST, TRICARE)"
                ))

    return errors
