from typing import List, Tuple, Dict
from ..utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
PROVIDER_NPI = "**********"
IST = ZoneInfo("Asia/Kolkata")

def _split_sv1(seg: Dict[str, str]) -> Tuple[str, str]:
    print("_split_sv1 called with", seg, "*"*50)
    parts = seg["Service Identification"].split(":")
    code = parts[1] if len(parts) >= 2 else ""
    modifier = parts[2] if len(parts) >= 3 else ""
    print("code", code, "modifier", modifier, "*"*50)
    return code, modifier

def _get_payer(mapped):
    print("_get_payer called with mapped", mapped, "*"*50)
    payer = next(
        (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
        next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg), "")
    )
    print("payer", payer, "*"*50)
    return payer

def validate_aetna_modifiers_and_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Aetna: No provider modifier (HM, HN, HO). Enforce rates."""
    errors = []
    # Map all relevant segments to find payer name
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer = next(
        (seg.get("Name Last or Organization Name", "").upper()
         for seg in mapped_all
         if seg.get("Entity Identifier Code") == "PR" and "Name Last or Organization Name" in seg),
        ""
    )
    print("payer", payer, "*"*50)  # Corrected line
    if "AETNA" not in payer:
        return errors

    # Only process service lines from claim_segments
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc = [s for s in mapped if "Service Identification" in s]
    cpt_rates = {
        "97151": 35.00,
        "97152": 35.00,
        "97155": 35.00,
        "97153": 25.00,
        "97156": 35.00,
    }
    for seg in svc:
        code, mod = _split_sv1(seg)
        amt_str = seg.get("Monetary Amount", "").strip()
        units_str = seg.get("Units/Days", "").strip()

        total_charge = float(amt_str)
        units = float(units_str)
        unit_rate = total_charge / units if units else 0
        
        if mod in ("HM", "HN", "HO"):
            errors.append((
                f"Aetna: CPT {code} should not have modifier {mod}",
                "Remove provider modifier (HM, HN, HO) for Aetna claims"
            ))
        if code in cpt_rates and unit_rate is not None and abs(unit_rate - cpt_rates[code]) > 0.01:
            errors.append((
                f"Aetna: CPT {code} has rate ${unit_rate:.2f}/unit, expected ${cpt_rates[code]:.2f}",
                f"Set rate for CPT {code} to ${cpt_rates[code]:.2f} per unit for Aetna claims"
            ))
    return errors

def validate_anthem_ca_modifiers_and_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Anthem Blue Cross of California: Enforce specific modifier and rate per CPT."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer = next(
        (seg.get("Name Last or Organization Name", "").upper()
         for seg in mapped_all
         if seg.get("Entity Identifier Code") == "PR" and "Name Last or Organization Name" in seg),
        ""
    )
    print("payer",payer)
    if not ("ANTHEM" in payer and "CALIFORNIA" in payer):
        return errors
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc = [s for s in mapped if "Service Identification" in s]
    cpt_mod_rate = {
        "97151": ("HO", 48.00),
        "97153": ("HM", 15.00),
        "97155": ("HO", 38.00),
        "97156": ("HO", 38.00),
    }
    for seg in svc:
        code, mod = _split_sv1(seg)
        amt_str = seg.get("Monetary Amount", "").strip()
        units_str = seg.get("Units/Days", "").strip()

        total_charge = float(amt_str)
        units = float(units_str)
        unit_rate = total_charge / units if units else 0

        if code in cpt_mod_rate:
            expected_mod, expected_rate = cpt_mod_rate[code]
            if mod != expected_mod:
                errors.append((
                    f"Anthem CA: CPT {code} should have modifier {expected_mod}, found '{mod}'",
                    f"Use modifier {expected_mod} for CPT {code} for Anthem Blue Cross of California claims"
                ))
            if unit_rate is not None and abs(unit_rate - expected_rate) > 0.01:
                errors.append((
                    f"Anthem CA: CPT {code} has rate ${unit_rate:.2f}/unit, expected ${expected_rate:.2f}",
                    f"Set rate for CPT {code} to ${expected_rate:.2f} per unit for Anthem Blue Cross of California claims"
                ))
    return errors

def validate_cigna_modifiers_and_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Cigna Behavioral Health: No provider modifier (HM, HN, HO). Enforce rates."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer = next(
        (seg.get("Name Last or Organization Name", "").upper()
         for seg in mapped_all
         if seg.get("Entity Identifier Code") == "PR" and "Name Last or Organization Name" in seg),
        ""
    )
    if "CIGNA" not in payer:
        return errors
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc = [s for s in mapped if "Service Identification" in s]
    cpt_rates = {
        "97151": 30.00,
        "97152": 30.00,
        "97155": 29.00,
        "97153": 20.00,
        "97156": 28.00,
    }
    for seg in svc:
        code, mod = _split_sv1(seg)
        amt_str = seg.get("Monetary Amount", "").strip()
        units_str = seg.get("Units/Days", "").strip()

        total_charge = float(amt_str)
        units = float(units_str)
        unit_rate = total_charge / units

        if mod in ("HM", "HN", "HO"):
            errors.append((
                f"Cigna: CPT {code} should not have modifier {mod}",
                "Remove provider modifier (HM, HN, HO) for Cigna Behavioral Health claims"
            ))
        if code in cpt_rates and unit_rate is not None and abs(unit_rate - cpt_rates[code]) > 0.01:
            errors.append((
                f"Cigna: CPT {code} has rate ${unit_rate:.2f}/unit, expected ${cpt_rates[code]:.2f}",
                f"Set rate for CPT {code} to ${cpt_rates[code]:.2f} per unit for Cigna Behavioral Health claims"
            ))
    return errors

def validate_reframing_behavior_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    return (
        validate_aetna_modifiers_and_rates(claim) +
        validate_anthem_ca_modifiers_and_rates(claim) +
        validate_cigna_modifiers_and_rates(claim)
    )