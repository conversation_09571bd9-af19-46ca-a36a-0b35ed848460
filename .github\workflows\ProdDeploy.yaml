name: Prod Pipeline

on:
  push:
    branches: ["main"]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4


      - name: Execute commands on EC2
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.SERVER_SSH_PORT }}
          script: |
            cd /var/www/scrubbing.therapypms.com/src/workspace
            git pull
            source /var/www/scrubbing.therapypms.com/venv/bin/activate
            pip install -r requirements.txt
            sudo systemctl restart scrubbing
