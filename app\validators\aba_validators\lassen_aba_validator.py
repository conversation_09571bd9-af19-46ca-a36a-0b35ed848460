from typing import List, Tu<PERSON>, Dict
from .utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
PROVIDER_NPI = "**********"  # <PERSON>
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
POS_21_CUTOFF = datetime(2022, 10, 9)  # From update on 10/09/2022
MODIFIER_START_DATE = datetime(2023, 7, 11)  # From Partnership update on 07/11/2023
H0032_SIX_MONTHS = 180  # 6 months in days

def validate_lassen_aba_pos_21_as_11(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: POS 21 billed as POS 11 until 10/09/2022."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE

    if dos_date <= POS_21_CUTOFF:
        for seg in service_lines:
            pos = seg.get("Place of Service", "").strip()
            if pos == "21":
                errors.append((
                    f"POS 21 on {dos} before 10/09/2022",
                    "Bill POS 21 as POS 11 until 10/09/2022 per Lassen ABA rules"
                ))
    return errors

def validate_lassen_aba_97154_group_confirmation(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: Flag 97154 for manual confirmation of group session (same DOS, provider, different patients)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "97154":
            dos = next((s.get("Date Time Period") for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), "Unknown")
            errors.append((
                f"CPT 97154 on {dos} requires group session confirmation",
                "Verify same DOS, provider, and multiple patients per 05/01/2023 update"
            ))
    return errors

def validate_lassen_aba_h0032_six_months(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 7: H0032 billed once every 6 months for Partnership."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_partnership = any("PARTNERSHIP" in seg.get("Payer Name", "").upper() or "PARTNERSHIP" in seg.get("Application Receiver's Code", "").upper() for seg in mapped_all)
    if not is_partnership:
        return []

    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    h0032_dates = []
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "H0032":
            dos = next((s.get("Date Time Period") for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), None)
            if dos:
                try:
                    dos_date = datetime.strptime(dos, "%Y%m%d")
                    h0032_dates.append(dos_date)
                except ValueError:
                    errors.append((f"Invalid DOS format for H0032: {dos}", "Use YYYYMMDD format"))

    if len(h0032_dates) > 1:
        sorted_dates = sorted(h0032_dates)
        for i in range(1, len(sorted_dates)):
            if (sorted_dates[i] - sorted_dates[i-1]).days < H0032_SIX_MONTHS:
                errors.append((
                    f"H0032 billed on {sorted_dates[i].strftime('%Y%m%d')} within 6 months of {sorted_dates[i-1].strftime('%Y%m%d')}",
                    "Bill H0032 only once every 6 months; send clarification mail for additional instances"
                ))
    return errors

def validate_lassen_aba_partnership_optum_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 8: Partnership and Optum use HN (BCABA), HM (RBT), HO (BCBA) modifiers post-07/11/2023."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    payer_name = next((seg.get("Payer Name", "").upper() for seg in mapped_all if "Payer Name" in seg), "")
    payer_code = next((seg.get("Application Receiver's Code", "").upper() for seg in mapped_all if "Application Receiver's Code" in seg), "")
    
    is_applicable = "PARTNERSHIP" in payer_name or "PARTNERSHIP" in payer_code or "OPTUM" in payer_name or "OPTUM" in payer_code
    if not is_applicable:
        return []

    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = datetime.strptime(dos, "%Y%m%d") if dos else CURRENT_DATE

    if dos_date >= MODIFIER_START_DATE:
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2:
                modifier = parts[2] if len(parts) > 2 else ""
                if modifier not in {"HM", "HN", "HO"}:
                    errors.append((
                        f"CPT {parts[1]} on {dos} missing required modifier for {payer_name or payer_code}; found '{modifier}'",
                        "Use HM (RBT), HN (BCABA), or HO (BCBA) post-07/11/2023"
                    ))
    return errors

def validate_lassen_aba_97154_h2014_group_sessions(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 9: Flag 97154 and H2014 for manual confirmation of group sessions."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]

    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in {"97154", "H2014"}:
            dos = next((s.get("Date Time Period") for s in mapped_all if "Date Time Period" in s and s.get("Date/Time Qualifier") == "472"), "Unknown")
            errors.append((
                f"CPT {parts[1]} on {dos} requires group session confirmation",
                "Verify 2+ sessions for different patients, same timing, provider, and POS per 07/11/2023 update"
            ))
    return errors

def validate_lassen_aba_97154_rate(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: CPT 97154 must be billed at $12/unit for all insurances except Partnership."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_partnership = any(
        "PARTNERSHIP" in seg.get("Payer Name", "").upper() or "PARTNERSHIP" in seg.get("Application Receiver's Code", "").upper()
        for seg in mapped_all
    )
    if is_partnership:
        return []

    current_lx = None
    seen = set()
    for seg in mapped_all:
        if "Assigned Number" in seg and "Service Identification" not in seg:
            current_lx = seg["Assigned Number"]
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2 and parts[1] == "97154":
                units = float(seg.get("Units/Days", "").strip())
                charge = float(seg.get("Monetary Amount", "").strip())
                per_unit_rate = round(charge / units, 2)
                error_key = (current_lx, per_unit_rate)
                if per_unit_rate != 12.00 and error_key not in seen:
                    seen.add(error_key)
                    lx_display = f"LX*{current_lx}~" if current_lx else ""
                    errors.append((
                        f"{lx_display} CPT 97154 billed at ${per_unit_rate:.2f} per unit (expected $12.00)",
                        "Bill CPT 97154 at $12.00 per unit for all insurances except Partnership."
                    ))
    return errors

def validate_lassen_anthony_armbrust_h2014_units(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    For the patient Anthony Armbrust, CPT H2014: if received above 32 units, flag to change and bill for 32 units.
    """
    errors = []
    mapped_patient = [map_segment(seg) for seg in claim["patient"]]
    patient_nm1 = next((seg for seg in mapped_patient if seg.get("Segment ID") == "NM1" and seg.get("Entity Identifier Code") == "IL"), None)
    last = (patient_nm1.get("Name Last or Organization Name", "") if patient_nm1 else "").strip().upper()
    first = (patient_nm1.get("Name First", "") if patient_nm1 else "").strip().upper()
    patient_name = f"{last} {first}".strip()
    if patient_name == "ARMBRUST ANTHONY":
        mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
        for seg in mapped_claim_segments:
            if "Service Identification" in seg:
                parts = seg["Service Identification"].split(":")
                if len(parts) >= 2 and parts[1] == "H2014":
                    units = float(seg.get("Units/Days", "").strip())
                    if units > 32:
                        errors.append((
                            f"Anthony Armbrust CPT H2014 exceeds 32 units; found {units}",
                            "Change H2014 to 32 units per practice rule"
                        ))
    return errors

def validate_lassen_blueshield_ca_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    For insurance Blue Shield CA, enforce modifiers:
    97151 - HO
    97153 - HM
    97155, 97156 - HO or HN
    """
    errors = []
    # Find payer name from NM1*PR segment in billing_provider, subscriber, patient, or claim_segments
    all_segments = claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]
    mapped_all = [map_segment(seg) for seg in all_segments]
    payer_nm1 = next((seg for seg in mapped_all if seg.get("Segment ID") == "NM1" and seg.get("Entity Identifier Code") == "PR"), None)
    payer_name = (payer_nm1.get("Name Last or Organization Name", "") if payer_nm1 else "").strip().upper()
    if "BLUE SHIELD - CALIFORNIA" not in payer_name:
        return []
    # Only process claim_segments for service lines
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    for seg in mapped_claim_segments:
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2:
                cpt = parts[1]
                modifier = parts[2] if len(parts) > 2 else ""
                if cpt == "97151" and modifier != "HO":
                    errors.append((
                        f"Blue Shield CA: CPT 97151 should have modifier HO, found '{modifier}'",
                        "Use modifier HO for CPT 97151 for Blue Shield CA"
                    ))
                elif cpt == "97153" and modifier != "HM":
                    errors.append((
                        f"Blue Shield CA: CPT 97153 should have modifier HM, found '{modifier}'",
                        "Use modifier HM for CPT 97153 for Blue Shield CA"
                    ))
                elif cpt in ("97155", "97156") and modifier not in ("HO", "HN"):
                    errors.append((
                        f"Blue Shield CA: CPT {cpt} should have modifier HO or HN, found '{modifier}'",
                        "Use modifier HO or HN for CPT 97155/97156 for Blue Shield CA"
                    ))
    return errors


def validate_lassen_sandra_nadeau_clubbing(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    For patient 'Sandra Nadeau', if received under two technicians (NM1*82) for the same DOS and same timing,
    flag that sessions can be clubbed and billed together.
    """
    errors = []
    mapped_patient = [map_segment(seg) for seg in claim["patient"]]

    patient_nm1 = next((seg for seg in mapped_patient if seg.get("Segment ID") == "NM1" and seg.get("Entity Identifier Code") == "IL"), None)
    last = (patient_nm1.get("Name Last or Organization Name", "") if patient_nm1 else "").strip().upper()
    first = (patient_nm1.get("Name First", "") if patient_nm1 else "").strip().upper()
    patient_name = f"{last} {first}".strip()

    if patient_name != "NADEAU SANDRA":
        return []

    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]

    sessions = []
    current_dos = None
    current_tech = None
    for seg in mapped_claim_segments:
        if seg.get("Segment ID") == "NM1" and seg.get("Entity Identifier Code") == "82":

            tech_last = seg.get("Name Last or Organization Name", "").strip().upper()
            tech_first = seg.get("Name First", "").strip().upper()
            current_tech = f"{tech_last} {tech_first}".strip()
        if seg.get("Segment ID") == "DTP" and seg.get("Date/Time Qualifier") == "472":
            current_dos = seg.get("Date Time Period")
        if seg.get("Segment ID") == "SV1":

            parts = seg.get("Service Identification", "").split(":")
            timing = ""
            if len(parts) > 5 and parts[5]:
                timing = parts[5]
            tech = current_tech or "UNKNOWN"
            sessions.append((current_dos, timing, tech))

    from collections import defaultdict
    session_map = defaultdict(set)
    for dos, timing, tech in sessions:
        session_map[(dos, timing)].add(tech)
    for (dos, timing), techs in session_map.items():

        if len(techs) > 1:
            errors.append((
                f"Sandra Nadeau: Multiple technicians ({', '.join(techs)}) for DOS {dos} and timing {timing}",
                "Club sessions for same DOS and timing under Sandra Nadeau and bill together"
            ))
    return errors


def validate_lassen_aba_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Lassen ABA-specific validation rules."""
    return (
        validate_lassen_aba_pos_21_as_11(claim) +
        validate_lassen_aba_97154_rate(claim) +
        validate_lassen_anthony_armbrust_h2014_units(claim) +
        validate_lassen_aba_h0032_six_months(claim) +
        validate_lassen_aba_partnership_optum_modifiers(claim) +
        validate_lassen_blueshield_ca_modifiers(claim) +
        validate_lassen_sandra_nadeau_clubbing(claim)
        # check_duplicate_cpt_billing_lassen(claim)
        # validate_lassen_aba_97154_h2014_group_sessions(claim)
    )