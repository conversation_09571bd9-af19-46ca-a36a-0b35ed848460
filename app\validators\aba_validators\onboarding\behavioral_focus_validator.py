# app/validators/aba_validators/onboarding/behavioral_focus_validator.py
from typing import List, <PERSON><PERSON>, Dict
from ..utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID     = "*********"
GROUP_NPI  = "**********"
IST        = ZoneInfo("Asia/Kolkata")

bcba_identifiers = ["STACY", "DOW", "TRUBE", "MILLER"]

rbt_names = {
    "ALFORD REBEKAH", "BAUKNIGHT EMILY", "HALL NIA", "HARRIS GABRIELLE", "KELLY KENDALL",
    "LEAGUE MEGAN", "MARTIN VALDES ADRIAN", "MOORE JANAH", "ROUSE LEAH", "SAAVEDRA OLGUIN MACARENA",
    "SHELNUTT KEELY-ANN"
}

def _split_sv1(seg: Dict[str, str]) -> <PERSON><PERSON>[str, str]:
    """Helper to pull CPT code and modifier out of SV1."""
    parts = seg["Service Identification"].split(":")
    code     = parts[1] if len(parts)>=2 else ""
    modifier = parts[2] if len(parts)>=3 else ""
    return code, modifier

def validate_superior_magellan_modifiers(claim: Dict[str,List[str]]) -> List[Tuple[str,str]]:
    """1) Superior & Magellan require a modifier on every 97151–97158."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc   = [s for s in mapped if "Service Identification" in s]
    # detect payer
    payer = next(
      (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
      next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg),
           "")
    )
    if any(x in payer for x in ("SUPERIOR","MAGELLAN")):
        for seg in svc:
            code,mod = _split_sv1(seg)
            if code.startswith("9715") and not mod:
                errors.append((
                  f"{payer}: CPT {code} missing modifier",
                  "Add treating-therapist modifier on every 97151–97158"
                ))
    return errors

def validate_firstcare_modifiers(claim: Dict[str,List[str]]) -> List[Tuple[str,str]]:
    """2) First Care: 97153 no modifier; 97151/55/56 must use HO."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc    = [s for s in mapped if "Service Identification" in s]
    payer  = next(
      (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
      next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg),
           "")
    )
    if "FIRST CARE" in payer:
        for seg in svc:
            code,mod = _split_sv1(seg)
            if code=="97153" and mod:
                errors.append((
                  "First Care: 97153 should not have modifier",
                  "Remove modifier on CPT 97153"
                ))
            if code in ("97151","97155","97156") and mod!="HO":
                errors.append((
                  f"First Care: CPT {code} modifier is '{mod or 'MISSING'}'",
                  "Use HO modifier on 97151, 97155, and 97156"
                ))
    return errors

def validate_taxonomy_rules(claim: Dict[str,List[str]]) -> List[Tuple[str,str]]:
    """3) Superior, WellPoint, First Care require taxonomy in 24J (PE*PXC) & 33B (BI*PXC)."""
    errors = []
    mapped = [map_segment(s) for s in claim["billing_provider"] + claim["subscriber"] + 
                                    claim["patient"]        + claim["claim_segments"]]
    payer  = next(
      (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
      next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg),
           "")
    )
    if any(x in payer for x in ("SUPERIOR","WELLPOINT","FIRST CARE")):
        has_ind = any(
          seg.get("Provider Code")=="PE" and 
          seg.get("Reference Identification Qualifier")=="PXC" and 
          seg.get("Reference Identification","").strip()
          for seg in mapped
        )
        has_grp = any(
          seg.get("Provider Code")=="BI" and 
          seg.get("Reference Identification Qualifier")=="PXC" and 
          seg.get("Reference Identification","").strip()
          for seg in mapped
        )
        if not has_ind:
            errors.append((
              f"{payer}: missing individual taxonomy (24J)",
              "Add PRV*PE*PXC with taxonomy code"
            ))
        if not has_grp:
            errors.append((
              f"{payer}: missing group taxonomy (33B)",
              "Add PRV*BI*PXC with taxonomy code"
            ))
    return errors

def validate_tricare_rules(claim: Dict[str,List[str]]) -> List[Tuple[str,str]]:
    """
    4) Tricare — no modifiers; treating-therapist taxonomy in 24J;
       9) 97151 units ≤ 32.
    """
    errors = []
    mapped_all = [map_segment(s) for s in claim["billing_provider"] + claim["subscriber"] + 
                                     claim["patient"]        + claim["claim_segments"]]
    svc       = [s for s in mapped_all if "Service Identification" in s]
    payer     = next(
      (seg["Payer Name"].upper() for seg in mapped_all if "Payer Name" in seg),
      next((seg["Application Receiver's Code"].upper() for seg in mapped_all if "Application Receiver's Code" in seg),
           "")
    )
    if "TRICARE" in payer:
        # no modifiers
        for seg in svc:
            code,mod = _split_sv1(seg)
            if mod:
                errors.append((
                  f"Tricare: CPT {code} should not have modifier",
                  "Remove modifiers for Tricare"
                ))
        # taxonomy
        if not any(
          seg.get("Provider Code")=="PE" and 
          seg.get("Reference Identification Qualifier")=="PXC" and 
          seg.get("Reference Identification","").strip()
          for seg in mapped_all
        ):
            errors.append((
              "Tricare: missing treating-therapist taxonomy (24J)",
              "Add PRV*PE*PXC with taxonomy code"
            ))
        # units cap
        for seg in svc:
            code,_ = _split_sv1(seg)
            if code=="97151":
                try:
                    if float(seg.get("Units/Days","0"))>32:
                        errors.append((
                          "Tricare: 97151 exceeds 32 units",
                          "Limit 97151 to 32 units per Tricare rules"
                        ))
                except ValueError:
                    pass
    return errors

def validate_bcbs_modifiers(claim: Dict[str,List[str]]) -> List[Tuple[str,str]]:
    """6) BCBS — bill as-is, no modifiers."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc    = [s for s in mapped if "Service Identification" in s]
    payer  = next(
      (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
      next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg),
           "")
    )
    if "BCBS" in payer:
        for seg in svc:
            code,mod = _split_sv1(seg)
            if mod:
                errors.append((
                  f"BCBS: CPT {code} should not have modifier",
                  "Remove all modifiers for BCBS"
                ))
    return errors

def validate_rates_based_Aloha(claim: Dict[str, List[dict]]) -> List[Tuple[str, str]]:
    """
    Validates Tricare insurance rates post and pre 05/01 cutoff with role-based rates.
    CPT codes and roles determine expected unit rate.
    """
    errors = []

    mapped_all = [map_segment(seg) for seg in (
        claim.get("billing_provider", []) +
        claim.get("subscriber", []) +
        claim.get("patient", []) +
        claim.get("claim_segments", [])
    )]
    mapped_claim_segments = [map_segment(seg) for seg in claim.get("claim_segments", [])]

    payer = ""
    provider_name = ""

    for seg in mapped_all:
        if seg and seg.get("Segment ID") == "NM1":
            entity_code = seg.get("Entity Identifier Code", "")
            if entity_code == "PR":  # Payer
                payer = seg.get("Name Last or Organization Name", "").upper()
            elif entity_code == "82":
                last_name = seg.get("Name Last or Organization Name", "").upper()
                first_name = seg.get("Name First", "").upper()
                provider_name = f"{last_name} {first_name}".strip()

    # Check if insurance is Tricare
    is_tricare = (
        "TRICARE" in payer or
        ("TRI" in payer and ("WEST" in payer or "EAST" in payer))
    )


    cutoff_date = datetime(2025, 5, 1)

    is_rbt = any(rbt in provider_name for rbt in rbt_names)
    is_bcba = any(bcba in provider_name for bcba in bcba_identifiers)


    dtp_segments = [seg for seg in mapped_claim_segments if seg.get("Segment ID") == "DTP" and seg.get("Date/Time Qualifier") == "472"]
    svc_segments = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    for i, svc_seg in enumerate(svc_segments):
        expected = None
        service_id = svc_seg.get("Service Identification", "")
        code = service_id.split(":")[1] if ":" in service_id else ""

        amt_str = svc_seg.get("Monetary Amount", "").strip()
        units_str = svc_seg.get("Units/Days", "").strip()

        svc_date_str = ""
        if i < len(dtp_segments):
            svc_date_str = dtp_segments[i].get("Date Time Period", "")

        svc_date = datetime.strptime(svc_date_str, "%Y%m%d")
        after_cutoff = svc_date >= cutoff_date

        total_charge = float(amt_str)
        units = float(units_str)
        unit_rate = round(total_charge / units, 2)
        expected = 0.00     

        if is_tricare and after_cutoff:
            if code == "97153":
                if is_bcba:
                    expected = 31.25
                elif is_rbt:
                    expected = 17.24
                else:
                    expected = 30.00
            elif code in ("97155", "97156"):
                if is_bcba:
                    expected = 31.25
                else:
                    expected = 40.00
            elif code == "97151":
                if is_bcba:
                    expected = 32.19
                else:
                    expected = 40.00  # Other Insurance Rate
        else:
            # Other insurance (not Tricare) rates
            if code == "97153":
                expected = 30.00
            elif code in ("97155", "97156", "97151"):
                expected = 40.00

        if unit_rate != expected:
            errors.append((
                f"Tricare: Incorrect rate for {code} on {svc_date_str} — got ${unit_rate:.2f}/unit",
                f"Use ${expected:.2f} per unit for CPT {code} under Tricare"
            ))

    return errors





def validate_wellpoint_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """8) WellPoint — HO modifier required on CPTs 97151, 97155, 97156; no modifier allowed on 97153."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]

    # Find payer name from NM1 segment where Entity Identifier Code == 'PR'
    payer_name = ""
    for seg in mapped:
        if seg.get("Segment ID") == "NM1" and seg.get("Entity Identifier Code") == "PR":
            payer_name = seg.get("Name Last or Organization Name", "").upper()
            print(f"DEBUG: Found payer name in NM1*PR segment: '{payer_name}'")
            break
    else:
        print("DEBUG: No NM1*PR segment found for payer.")

    if "WELLPOINT" not in payer_name:
        print("DEBUG: WellPoint not detected as payer, skipping modifier validation.")
        return errors

    print("DEBUG: WellPoint detected as payer. Validating modifiers...")

    svc = [s for s in mapped if "Service Identification" in s]

    # Map segment index to LX line number for service lines
    lx_lines = {}
    current_lx = None
    for i, seg in enumerate(mapped):
        if seg.get("Segment ID") == "LX":
            current_lx = seg.get("Assigned Number", None)
        lx_lines[i] = current_lx  # assign current LX number to segment index

    # Collect errors by (code, mod) with list of line numbers
    error_records = {}

    for i, seg in enumerate(mapped):
        if "Service Identification" not in seg:
            continue

        code, mod = _split_sv1(seg)
        line_num = lx_lines.get(i, "Unknown")

        # CPT 97153 should NOT have a modifier
        if code == "97153" and mod:
            key = (code, mod or "", "no_modifier_expected")
            error_records.setdefault(key, []).append(line_num)

        # CPTs 97151, 97155, 97156 must have HO modifier
        if code in ("97151", "97155", "97156") and mod != "HO":
            key = (code, mod or "", "ho_modifier_required")
            error_records.setdefault(key, []).append(line_num)

    # Now create error messages with combined line numbers
    for (code, mod, err_type), lines in error_records.items():
        line_str = ", ".join(str(l) for l in sorted(set(lines)))
        if err_type == "no_modifier_expected":
            errors.append((
                f"WellPoint: CPT {code} should NOT have any modifier; found on line(s): {line_str}.",
                f"Please remove modifiers from CPT {code} on line(s): {line_str}."
            ))
        elif err_type == "ho_modifier_required":
            errors.append((
                f"WellPoint: CPT {code} modifier is '{mod or 'MISSING'}'; issue on line(s): {line_str}.",
                f"Please use 'HO' modifier for CPTs 97151, 97155, and 97156 under WellPoint on line(s): {line_str}."
            ))

    return errors

def validate_firstcare_unit_limit(claim: Dict[str,List[str]]) -> List[Tuple[str,str]]:
    """From Vincent 04/10/2025: First care total CPT units ≤ 32 per day."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc    = [s for s in mapped if "Service Identification" in s]
    payer  = next(
      (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
      next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg),
           "")
    )
    if "FIRST CARE" in payer:
        total = 0.0
        for seg in svc:
            code,_ = _split_sv1(seg)
            if code.startswith("9715"):
                try:
                    total += float(seg.get("Units/Days","0"))
                except:
                    pass
        if total>32:
            errors.append((
              "First Care: total ABA units exceed 32",
              "Combine all 97151–97158 units to no more than 32 per day"
            ))
    return errors


def validate_tricare_bcba_pre_may2025(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 9: If we received BCBA provider in Tricare insurance, take $40.00 (until 04/30/2025)."""
    errors = []

    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]

    payer = ""
    provider_name = ""

    cutoff_date = datetime(2025, 5, 1)

    service_lines = []
    current_service = {}

    for seg in mapped_all:
        if seg.get("Segment ID") == "NM1":
            if seg.get("Entity Identifier Code") == "PR":
                payer = seg.get("Name Last or Organization Name", "").upper()
                print("Payer detected:", payer)
            elif seg.get("Entity Identifier Code") == "82":
                provider_name = seg.get("Name Last or Organization Name", "").upper()
                print("Provider name detected:", provider_name)

        elif seg.get("Segment ID") == "SV1":
            current_service = {
                "code": seg.get("Service Identification", "").split(":")[1] if ":" in seg.get("Service Identification", "") else "",
                "amount": float(seg.get("Monetary Amount", "0")),
                "units": float(seg.get("Units/Days", "1")),
                "date": None
            }

        elif seg.get("Segment ID") == "DTP" and seg.get("Date Time Period Format Qualifier") == "D8":
            date_str = seg.get("Date Time Period", "")
            try:
                service_date = datetime.strptime(date_str, "%Y%m%d")
                if current_service:
                    current_service["date"] = service_date
                    service_lines.append(current_service)
                    current_service = {}
            except Exception as e:
                pass

    is_tricare = any(keyword in payer for keyword in ["TRICARE", "TRIWEST", "TRIEAST", "TRI",])

    # is_tricare = "TRICARE" in payer or ("TRI" in payer and ("WEST" in payer or "EAST" in payer))
    is_bcba_override = any(bcba_key in provider_name for bcba_key in bcba_identifiers)

    if is_tricare and is_bcba_override:
        for svc in service_lines:
            if svc["date"] and svc["date"] < cutoff_date:
                unit_rate = round(svc["amount"] / svc["units"], 2)
                expected_rate = 40.00
                if unit_rate != expected_rate:
                        errors.append((
                            f"Tricare BCBA pre-05/01/2025: Incorrect rate for {svc['code']} — got ${unit_rate:.2f}/unit",
                            f"Use ${expected_rate:.2f} per unit for CPT {svc['code']} for BCBA provider under Tricare before 05/01/2025"
                        ))
    return errors



def validate_behavioral_focus_practice_rules(claim: Dict[str,List[str]]) -> List[Tuple[str,str]]:
    """Combine all Behavioral Focus LLC rules that can be EDI-checked."""
    return (
        validate_superior_magellan_modifiers(claim)
      + validate_firstcare_modifiers(claim)
      + validate_taxonomy_rules(claim)
      + validate_tricare_rules(claim)
      + validate_bcbs_modifiers(claim)
      + validate_rates_based_Aloha(claim)
      + validate_wellpoint_modifiers(claim)
      + validate_firstcare_unit_limit(claim)
    )
