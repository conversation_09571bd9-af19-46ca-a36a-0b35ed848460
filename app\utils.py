import os
from dotenv import load_dotenv
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

load_dotenv()  # Load variables from .env

bearer_scheme = HTTPBearer()

# Define your tokens (for demo purposes; use env vars in production)
GENERAL_TOKEN = os.getenv("GENERAL_TOKEN")
ABA_TOKEN = os.getenv("API_KEY_ABA")
DAQ_TOKEN = os.getenv("DAQ_TOKEN")
THERA_TOKEN = os.getenv("THERA_TOKEN")

def verify_general_token(credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme)):
    received = credentials.credentials.strip()
    print("Received token:", repr(received))
    print("Expected token:", repr(GENERAL_TOKEN))
    if received != GENERAL_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing general authorization token",
        )

def verify_aba_token(credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme)):
    received = credentials.credentials.strip()
    if received != ABA_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing ABA authorization token",
        )

def verify_daq_token(credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme)):
    received = credentials.credentials.strip()
    if received != DAQ_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing DAQ authorization token",
        )

def verify_thera_token(credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme)):
    received = credentials.credentials.strip()
    if received != THERA_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing Theralympics authorization token",
        )
