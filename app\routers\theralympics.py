# app/routers/theralympics.py

from fastapi import APIRouter, UploadFile, File, HTTPException, Request, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from app.validators.theralympics.main import validate_837_content, get_daily_count_endpoint, trigger_daily_report, reset_database
from app.utils import verify_thera_token

templates = Jinja2Templates(directory="app/templates")
router = APIRouter(prefix="/theralympics", tags=["Theralympics"])

@router.get("/", response_class=HTMLResponse)
async def theralympics_home(request: Request):
    return templates.TemplateResponse("theralympics_home.html", {"request": request})

@router.post("/validate-837", response_class=HTMLResponse)
async def theralympics_validate(request: Request, file: UploadFile = File(...)):
    try:
        raw = (await file.read()).decode("utf-8").strip()
        result = await validate_837_content(raw, file.filename)
        return templates.TemplateResponse(
            "theralympics_results.html",
            {"request": request, **result}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/daily-count", response_class=JSONResponse)
async def theralympics_daily_count():
    return await get_daily_count_endpoint()

@router.post("/trigger-daily-report", response_class=JSONResponse, dependencies=[Depends(verify_thera_token)])
async def theralympics_trigger_daily_report():
    return await trigger_daily_report()

@router.post("/reset-database", response_class=JSONResponse)
async def theralympics_reset_database(confirm: str = None):
    return await reset_database(confirm)
