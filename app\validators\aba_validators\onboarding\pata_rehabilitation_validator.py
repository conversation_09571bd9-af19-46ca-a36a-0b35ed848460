from typing import List, Set, Tuple, Dict
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

from ..utils import map_segment



GROUP_NPI = "**********"



def validate_pata_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []

    segments = claim.get("claim_segments") or []
    mapped_segments = [mapped for mapped in (map_segment(seg) for seg in segments if seg) if mapped]
    # print(f"Total claim segments mapped: {len(mapped_segments)}")

    service_lines = [seg for seg in mapped_segments if seg and "Service Identification" in seg]
    # print(f"Service lines found: {len(service_lines)}")

    for seg in service_lines:
        service_id = seg.get("Service Identification", "")
        # print(f"\nProcessing service line: {service_id}")
        parts = service_id.split(":")

        if len(parts) < 2:
            # print("Skipping: Not enough parts in Service Identification")
            continue

        procedure_qualifier = parts[0].strip()
        cpt = parts[1].strip()

        modifiers = [p.strip() for p in parts[2:6] if p.strip()]
        modifier = modifiers[0] if modifiers else procedure_qualifier

        # print(f"Extracted CPT code: '{cpt}'")

        expected_rate = None
        if cpt == "31579":
            if not modifiers:
                expected_rate = 285.00
        elif cpt == "92521":
            expected_rate = 172.00
        elif cpt == "92522":
            expected_rate = 140.00
        elif cpt == "92523":
            expected_rate = 290.00
        elif cpt == "92524":
            expected_rate = 145.00

        # if expected_rate is None:
        #     # print(f"No expected rate defined for CPT {cpt} with modifier '{modifier}', skipping")
        #     continue

        total_charge_str = seg.get("Monetary Amount", "").strip()
        units_str = seg.get("Units/Days", "").strip()
        # print(f"Total charge string: '{total_charge_str}', Units string: '{units_str}'")

        # try:
        total_charge = float(total_charge_str)
        units = float(units_str)


        actual_per_unit = round(total_charge / units, 2)
        # print(f"Actual per-unit rate calculated: {actual_per_unit}, Expected per-unit rate: {expected_rate}")

        if actual_per_unit != expected_rate:
            error_msg = f"Incorrect rate for CPT {cpt}: charged {actual_per_unit}/unit, expected {expected_rate}/unit"
            fix_msg = f"Set per-unit charge to {expected_rate}"
            errors.append((error_msg, fix_msg))
            print(f"Error appended: {error_msg}")

    # print(f"\nTotal errors found: {len(errors)}")
    return errors




def validate_pata_rehabilitation_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Honey Bee Therapy-specific validation rules"""
    return (
        validate_pata_rates(claim)
    )
