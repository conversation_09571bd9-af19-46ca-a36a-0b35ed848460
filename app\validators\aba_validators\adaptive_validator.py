from typing import List, Tu<PERSON>, Dict
from .utils import map_segment
from datetime import datetime
from zoneinfo import ZoneInfo

# Adaptive LLC specific constants
GROUP_NPI = "**********"
PROVIDER_NPI_SABRINA = "**********"
PROVIDER_NPI_SHANITA = "**********"
PROVIDER_NPI_ILONA = "**********"

PROVIDER_RATES = {
    "SHANITA ALLEN": {"97151": 31.25, "97153": 18.75, "97155": 31.25, "97156": 31.25},  # BCBS
    "ILONA DARLING": {"97151": 31.25, "97153": 18.75, "97155": 31.25, "97156": 31.25},  # UMR
    "SABRINA KING": {"97153": 16.91, "97151": 22.80, "97155": 22.80, "97156": 22.80}    # AETNA
}

HM_MODIFIER = "HM"
HO_MODIFIER = "HO"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now().astimezone(IST_TIMEZONE).replace(tzinfo=None)



def validate_adaptive_cpt_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: Validate rates only for specific providers with their assigned rate tables."""
    
    errors = []
    
    # print("Incoming claim data:", claim)
    
    mapped_all = []
    for segment_group in [claim["billing_provider"], claim["subscriber"], claim["patient"], claim["claim_segments"]]:
        mapped_all.extend([map_segment(seg) for seg in segment_group])
    
    provider_name = ""
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") == "82":
            first_name = seg.get("Name First", "").strip().upper()
            last_name = seg.get("Name Last or Organization Name", "").strip().upper()
            if first_name and last_name:
                provider_name = f"{first_name} {last_name}"
            elif last_name:
                provider_name = last_name
            break
    
    # print(f"Resolved provider name: {provider_name}")
    
    rate_table = PROVIDER_RATES.get(provider_name, {})
    
    # print(f"Rate table for {provider_name}: {rate_table}")
    
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    
    for seg in mapped_claim_segments:
        if "Service Identification" in seg and "Units/Days" in seg:
            parts = seg["Service Identification"].split(":")
            cpt_code = parts[1] if len(parts) > 1 else ""
            
            if not cpt_code:
                continue
            
            units = int(seg["Units/Days"])
            actual_total = float(seg.get("Monetary Amount", 0))
            
            per_unit_rate = actual_total / units
            expected_rate = rate_table.get(cpt_code)
            
            # print(f"Checking CPT {cpt_code} for {provider_name} - Per Unit Rate: {per_unit_rate}, Expected Rate: {expected_rate}")
            
            if expected_rate and per_unit_rate != expected_rate:
                errors.append((
                    f"Provider {provider_name}: CPT {cpt_code} has rate ${per_unit_rate:.2f} instead of ${expected_rate}",
                    f"Correct total for CPT {cpt_code} should be ${expected_rate * units:.2f} (${expected_rate} × {units} units)"
                ))
    
    # print("Validation errors:", errors)
    
    return errors

def validate_adaptive_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: Validate the modifiers (HM, HO) used with specific CPT codes."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]

    for seg in mapped_claim_segments:
        if "Procedure Code" in seg:
            cpt_code = seg["Procedure Code"]
            modifier = seg.get("Procedure Modifier", "")
            if cpt_code == "97153" and modifier != HM_MODIFIER:
                errors.append((
                    f"Claim with CPT 97153 does not have the required HM modifier, found {modifier}",
                    f"Use HM modifier for CPT 97153"
                ))
            elif cpt_code in ["97155", "97156"] and modifier != HO_MODIFIER:
                errors.append((
                    f"Claim with CPT {cpt_code} does not have the required HO modifier, found {modifier}",
                    f"Use HO modifier for CPT {cpt_code}"
                ))

    return errors


def validate_provider_npi_by_insurance(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Ensure provider name and NPI match the insurance payer rules."""
    errors = []

    insurance_provider_mapping = {
        "BCBS OF FL": ("SHANITA ALLEN", "**********"),
        "UMR - HARRINGTON": ("ILONA DARLING", "**********"),
        "AETNA": ("SABRINA KING", "**********"),
    }

    # Get payer name
    payer_segment = next((map_segment(seg) for seg in claim["subscriber"] if "Name Last or Organization Name" in seg), None)
    # print("payer_segment:",payer_segment,'*'*50)
    payer_name = payer_segment.get("Name Last or Organization Name", "").strip().upper() if payer_segment else ""
    # print("payer_name:",payer_name,'*'*50)

    if payer_name not in insurance_provider_mapping:
        errors.append((f"Unsupported payer: '{payer_name}'", "Payer must be one of: BCBS OF FL, UMR - HARRINGTON, AETNA."))
        return errors

    expected_name, expected_npi = insurance_provider_mapping[payer_name]
    # print("expected_name:",expected_name,'*'*50)
    # print("expected_npi:",expected_npi,'*'*50)

    # Get billing provider info
    billing_provider = next((map_segment(seg) for seg in claim["billing_provider"]), {})
    # print("billing_provider:",billing_provider,'*'*50)
    actual_first = billing_provider.get("Name First", "").strip().upper()
    # print("actual_first:",actual_first,'*'*50)
    actual_last = billing_provider.get("Name Last or Organization Name", "").strip().upper()
    # print("actual_last:",actual_last,'*'*50)
    actual_name = f"{actual_first} {actual_last}".strip()
    # print("actual_name:",actual_name,'*'*50)
    actual_npi = billing_provider.get("Identification Code", "").strip()
    # print("actual_npi:",actual_npi,'*'*50)

    if actual_name != expected_name:
        errors.append((f"Incorrect provider name for payer '{payer_name}': Found '{actual_name}'", f"provider name should be '{expected_name}'"))

    if actual_npi != expected_npi:
        errors.append((f"Incorrect NPI for payer '{payer_name}': Found '{actual_npi}'", f"NPI should be: '{expected_npi}'"))

    return errors



def validate_modifiers_by_insurance(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Check modifiers are applied correctly based on payer-specific rules."""
    errors = []

    payer_segment = next((map_segment(seg) for seg in claim["subscriber"] if "Name Last or Organization Name" in seg), None)
    # print("payer_segment:",payer_segment,'*'*50)
    payer_name = payer_segment.get("Name Last or Organization Name", "").strip().upper() if payer_segment else ""
    # print("payer_name:",payer_name,'*'*50)
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    # print("mapped_claim_segments:",mapped_claim_segments,'*'*50)
    for seg in mapped_claim_segments:
        cpt = seg.get("Procedure Code", "")
        # print("cpt:",cpt,'*'*50)
        mod = seg.get("Procedure Modifier", "")
        # print("mod:",mod,'*'*50)
        if payer_name == "UMR - HARRINGTON":
            if cpt == "97153" and mod != HM_MODIFIER:
                errors.append((f"CPT 97153 must have HM modifier for UMR - HARRINGTON.", "Use HM modifier."))
            elif cpt == "97155" and mod != HO_MODIFIER:
                errors.append((f"CPT 97155 must have HO modifier for UMR - HARRINGTON.", "Use HO modifier."))
        elif payer_name in ["BCBS OF FL", "AETNA"]:
            if mod in [HM_MODIFIER, HO_MODIFIER]:
                errors.append((f"{payer_name} should not have modifier '{mod}' for CPT {cpt}.", f"Remove modifier '{mod}'."))

    return errors


def validate_adaptive_claim_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Adaptive LLC-specific validation rules."""
    return (
        validate_adaptive_cpt_rates(claim) +
        validate_provider_npi_by_insurance(claim) +
        validate_modifiers_by_insurance(claim) +
        validate_adaptive_modifiers(claim)
    )
