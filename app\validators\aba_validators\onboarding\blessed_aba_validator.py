from typing import List, <PERSON><PERSON>, Dict
from ..utils import map_segment

# Blessed ABA constants
BLESSED_GROUP_NPI = "**********"  # <PERSON>
BLESSED_BCBA_NAME = "ANA DIAZ HERNANDEZ"
TEXAS_INSURANCES = {"AMERIGROUP", "TEXAS CHILDREN'S HEALTH PLAN"}
REQUIRED_TAXONOMY_CODES = {"24J", "33B"}
HO_MODIFIER = "HO"
HO_MODIFIER_CPTS = {"97153", "97155", "97156"}




def validate_blessed_billing_provider_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """3)Ensure billing and rendering NPIs are BLESSED_GROUP_NPI."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]

    # Billing Provider NPI (usually Entity Identifier Code "85")
    billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
    if billing_npi != BLESSED_GROUP_NPI:
        errors.append((
            f"Billing NPI is {billing_npi}, expected {BLESSED_GROUP_NPI}",
            f"Use Billing Provider NPI {BLESSED_GROUP_NPI} (Ana Diaz Hernandez) in NM1*85 segment"
        ))

    # Service lines (SV1) rendering NPI check
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    for seg in service_lines:
        rendering_npi = seg.get("Rendering Provider Identifier", None)
        if rendering_npi and rendering_npi != BLESSED_GROUP_NPI:
            errors.append((
                f"Service line uses Rendering NPI {rendering_npi} instead of {BLESSED_GROUP_NPI}",
                f"Use Rendering Provider NPI {BLESSED_GROUP_NPI} for all service lines"
            ))

    return errors



def validate_blessed_taxonomy_codes(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    4)For Amerigroup and Texas Children's Health Plan insurances,
    taxonomy codes 24J and 33B must be included.
    """
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]

    insurances = {seg.get("Name Last or Organization Name", "").upper() for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"}

    texas_insurance_present = bool(insurances.intersection(TEXAS_INSURANCES))

    if texas_insurance_present:
        taxonomy_codes = {seg.get("Provider Taxonomy Code", "").upper() for seg in mapped_all if "Provider Taxonomy Code" in seg}

        missing_codes = REQUIRED_TAXONOMY_CODES - taxonomy_codes
        if missing_codes:
            errors.append((
                f"Missing taxonomy codes: {', '.join(missing_codes)} for Texas Medicaid claims",
                "Ensure taxonomy codes 24J and 33B are included for Texas Medicaid HMO insurances"
            ))
    return errors


def validate_blessed_ho_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    5)If Rendering Provider is Ana Diaz Hernandez and CPT is 97153/97155/97156,
    HO modifier must be present.
    For other providers, HO modifier should NOT be present.
    """
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) < 2:
            continue
        cpt = parts[1]
        modifiers = parts[2].split(",") if len(parts) > 2 else []
        rendering_npi = seg.get("Rendering Provider Identifier", None)

        # Check if rendering provider is Ana Diaz Hernandez (NPI)
        is_ana = rendering_npi == BLESSED_GROUP_NPI

        if cpt in HO_MODIFIER_CPTS:
            if is_ana and HO_MODIFIER not in modifiers:
                errors.append((
                    f"CPT {cpt} by Ana Diaz Hernandez missing HO modifier",
                    "Add HO modifier for Ana Diaz Hernandez on CPTs 97153, 97155, 97156"
                ))
            if not is_ana and HO_MODIFIER in modifiers:
                errors.append((
                    f"CPT {cpt} by non-Ana provider should NOT have HO modifier",
                    "Remove HO modifier for providers other than Ana Diaz Hernandez"
                ))
    return errors


def validate_blessed_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combine Blessed ABA practice validation rules."""
    return (
        validate_blessed_billing_provider_npi(claim) +
        validate_blessed_ho_modifier(claim) +
        validate_blessed_taxonomy_codes(claim)
    )
