from typing import List, <PERSON><PERSON>, Dict
from .utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
GROUP_NPI = "**********"
PROVIDER_NPI = "**********"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)

def validate_bbs_aetna_group_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: Aetna claims must use Group NPI (**********) in billing provider and service lines."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_aetna = any(seg.get("Name Last or Organization Name", "").upper() == "AETNA" for seg in mapped_all if seg.get("Entity Identifier Code") == "PR")
    
    if is_aetna:
        billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
        if billing_npi != GROUP_NPI:
            errors.append((
                f"Aetna claim uses Billing NPI {billing_npi} instead of Group NPI {GROUP_NPI}",
                f"Use Group NPI {GROUP_NPI} in NM1*85 for Aetna claims"
            ))
        
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            rendering_npi = seg.get("Rendering Provider Identifier", None)
            if rendering_npi and rendering_npi != GROUP_NPI:
                errors.append((
                    f"Aetna service line uses Rendering NPI {rendering_npi} instead of Group NPI {GROUP_NPI}",
                    f"Use Group NPI {GROUP_NPI} in SV1 for Aetna claims"
                ))
    return errors

def validate_bbs_non_aetna_provider_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 3: Non-Aetna claims must use Provider NPI (**********, Alhena Hernandez)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    is_aetna = any(seg.get("Name Last or Organization Name", "").upper() == "AETNA" for seg in mapped_all if seg.get("Entity Identifier Code") == "PR")
    
    if not is_aetna:
        billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "82"), None)
        if billing_npi != PROVIDER_NPI:
            errors.append((
                f"Non-Aetna claim uses Billing NPI {billing_npi} instead of Provider NPI {PROVIDER_NPI}",
                f"Use Provider NPI {PROVIDER_NPI} in NM1*85 for non-Aetna claims"
            ))
        
        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        for seg in service_lines:
            rendering_npi = seg.get("Rendering Provider Identifier", None)
            if rendering_npi and rendering_npi != PROVIDER_NPI:
                errors.append((
                    f"Non-Aetna service line uses Rendering NPI {rendering_npi} instead of Provider NPI {PROVIDER_NPI}",
                    f"Use Provider NPI {PROVIDER_NPI} in SV1 for non-Aetna claims"
                ))
    return errors

def validate_bbs_same_dos_cpt_provider(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 5: Same DOS, CPT, and provider requires XS modifier on one line."""
    import logging
    logger = logging.getLogger("aba_validators.bbs_validator")
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    # Map each SV1 to its following DTP (DOS)
    dos_cpt_map = {}
    for idx, seg in enumerate(mapped_claim_segments):
        if "Service Identification" not in seg:
            continue
        parts = seg["Service Identification"].split(":")
        if len(parts) < 2:
            continue
        cpt = parts[1]
        modifier = parts[2] if len(parts) > 2 else ""
        # Find the next DTP after this SV1
        dos = None
        for j in range(idx + 1, len(mapped_claim_segments)):
            dtp_seg = mapped_claim_segments[j]
            if dtp_seg.get("Date/Time Qualifier") == "472" and dtp_seg.get("Date Time Period Format Qualifier") == "D8":
                dos = dtp_seg.get("Date Time Period")
                break
            if "Service Identification" in dtp_seg:
                break
        logger.debug(f"SV1 idx={idx}, CPT={cpt}, mapped DOS={dos}")
        if not dos:
            continue
        key = (dos, cpt)
        dos_cpt_map.setdefault(key, []).append((seg, modifier))

    # Log the grouping
    for (dos, cpt), lines in dos_cpt_map.items():
        logger.debug(f"Grouped DOS={dos}, CPT={cpt}, count={len(lines)}")

    # Check for duplicates
    for key, lines in dos_cpt_map.items():
        if len(lines) > 1:
            xs_count = sum(1 for _, mod in lines if "XS" in mod)
            logger.debug(f"Checking XS for DOS={key[0]}, CPT={key[1]}: {len(lines)} lines, {xs_count} with XS")
            if xs_count != 1:
                errors.append((
                    f"Multiple lines for CPT {key[1]} on DOS {key[0]}: {len(lines)} lines, {xs_count} with XS",
                    "Ensure exactly one line has XS modifier for same DOS and CPT"
                ))
    return errors

def validate_bbs_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all BBS-specific validation rules."""
    return (
        validate_bbs_aetna_group_npi(claim) +
        validate_bbs_non_aetna_provider_npi(claim) +
        validate_bbs_same_dos_cpt_provider(claim)
    )