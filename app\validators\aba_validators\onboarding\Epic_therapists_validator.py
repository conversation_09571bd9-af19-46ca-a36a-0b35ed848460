from typing import List, Tu<PERSON>, Dict
from ..utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Epic therapists practice constants
GROUP_NPI = "**********"
TAX_ID = "*********"
IST_TIMEZONE = ZoneInfo("America/Chicago")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)

def validate_epic_gn_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: All speech claims must have GN modifier in SV1 segments."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    sv1_segments = [seg for seg in mapped_claim_segments if seg.get("Segment ID", "") == "SV1" or "Service Identification" in seg]
    
    for seg in sv1_segments:
        modifiers = seg.get("Service Identification", "").split(":")
        # Modifiers usually start after CPT code in SV1, e.g. SV1*HC:92507:GN
        if len(modifiers) < 3 or "GN" not in modifiers[2:]:
            errors.append((
                "Speech claim missing GN modifier in SV1 segment",
                "Add GN modifier to SV1 segment for speech claims"
            ))
    return errors

def validate_epic_amerigroup_molina(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    Rule: For Amerigroup and Molina Medicaid HMO claims:
    - Must have referring provider segment with taxonomy
    - HCFA form must have it (simulated as present in claim segments)
    - Must include U5 modifier in SV1
    """
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Check if payer is Amerigroup or Molina
    is_medicaid_hmo = any(
        seg.get("Name Last or Organization Name", "").upper() in ["AMERIGROUP", "MOLINA"]
        for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"
    )
    if is_medicaid_hmo:
        has_referring_provider = any(
            seg.get("Segment ID") == "NM1" and seg.get("Entity Identifier Code") == "DN" for seg in mapped_all
        )
        has_taxonomy = any(
            seg.get("Segment ID") == "PRV" and seg.get("Provider Code") == "PXC" for seg in mapped_all
        )
        if not has_referring_provider:
            errors.append((
                "Medicaid HMO claim missing referring provider segment (NM1*DN)",
                "Add referring provider (NM1*DN) segment with proper taxonomy"
            ))
        if not has_taxonomy:
            errors.append((
                "Medicaid HMO claim missing taxonomy (PRV*PXC) segment",
                "Add taxonomy segment PRV with Provider Code PXC for referring provider"
            ))
        
        sv1_segments = [seg for seg in mapped_all if seg.get("Segment ID") == "SV1"]
        for seg in sv1_segments:
            modifiers = seg.get("Service Identification", "").split(":")
            if len(modifiers) < 3 or "U5" not in modifiers[2:]:
                errors.append((
                    "Medicaid HMO claim missing U5 modifier in SV1 segment",
                    "Add U5 modifier to SV1 segment"
                ))
    return errors

def validate_epic_bcbs_group_number(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: BCBS claims must include group number."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    is_bcbs = any(
        "BLUE CROSS BLUE SHIELD" in seg.get("Name Last or Organization Name", "").upper()
        for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"
    )
    
    if is_bcbs:
        has_group_number = any(
            seg.get("Segment ID") == "SBR" and seg.get("Insured Group or Policy Number", "") != ""
            for seg in mapped_all
        )
        if not has_group_number:
            errors.append((
                "BCBS claim missing group number in SBR segment",
                "Add group number to SBR segment on BCBS claims"
            ))
    return errors

def validate_epic_uhc_member_id(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: For UHC member IDs starting with '1', add U5 modifier on SV1."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    is_uhc = any(
        "UNITEDHEALTHCARE" in seg.get("Name Last or Organization Name", "").upper()
        for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"
    )
    if is_uhc:
        member_ids = [seg.get("Reference Identification") for seg in mapped_all if seg.get("Segment ID") == "REF"]
        for member_id in member_ids:
            if member_id and member_id.startswith("1"):
                sv1_segments = [seg for seg in mapped_all if seg.get("Segment ID") == "SV1"]
                for seg in sv1_segments:
                    modifiers = seg.get("Service Identification", "").split(":")
                    if len(modifiers) < 3 or "U5" not in modifiers[2:]:
                        errors.append((
                            "UHC claim with member ID starting with 1 missing U5 modifier in SV1",
                            "Add U5 modifier to SV1 segment for UHC claims with member IDs starting with 1"
                        ))
                        break
    return errors

def validate_epic_97_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: All claims must have 97 modifier in SV1 segment for rebill."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    sv1_segments = [seg for seg in mapped_claim_segments if seg.get("Segment ID") == "SV1" or "Service Identification" in seg]
    
    for seg in sv1_segments:
        modifiers = seg.get("Service Identification", "").split(":")
        if len(modifiers) < 3 or "97" not in modifiers[2:]:
            errors.append((
                "Claim missing 97 modifier in SV1 segment for rebill",
                "Add 97 modifier to SV1 segment for rebilled claims"
            ))
    return errors

def validate_epic_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Epic therapists validation rules."""
    return (
        validate_epic_gn_modifier(claim) +
        validate_epic_amerigroup_molina(claim) +
        validate_epic_bcbs_group_number(claim) +
        validate_epic_uhc_member_id(claim) +
        validate_epic_97_modifier(claim)
    )
