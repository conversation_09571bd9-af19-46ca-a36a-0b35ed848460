from typing import List, Tu<PERSON>, Dict
from ..utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Tempo-specific constants
TEMPO_GROUP_NPI = "**********"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
CYNTHIA_MARTINEZ_NPI = "**********"

TARGET_PAYERS = {"AETNA", "CARELON"}

AETNA_RATES = {
    "97153": 18.08,
    "97155": 24.37,
}


def validate_tempo_provider_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """3) All claims should use <PERSON>'s NPI (**********)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["claim_segments"]]
    
    billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
    if billing_npi != CYNTHIA_MARTINEZ_NPI:
        errors.append((
            f"Billing NPI {billing_npi} does not match Cynthia Martinez's NPI {CYNTHIA_MARTINEZ_NPI}.",
            f"Use Cynthia Martinez's NPI {CYNTHIA_MARTINEZ_NPI} in billing_npi."
        ))

    for seg in mapped_all:
        if "Service Identification" in seg:
            rendering_npi = seg.get("Rendering Provider Identifier", "")
            if rendering_npi and rendering_npi != CYNTHIA_MARTINEZ_NPI:
                errors.append((
                    f"Service line uses Rendering NPI {rendering_npi} instead of Cynthia Martinez's NPI {CYNTHIA_MARTINEZ_NPI}.",
                    f"Use Cynthia Martinez's NPI {CYNTHIA_MARTINEZ_NPI} in SV1."
                ))

    return errors



def validate_tempo_cpt_rates_and_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """ 
    No modifiers should be present for Aetna. Rates for specific CPT codes should be checked individually.
    """
    errors = []
    mapped = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped if isinstance(seg, dict) and "Service Identification" in seg]

    payer = next(
        (seg.get("Name Last or Organization Name", "").upper()
         for seg in mapped if seg.get("Entity Identifier Code") == "PR"),
        ""
    )

    for seg in service_lines:
        svc_parts = seg["Service Identification"].split(":")
        if len(svc_parts) < 2:
            continue
        cpt = svc_parts[1]
        modifier = svc_parts[2] if len(svc_parts) > 2 else ""
        # print("modifier",modifier)
        charge = float(seg.get("Monetary Amount", "0"))
        # print("charge", charge, "-" * 50)
        units = float(seg.get("Units/Days", "0"))
        # print("units", units, "-" * 50)

        # if units == 0:
        #     continue

        per_unit_rate = charge / units
        # print("per_unit_rate",per_unit_rate,"*"*50)
        if payer == "AETNA":
            if modifier:
                errors.append((
                    f"Aetna claim contains modifier '{modifier}' on CPT {cpt}.",
                    f"No modifier should be used for Aetna claims."
                ))
        else:
            if cpt == "97153":
                expected_rate = 18.08
                if per_unit_rate != expected_rate:
                    errors.append((
                        f"Aetna CPT {cpt} billed at ${per_unit_rate} per unit, expected ${expected_rate} per unit.",
                        f"Bill CPT {cpt} at exact rate ${expected_rate} per unit for Aetna claims."
                    ))

            elif cpt == "97155":
                expected_rate = 24.37
                if per_unit_rate != expected_rate:
                    errors.append((
                        f"Aetna CPT {cpt} billed at ${per_unit_rate} per unit, expected ${expected_rate} per unit.",
                        f"Bill CPT {cpt} at exact rate ${expected_rate} per unit for Aetna claims."
                    ))

    return errors







def validate_tempo_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:

    return (
        # validate_tempo_provider_npi(claim)+
        validate_tempo_cpt_rates_and_modifiers(claim)
    )
