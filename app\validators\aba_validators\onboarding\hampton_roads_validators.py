from typing import List, <PERSON><PERSON>, Dict, Set
from datetime import datetime
from zoneinfo import ZoneInfo
from ..utils import map_segment


# Practice-specific constants
TAX_ID = "86-1910235"
GROUP_NPI = "**********"
BCBA_PROVIDER_NPIS = {"**********", "**********", "**********"}


# Insurance identifiers
TRICARE_INSURANCES = ["TRICARE", "TRIWEST", "TRICARE EAST", "TRICARE WEST"]
GEHA_INSURANCES = ["GEHA", "GOVERNMENT EMPLOYEES HEALTH ASSOCIATION"]
SENTARA_INSURANCES = ["SENTARA", "SENTARA HEALTH PLAN"]

# Set the current date for validation
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now().astimezone(IST_TIMEZONE).replace(tzinfo=None)


def validate_bcba_billing_for_non_tricare(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    '''1) For all the insurance claims should be submitted under the BCBA Provider except Tricare insurance.'''
    errors = []

    mapped_all = [map_segment(seg) for seg in (claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"])]

    payer = ""
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") == "PR":
            payer = seg.get("Name Last or Organization Name", "").upper()
            print("PAYER", payer, "=" * 50)
            break

    rendering_provider_npi = ""
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") == "82":
            rendering_provider_npi = seg.get("Identification Code", "")
            print("RENDERING NPI", rendering_provider_npi, "+" * 50)
            break

    is_tricare = any(tricare in payer for tricare in TRICARE_INSURANCES)

    is_bcba_provider = rendering_provider_npi in BCBA_PROVIDER_NPIS

    if not is_tricare and not is_bcba_provider:
        errors.append((
            f"Claim billed under provider NPI {rendering_provider_npi} for {payer} insurance",
            f"Non-Tricare claims must be submitted under BCBA providers. Allowed NPIs: {', '.join(BCBA_PROVIDER_NPIS)}"
        ))

    return errors


def validate_taxonomy_and_timing_required(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    '''3) For all the insurance provider Taxonomy and Timing is must in box# 24 J.'''
    errors = []

    # print("Input claim", claim, "*" * 50)

    mapped_all = [map_segment(seg) for seg in (
        claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]
    )]
    # print("Mapped all segments", mapped_all, "*" * 50)

    has_taxonomy = False
    for seg in mapped_all:
        # print("Checking segment for taxonomy", seg, "*" * 50)
        if seg.get("Provider Code") == "PE" and seg.get("Reference Identification Qualifier") == "PXC" and seg.get("Reference Identification", "").strip():
            has_taxonomy = True
            # print("Found taxonomy segment", seg, "*" * 50)
            break
    # print("Has taxonomy", has_taxonomy, "*" * 50)

    has_timing = False
    for seg in mapped_all:
        # print("Checking segment for timing", seg, "*" * 50)
        if "Service Identification" in seg and seg.get("Service Type Code") and seg.get("Service Type Code").strip():
            has_timing = True
            # print("Found timing segment", seg, "*" * 50)
            break
    # print("Has timing", has_timing, "*" * 50)

    if not (has_taxonomy and has_timing):
        error = (
            "Missing taxonomy and timing information in box 24J",
            "Add PRV*PE*PXC with taxonomy code and ensure timing information is present"
        )
        # print("Appending error", error, "*" * 50)
        errors.append(error)

    # print("Final errors", errors, "*" * 50)
    return errors



def validate_tricare_no_modifiers_except_gt(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    '''4) For Tricare insurance there is no provider modifier except GT modifier.'''
    errors = []
    mapped_all = [map_segment(seg) for seg in (
        claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]
    )]
    payer = ""
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") == "PR":
            payer = seg.get("Name Last or Organization Name", "").upper()
            break
        if "Payer Name" in seg:
            payer = seg.get("Payer Name", "").upper()
            break
    is_tricare = any(tricare in payer for tricare in TRICARE_INSURANCES)
    if is_tricare:
        mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
        service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
        for seg in service_lines:
            parts = seg["Service Identification"].split(":")
            code = parts[1] if len(parts) >= 2 else ""
            modifier = parts[2] if len(parts) >= 3 else ""
            if modifier and modifier != 'GT':
                errors.append((
                    f"Tricare: CPT {code} has invalid modifier '{modifier}'",
                    "Tricare insurance only allows GT modifier for telehealth services"
                ))
    return errors


def validate_tricare_treating_therapist_billing(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    '''Rule 5: For Tricare insurance claims should be submitted under the respective treating therapist (rendering provider and treating provider should be the same).'''
    errors = []
    mapped_all = [map_segment(seg) for seg in (
        claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]
    )]
    payer = ""
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") == "PR":
            payer = seg.get("Name Last or Organization Name", "").upper()
            break
        if "Payer Name" in seg:
            payer = seg.get("Payer Name", "").upper()
            break
    is_tricare = any(tricare in payer for tricare in TRICARE_INSURANCES)
    if is_tricare:
        rendering_provider = ""
        treating_provider = ""
        # Find rendering provider (NM1*82)
        for seg in mapped_all:
            if seg.get("Entity Identifier Code") == "82":
                last_name = seg.get("Name Last or Organization Name", "").upper()
                first_name = seg.get("Name First", "").upper()
                rendering_provider = f"{last_name} {first_name}".strip()
                break
        # Find treating provider (NM1*82, or if you have a different segment for treating, use that)
        # Here, we assume treating provider is also NM1*82 (if different, adjust logic)
        for seg in mapped_all:
            if seg.get("Entity Identifier Code") == "85":
                last_name = seg.get("Name Last or Organization Name", "").upper()
                first_name = seg.get("Name First", "").upper()
                treating_provider = f"{last_name} {first_name}".strip()
                break
        if rendering_provider != treating_provider:
            errors.append((
                f"Tricare claim: Rendering provider ('{rendering_provider}') and treating provider ('{treating_provider}') do not match.",
                "For Tricare insurance, claims must be submitted under the respective treating therapist (rendering and treating provider must be the same)."
            ))
    return errors

def parse_time_range(time_str: str):
    """Parse time range string like '1330-1700' into (start_int, end_int)."""
    if not time_str or '-' not in time_str:
        return None, None
    start_str, end_str = time_str.split('-')
    try:
        start = int(start_str)
        end = int(end_str)
        return start, end
    except:
        return None, None

def times_overlap(t1: str, t2: str) -> bool:
    """Return True if two time ranges overlap."""
    s1, e1 = parse_time_range(t1)
    s2, e2 = parse_time_range(t2)
    if None in (s1, e1, s2, e2):
        return False
    # Overlap if ranges intersect
    return max(s1, s2) < min(e1, e2)

def validate_concurrent_hs_modifier_97153_97156(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    '''6) If CPT 97153 and 97156 received for the same DOS with concurrent session (timing),
    we need to add HS modifier for 97156 CPT.'''

    errors = []

    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]

    service_lines = []
    current_service = None

    for seg in mapped_claim_segments:
        if seg.get("Segment ID") == "SV1" and "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            cpt = parts[1] if len(parts) > 1 else ""
            time = parts[6] if len(parts) > 6 else None

            current_service = {
                "segment": seg,
                "cpt": cpt,
                "dos": None,
                "time": time,
            }
            service_lines.append(current_service)

        elif seg.get("Segment ID") == "DTP" and seg.get("Date/Time Qualifier") == "472":
            if current_service:
                current_service["dos"] = seg.get("Date Time Period")

    # Group by DOS and overlapping time ranges
    session_groups = []
    for svc in service_lines:
        if not svc["dos"] or not svc["time"]:
            continue
        placed = False
        for group in session_groups:
            if svc["dos"] == group["dos"] and times_overlap(svc["time"], group["time"]):
                group["services"].append(svc)
                placed = True
                break
        if not placed:
            session_groups.append({"dos": svc["dos"], "time": svc["time"], "services": [svc]})

    # Check for concurrent sessions and HS modifier requirement
    for group in session_groups:
        dos = group["dos"]
        time = group["time"]
        services = group["services"]

        has_97153 = any(s["cpt"] == "97153" for s in services)
        has_97156 = any(s["cpt"] == "97156" for s in services)

        if has_97153 and has_97156:
            for s in services:
                if s["cpt"] == "97156":
                    modifiers = s["segment"].get("Procedure Modifier", "")
                    if "HS" not in modifiers:
                        if modifiers:
                            s["segment"]["Procedure Modifier"] = modifiers + ":HS"
                        else:
                            s["segment"]["Procedure Modifier"] = "HS"
                        error = (
                            f"Concurrent session: CPT 97156 missing HS modifier on DOS {dos}, time {time}",
                            "Add HS modifier to CPT 97156 when concurrent with CPT 97153"
                        )
                        errors.append(error)
    return errors



# def validate_geha_provider_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
#     '''8 For GEHA insurance need to use modifier for the respective provider degree for all the CPTs.'''
#     errors = []
#     mapped_all = [map_segment(seg) for seg in (
#         claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]
#     )]
#     payer = ""
#     for seg in mapped_all:
#         if seg.get("Entity Identifier Code") == "PR":
#             payer = seg.get("Name Last or Organization Name", "").upper()
#             break
#         if "Payer Name" in seg:
#             payer = seg.get("Payer Name", "").upper()
#             break
#     is_geha = any(geha in payer for geha in GEHA_INSURANCES)
#     if is_geha:
#         mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
#         service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
#         for seg in service_lines:
#             parts = seg["Service Identification"].split(":")
#             code = parts[1] if len(parts) >= 2 else ""
#             modifier = parts[2] if len(parts) >= 3 else ""
#             if not modifier:
#                 errors.append((
#                     f"GEHA: CPT {code} missing provider degree modifier",
#                     "GEHA insurance requires provider degree modifiers on all CPT codes"
#                 ))
#     return errors



# def validate_sentara_provider_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
#     errors = []
#     mapped_all = [map_segment(seg) for seg in (
#         claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]
#     )]
#     payer = ""
#     for seg in mapped_all:
#         if seg.get("Entity Identifier Code") == "PR":
#             payer = seg.get("Name Last or Organization Name", "").upper()
#             break
#         if "Payer Name" in seg:
#             payer = seg.get("Payer Name", "").upper()
#             break
#     is_sentara = any(sentara in payer for sentara in SENTARA_INSURANCES)
#     if is_sentara:
#         mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
#         service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
#         for seg in service_lines:
#             parts = seg["Service Identification"].split(":")
#             code = parts[1] if len(parts) >= 2 else ""
#             modifier = parts[2] if len(parts) >= 3 else ""
#             if not modifier:
#                 errors.append((
#                     f"Sentara: CPT {code} missing provider degree modifier",
#                     "Sentara Health Plan requires provider degree modifiers on all CPT codes"
#                 ))
    # return errors


def validate_hampton_road_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    return (
        validate_bcba_billing_for_non_tricare(claim) +
        validate_taxonomy_and_timing_required(claim) +
        validate_tricare_no_modifiers_except_gt(claim) +
        # validate_tricare_treating_therapist_billing(claim) +
        validate_concurrent_hs_modifier_97153_97156(claim)
        # validate_geha_provider_modifiers(claim) +
        # validate_sentara_provider_modifiers(claim) +
    )