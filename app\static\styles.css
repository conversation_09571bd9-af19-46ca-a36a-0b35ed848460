/* app/static/styles.css */
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    line-height: 1.6;
}

h1, h2 { 
    color: #2c3e50; 
}

form {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

label {
    display: block;
    margin-bottom: 10px;
}

input[type="file"] {
    margin-bottom: 15px;
    display: block;
}

button {
    background-color: #3498db;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    background-color: #2980b9;
}

table { 
    border-collapse: collapse; 
    width: 100%; 
    margin-top: 20px; 
}

th { 
    background-color: #3498db; 
    color: white; 
    text-align: left; 
    padding: 12px; 
}

td { 
    border: 1px solid #ddd; 
    padding: 8px; 
}

tr:nth-child(even) { 
    background-color: #f2f2f2; 
}

tr:hover { 
    background-color: #e9f7fe; 
}

.message { 
    margin: 20px 0; 
    padding: 10px; 
    background-color: #e9f7fe; 
    border-left: 5px solid #3498db; 
}

.high { 
    color: #e74c3c; 
    font-weight: bold; 
}

.medium { 
    color: #f39c12; 
}

ul {
    list-style-type: none;
    padding: 0;
}

ul li {
    margin-bottom: 10px;
}

ul li a {
    display: block;
    padding: 10px 15px;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    text-align: center;
    width: 200px;
}

ul li a:hover {
    background-color: #2980b9;
}