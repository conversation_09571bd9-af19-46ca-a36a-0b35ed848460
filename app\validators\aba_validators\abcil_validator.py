from typing import List, Tu<PERSON>, Dict
from .utils import map_segment, parse_hierarchy
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "47-5369953"
GROUP_NPI = "**********"
PROVIDER_NPI = "**********"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)

def validate_abcil_billing_date(mapped_claim_segments: List[Dict[str, str]]) -> List[Tuple[str, str]]:
    """Rule 1: Billing before 04/30/2021 requires approval."""
    errors = []
    for seg in mapped_claim_segments:
        if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472":  # Service date
            dos = seg["Date Time Period"]
            if seg.get("Date Time Period Format Qualifier") == "D8":
                try:
                    dos_date = datetime.strptime(dos, "%Y%m%d")
                    if dos_date <= datetime(2021, 4, 30):
                        errors.append((
                            f"Date of service {dos} is on or before 04/30/2021",
                            "Request billing approval <NAME_EMAIL>"
                        ))
                except ValueError:
                    errors.append((f"Invalid date format in DTP: {dos}", "Ensure date is in YYYYMMDD format"))
    return errors

def validate_abcil_session_times(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: DBT (97153) sessions in Catalyst; others (e.g., BCBA) may be missing if not in EDI."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
    
    cpt_codes = {seg["Service Identification"].split(":")[1] for seg in service_lines if len(seg["Service Identification"].split(":")) >= 2}
    if "97153" not in cpt_codes:
        errors.append(("Missing DBT sessions (CPT 97153)", "Ensure DBT sessions are included; may need to verify Catalyst data"))
    if not any(code in {"97151", "97155", "97156"} for code in cpt_codes):
        errors.append(("Possible missing BCBA sessions (e.g., 97151, 97155, 97156)", "Request BCBA sessions via billing email if tracked separately"))
    return errors

def validate_abcil_authorization(mapped_all: List[Dict[str, str]]) -> List[Tuple[str, str]]:
    """Rule 5: BCBS of IL requires authorization number for all CPT codes."""
    errors = []
    auth_found = any(seg.get("Reference Identification Qualifier") == "G1" and seg.get("Reference Identification", "").strip() for seg in mapped_all)
    if not auth_found:
        errors.append(("Missing authorization number (REF*G1)", "Add REF*G1 with BCBS of IL authorization number for all CPT codes"))
    return errors

def validate_abcil_97151_units(service_lines: List[Dict[str, str]]) -> List[Tuple[str, str]]:
    """Rule 7: Cap 97151 at 8 units/day if submitted as 12 units."""
    errors = []
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] == "97151":
            try:
                units = float(seg.get("Units/Days", "").strip())
                if units > 8:
                    errors.append((
                        f"CPT 97151 exceeds 8 units/day; found {units}",
                        "Reduce to 8 units/day per Larry's update"
                    ))
            except ValueError:
                errors.append((f"Invalid Units/Days '{seg.get('Units/Days', '')}' for 97151", "Ensure Units/Days is a valid number"))
    return errors

def validate_abcil_magellan_modifiers(service_lines: List[Dict[str, str]], mapped_all: List[Dict[str, str]]) -> List[Tuple[str, str]]:
    """Rule 9: Use HN modifier for Magellan direct services (97153, 97154)."""
    errors = []
    is_magellan = any(seg.get("Application Receiver's Code", "").upper() == "MAGELLAN" for seg in mapped_all if "Application Receiver's Code" in seg)
    if not is_magellan:
        return []

    direct_codes = {"97153", "97154"}
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2 and parts[1] in direct_codes:
            modifier = parts[2] if len(parts) > 2 else ""
            if modifier != "HN":
                errors.append((
                    f"CPT {parts[1]} missing HN modifier for Magellan",
                    "Use HN modifier for direct services per contract"
                ))
    return errors

def validate_abcil_patient_restrictions(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rules 11 & 12: Restrictions for Kenan K. and Noah K. Alyafai."""
    errors = []
    mapped_patient = [map_segment(seg) for seg in claim["patient"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    
    # Extract patient name and DOS
    patient_name = next((seg.get("Name Last or Organization Name", "").upper() + "_" + seg.get("Name First", "").upper() for seg in mapped_patient if "Name Last or Organization Name" in seg), "")
    dos = next((seg["Date Time Period"] for seg in mapped_claim_segments if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472" and seg.get("Date Time Period Format Qualifier") == "D8"), None)
    
    if dos:
        try:
            dos_date = datetime.strptime(dos, "%Y%m%d")
            if "KENAN_K" in patient_name and dos_date < datetime(2024, 12, 1):
                errors.append((
                    f"Billing for Kenan K. on {dos} before 12/01/2024",
                    "Do not bill Kenan K. before 12/01/2024 per new BCBS plan"
                ))
            if "NOAH_K_ALYAFAI" in patient_name and dos_date < datetime(2025, 2, 1):
                errors.append((
                    f"Billing for Noah K. Alyafai on {dos} before 02/01/2025",
                    "Do not bill Noah K. Alyafai before 02/01/2025 per new plan"
                ))
        except ValueError:
            errors.append((f"Invalid date format in DTP: {dos}", "Ensure date is in YYYYMMDD format"))
    
    return errors


def validate_able_kids_rate_per_unit(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validates if the monetary amount per unit is correct based on the units and the CPT code rate,
       and checks for specific modifier conditions."""

    errors = []

    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        
        if len(parts) >= 2:
            cpt = parts[1]
            modifier = parts[0]
            # print(f"Validating CPT: {cpt}")
            # print(f"Modifier: {modifier}")

            # Retrieve other fields
            units = seg.get("Units/Days", "").strip()
            monetary_amount = seg.get("Monetary Amount", "0").strip()

            # print(f"Units: {units}")
            # print(f"Monetary Amount: {monetary_amount}")

            units = float(units)
            monetary_amount = float(monetary_amount) 

            # Check for CPT 97153 with specific modifiers (HM, HN, HO)
            if cpt == "97153" and modifier in ["HM", "HN", "HO"]:
                expected_rate = 19.92
                monetary_amount_per_unit = monetary_amount / units
                # print(f"Monetary Amount per Unit: {monetary_amount_per_unit:.2f}")
                if monetary_amount_per_unit != expected_rate:
                    # print(f"Error: Monetary amount per unit does not match expected rate for CPT {cpt}.")
                    errors.append(( 
                        f"CPT {cpt} with modifier {modifier} has a monetary amount of ${monetary_amount:.2f} for {units} units. Expected ${expected_rate:.2f} per unit, but found ${monetary_amount_per_unit:.2f}.",
                        "Ensure the monetary amount per unit matches the expected rate for CPT 97153 with modifier HM, HN, or HO"
                    ))

            # Check for CPT 97155, 97156, and 97151 (these should not have any modifier)
            elif cpt in ["97155", "97156", "97151"] and modifier:
                # print(f"Error: CPT {cpt} should not have a modifier, but found {modifier}.")
                errors.append((
                    f"CPT {cpt} should not have a modifier, but found {modifier}.",
                    "Ensure that CPT 97155, 97156, and 97151 do not use a modifier"
                ))



    return errors




def validate_abcil_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all ABCIL-specific validation rules."""
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    return (
        validate_abcil_billing_date(mapped_claim_segments) +
        validate_abcil_session_times(claim) +
        validate_abcil_authorization(mapped_all) +
        validate_abcil_97151_units(service_lines) +
        validate_abcil_magellan_modifiers(service_lines, mapped_all) +
        validate_abcil_patient_restrictions(claim) +
        validate_able_kids_rate_per_unit(claim)
    )