from typing import List, Set, Tuple, Dict
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

from app.validators.aba_validators.general_validator import ABA_CPT_MUE
from app.validators.aba_validators.phos_inc_validator import PROVIDER_NPIS

from ..utils import map_segment

# Practice-specific constants
HONEY_BEE_GROUP_NPI = "**********"
OASCHA_NPI = "**********"
HONEY_BEE_NAME = "HONEY BEE THERAPY"
CPT_RATES = {
    "97153": 17.50,
    "97151": 31.25,
    "97155": 31.25,
    "97156": 31.25,
    "97154": 31.25
}
MUE_LIMITS = {
    "97151": 8  # Example: CPT 97151 should not exceed 8 units for CareSource
}
CURRENT_DATE = datetime.now(timezone.utc).astimezone(ZoneInfo("Asia/Kolkata")).replace(tzinfo=None)

def validate_honey_bee_billing_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    Validates that if Honey Bee Therapy is present in box 32 or 33,
    the NPI must be **********.
    """
    errors = []

    mapped_all = [
        map_segment(seg) 
        for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"] 
        ]
    
    relevant_segments = [
        seg for seg in mapped_all 
        if seg.get("Entity Identifier Code") in {"77", "85"}
    ]
    
    for seg in relevant_segments:
        provider_name = seg.get("Name Last or Organization Name", "")
        entity_code = seg.get("Entity Identifier Code")
        npi = seg.get("Identification Code")

        if HONEY_BEE_NAME in provider_name.upper():
            if npi != HONEY_BEE_GROUP_NPI:
                errors.append((
                    f"Box {entity_code} uses NPI {npi} but Honey Bee Therapy requires NPI {HONEY_BEE_GROUP_NPI}",
                    f"Please update NPI to {HONEY_BEE_GROUP_NPI} for Honey Bee Therapy in Box {entity_code}"
                ))

    return errors




def validate_anthem_medicaid_claim(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """
    Special case for Anthem Medicaid:
    - Box 33 (NM1*85 - Billing Provider) must have NPI ********** (Oascha Hightower)
    """
    errors = []
    all_segments = (
        claim.get("billing_provider", []) +
        claim.get("subscriber", []) +
        claim.get("patient", []) +
        claim.get("claim_segments", []) +
        claim.get("insurance", [])
    )

    mapped_all = [map_segment(seg) for seg in all_segments]

    # Determine if the claim is for Anthem Medicaid
    is_anthem_medicaid = any(
        seg.get("Organization Name", "").strip().upper() == "ANTHEM MEDICAID"
        for seg in mapped_all
    )
    if not is_anthem_medicaid:
        return errors  # No validation needed

    billing_provider_npi = next(
        (seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"),
        None
    )
    if billing_provider_npi != OASCHA_NPI:
        errors.append((
            f"Anthem Medicaid claim uses Billing Provider NPI '{billing_provider_npi}' instead of Oascha Hightower NPI '{OASCHA_NPI}'.",
            "Box 33 (NM1*85) must use Oascha Hightower's NPI when billing Anthem Medicaid."
        ))

    return errors


def validate_medicaid_claims(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validate Medicaid Claims (Anthem & CareSource)"""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Check if it's an Anthem Medicaid claim
    is_anthem = any(seg.get("Name Last or Organization Name", "").upper() == "ANTHEM" for seg in mapped_all if seg.get("Entity Identifier Code") == "PR")
    if is_anthem:
        provider_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "33"), None)
        if provider_npi != OASCHA_NPI:
            errors.append((
                f"Anthem Medicaid claim should be billed under Oascha Hightower NPI {OASCHA_NPI}",
                f"Use Oascha Hightower NPI {OASCHA_NPI} for Anthem Medicaid claims"
            ))
    
    # Check for CareSource Medicaid claims and validate provider NPI
    is_caresource = any(seg.get("Name Last or Organization Name", "").upper() == "CARESOURCE" for seg in mapped_all if seg.get("Entity Identifier Code") == "PR")
    if is_caresource:
        # Ensure CareSource claims are billed under the respective tx provider
        provider_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "33"), None)
        if provider_npi not in [OASCHA_NPI, "Other Provider NPI"]:  # Add other NPI logic if necessary
            errors.append((
                f"CareSource Medicaid claim should use correct provider NPI, found {provider_npi}",
                "Ensure CareSource claims are billed under the respective tx providers"
            ))

    return errors


from typing import Dict, List, Tuple

def validate_honey_bee_rate_per_unit(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validates if the monetary amount per unit is correct based on the units and the CPT code rate,
       and checks for specific modifier conditions. Now includes actual LX number in error."""
    errors = []
    claim_segments = claim["claim_segments"]
    current_lx_number = None
    for seg in claim_segments:
        # Detect LX segment and update current_lx_number
        if isinstance(seg, dict):
            # If already mapped, check for LX
            if seg.get("Segment ID") == "LX":
                lx_val = seg.get("Assigned Number")
                if lx_val:
                    current_lx_number = lx_val
        elif isinstance(seg, str):
            # If raw string, check if it starts with LX*
            if seg.startswith("LX*"):
                parts = seg.split("*")
                if len(parts) > 1:
                    current_lx_number = parts[1].split("~")[0].strip()
        # Check for SV1 segment
        # Map segment if not already mapped
        mapped = seg if isinstance(seg, dict) else map_segment(seg)
        if "Service Identification" in mapped:
            parts = mapped["Service Identification"].split(":")
            if len(parts) >= 2:
                cpt = parts[1]
                modifier = parts[0]
                units_str = mapped.get("Units/Days", "").strip()
                monetary_str = mapped.get("Monetary Amount", "").strip()
                try:
                    units = float(units_str)
                    monetary_amount = float(monetary_str)
                except ValueError:
                    continue
                if cpt == "97153":
                    expected_rate = 17.50
                elif cpt in ["97151", "97155", "97156"]:
                    expected_rate = 31.25
                else:
                    continue
                if units == 0:
                    continue
                monetary_amount_per_unit = monetary_amount / units
                if monetary_amount_per_unit != expected_rate:
                    lx_display = f"LX*{current_lx_number}~" if current_lx_number is not None else "(unknown LX)"
                    error_msg = (
                        f"{lx_display}: CPT {cpt} has a monetary amount of ${monetary_amount:.2f} for {units} units. "
                        f"Expected ${expected_rate:.2f} per unit, but found ${monetary_amount_per_unit:.2f}."
                    )
                    errors.append((
                        error_msg,
                        "Ensure the monetary amount per unit matches the expected rate for CPT"
                    ))
    return errors



# def validate_cpt_and_units(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
#     """Validate CPT and Units for proper billing"""
#     errors = []
#     mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
#     service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

#     for seg in service_lines:
#         # Extract CPT code and check for the correct rate
#         cpt_code = seg.get("Service Identification").split(":")[1] if ":" in seg.get("Service Identification", "") else None
#         if cpt_code and cpt_code in CPT_RATES:
#             rate = CPT_RATES[cpt_code]
#             if rate != CPT_RATES.get(cpt_code):
#                 errors.append((
#                     f"CPT code {cpt_code} has incorrect rate",
#                     f"Ensure correct rate of ${rate} for CPT {cpt_code}"
#                 ))

#         # Check for MUE limits on CareSource claims
#         if cpt_code in MUE_LIMITS:
#             units = int(seg.get("Quantity", 0))  # Assuming units are stored in 'Quantity'
#             if units > MUE_LIMITS[cpt_code]:
#                 errors.append((
#                     f"CPT {cpt_code} exceeds MUE limit for CareSource claim",
#                     f"Ensure units for CPT {cpt_code} do not exceed {MUE_LIMITS[cpt_code]} for CareSource"
#                 ))

#     return errors




def validate_hepe_different_pos_not_clubbed(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """He Pe Rule: Do not club sessions with different POS (Place of Service) codes. Each must be billed separately."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    pos_cpt_dos_map = {}
    for idx, seg in enumerate(service_lines):
        cpt = ""
        dos = ""
        pos = seg.get("Place of Service", "")

        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2:
                cpt = parts[1]

        # Find the corresponding DOS (Date of Service)
        for j in range(idx + 1, len(mapped_claim_segments)):
            dtp_seg = mapped_claim_segments[j]
            if dtp_seg.get("Date/Time Qualifier") == "472" and dtp_seg.get("Date Time Period Format Qualifier") == "D8":
                dos = dtp_seg.get("Date Time Period")
            if "Service Identification" in dtp_seg:
                break

        if dos and cpt:
            key = (dos, cpt)
            pos_cpt_dos_map.setdefault(key, set()).add(pos)

    # Validate that no group has multiple POS
    for (dos, cpt), pos_set in pos_cpt_dos_map.items():
        if len(pos_set) > 1:
            error_msg = f"Multiple POS codes {pos_set} found for CPT {cpt} on DOS {dos}"
            correction_msg = "Bill sessions with different POS codes on separate claim lines"
            errors.append((error_msg, correction_msg))

    return errors



def validate_phos_inc_psycho_dx_james_orseno(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 3: Psycho DX Eval sessions (90791, 96130, 96131, 96136, 96137) billed under James Orseno."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    psycho_dx_codes = {"90791", "96130", "96131", "96136", "96137"}
    
    for seg in service_lines:
        # Try both Service Identification and Procedure Code fields
        code = None
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            if len(parts) >= 2:
                code = parts[1]
        if not code and "Procedure Code" in seg:
            code = seg["Procedure Code"]

        if code in psycho_dx_codes:
            rendering_npi = seg.get("Identification Code")
            if rendering_npi != PROVIDER_NPIS["James Orseno"]:
                errors.append((
                    f"Psycho DX CPT {code} billed with Rendering NPI {rendering_npi}",
                    f"Bill under James Orseno (NPI: {PROVIDER_NPIS['James Orseno']}) for Psycho DX Eval sessions"
                ))

    return errors

def validate_honey_bee_mue_limits(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validates that units in SV1 segments do not exceed MUE limits for CPT codes unique to Honey Bee (not in ABA_CPT_MUE). Raises error if CPT code is missing from both MUE dictionaries."""
    HONEY_BEE_ONLY_MUE = {"0362T": 8, "0373T": 32}  # Only codes not in ABA_CPT_MUE
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt_code = parts[1]
            if cpt_code in ABA_CPT_MUE:
                continue  # Skip codes already checked by the general rule
            elif cpt_code in HONEY_BEE_ONLY_MUE:
                try:
                    units = float(seg.get("Units/Days", "").strip())
                    if units > HONEY_BEE_ONLY_MUE[cpt_code]:
                        errors.append((
                            f"CPT {cpt_code} exceeds MUE limit of {HONEY_BEE_ONLY_MUE[cpt_code]} units/day; found {units}",
                            f"Reduce units to {HONEY_BEE_ONLY_MUE[cpt_code]} or less for CPT {cpt_code}"
                        ))
                except ValueError:
                    errors.append((f"Invalid Units/Days '{seg.get('Units/Days', '')}' for '{seg['Service Identification']}'", "Ensure Units/Days is a valid number"))
            else:
                errors.append((f"Missing MUE limit for CPT {cpt_code}", f"Add MUE limit for CPT {cpt_code} to the Honey Bee MUE dictionary"))
    return errors

def validate_honey_bee_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Honey Bee Therapy-specific validation rules"""
    return (
        validate_honey_bee_billing_npi(claim) +
        validate_anthem_medicaid_claim(claim) +
        validate_medicaid_claims(claim) +
        validate_honey_bee_rate_per_unit(claim) +
        validate_honey_bee_mue_limits(claim)
        # validate_phos_inc_psycho_dx_james_orseno(claim)
    )