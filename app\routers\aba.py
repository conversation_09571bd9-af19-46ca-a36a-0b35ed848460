# app/routers/aba.py
from fastapi import APIRouter, UploadFile, File, HTTPException, Request, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jin<PERSON>2Templates
from app.validators.aba_validators.main import (
    validate_837_content,
    get_daily_count,
    send_daily_report,
    get_daily_stats,
    increment_daily_count,
    save_validation_stats
)
from datetime import date
import collections
from app.utils import verify_aba_token

# Initialize templates
templates = Jinja2Templates(directory="app/templates")

router = APIRouter(prefix="/aba", tags=["ABA"])

@router.get("/", response_class=HTMLResponse)
async def aba_home(request: Request):
    return templates.TemplateResponse("aba_home.html", {"request": request})

@router.post("/validate")
async def validate_aba_file(request: Request, file: UploadFile = File(...)):
    try:
        content_bytes = await file.read()
        content_str = content_bytes.decode('utf-8').strip()
        if not content_str:
            raise HTTPException(status_code=400, detail="Uploaded file is empty")
        
        # 1) run the validator
        result = await validate_837_content(content_str)

        # 2) increment daily counter & save this file's stats
        increment_daily_count()
        error_breakdown = collections.Counter(
            err["error"].split(":")[0].strip()
            for err in result.get("errors", [])
        )
        save_validation_stats(
            filename=file.filename,
            total_errors=len(result.get("errors", [])),
            error_breakdown=error_breakdown,
            practice_name=result.get("practice_name", "Unknown"),
        )
        # print("practice name :", result.get("practice_name", "Unknown"))

        # 3) if client wants JSON, send that
        accept = request.headers.get("accept", "")
        if "application/json" in accept.lower():
            return JSONResponse(result)

        # 4) otherwise render the same HTML template
        return templates.TemplateResponse(
            "aba_results.html", 
            {
                "request": request,
                "message": result.get("message", ""),
                "errors": result.get("errors", [])
            }
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/daily-count", response_class=JSONResponse)
async def aba_daily_count():
    return {"count": get_daily_count()}

@router.get("/daily-stats", response_class=JSONResponse)
async def aba_daily_stats():
    return get_daily_stats(date.today().isoformat())

@router.post("/trigger-daily-report", response_class=JSONResponse, dependencies=[Depends(verify_aba_token)])
async def aba_trigger_daily_report():
    send_daily_report()
    return {"status": "Report triggered successfully"}