# app/validators/aba_validators/onboarding/embracing_autism_validator.py
from typing import List, <PERSON><PERSON>, Dict

from ..utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "*********"
GROUP_NPI = "**********"
IST = ZoneInfo("Asia/Kolkata")

def _split_sv1(seg: Dict[str, str]) -> Tu<PERSON>[str, str]:
    """Helper to pull CPT code and modifier out of SV1."""
    parts = seg["Service Identification"].split(":")
    code     = parts[1] if len(parts)>=2 else ""
    modifier = parts[2] if len(parts)>=3 else ""
    return code, modifier

def validate_unit_limits(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validate unit limits for different CPT codes and payers."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    
    # Get payer info
    payer = next(
        (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
        next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg), "")
    )
    
    svc = [s for s in mapped if isinstance(s, dict) and "Service Identification" in s]
    
    cpt_units = {}
    
    for seg in svc:
        code, _ = _split_sv1(seg)
        try:
            units_str = seg.get("Units/Days", "0")
            if units_str:
                units = float(units_str)
                cpt_units[code] = cpt_units.get(code, 0) + units
        except (ValueError, TypeError):
            continue
    
    # Check 97153 32-unit limit (general rule)
    # if cpt_units.get("97153", 0) > 32:
    #     errors.append((
    #         f"CPT 97153 exceeds 32 units per day ({cpt_units['97153']} units)",
    #         "Reduce units to 32 or less"
    #     ))
    
    # Check Kaiser-specific 97151 16-unit limit
    if "KAISER" in payer and cpt_units.get("97151", 0) > 16:
        errors.append((
            f"Kaiser: CPT 97151 exceeds 16 units per day ({cpt_units['97151']} units)",
            "Reduce units to 16 or less for Kaiser claims"
        ))
    
    # CareOregon allows up to 32 units of 97151
    if "CAREOREGON" in payer and cpt_units.get("97151", 0) > 32:
        errors.append((
            f"CareOregon: CPT 97151 exceeds 32 units per day ({cpt_units['97151']} units)",
            "Reduce units to 32 or less for CareOregon claims"
        ))
    
    return errors

def validate_kaiser_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validate Kaiser-specific rules."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc = [s for s in mapped if "Service Identification" in s]
    
    payer = next(
        (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
        next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg), "")
    )
    
    if "KAISER" not in payer:
        return errors
        
    pos = next(
        (seg.get("Place of Service Code", "") for seg in mapped if "Place of Service Code" in seg),
        ""
    )
    
    # Check telehealth rules (POS 02 without modifiers)
    if pos == "02":
        for seg in svc:
            code, mod = _split_sv1(seg)
            if mod in ["95", "GT"]:
                errors.append((
                    f"Kaiser telehealth: CPT {code} should not have 95/GT modifier with POS 02",
                    "Remove 95/GT modifier for Kaiser telehealth claims"
                ))
    
    # Check concurrent billing rules
    concurrent_codes = {}
    for seg in svc:
        code, mods = _split_sv1(seg)
        service_time = seg.get("Service Time", "")
        if service_time:
            concurrent_codes[service_time] = concurrent_codes.get(service_time, []) + [(code, mods)]
    
    for time, codes in concurrent_codes.items():
        if len(codes) > 1:
            codes_dict = dict(codes)
            # 97155 concurrent with 97153
            if "97155" in codes_dict and "97153" in codes_dict:
                if "HO" not in codes_dict["97155"] or "HM" not in codes_dict["97155"]:
                    errors.append((
                        "Kaiser concurrent billing: 97155 with 97153 requires both HO and HM modifiers",
                        "Add both HO and HM modifiers to 97155 when concurrent with 97153"
                    ))
            # 97155 concurrent with 97154
            if "97155" in codes_dict and "97154" in codes_dict:
                if "HO" not in codes_dict["97155"] or "59" not in codes_dict["97155"]:
                    errors.append((
                        "Kaiser concurrent billing: 97155 with 97154 requires both HO and 59 modifiers",
                        "Add both HO and 59 modifiers to 97155 when concurrent with 97154"
                    ))
    
    return errors

def validate_tricare_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validate Tricare-specific rules."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc = [s for s in mapped if "Service Identification" in s]
    
    payer = next(
        (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
        next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg), "")
    )
    
    if "TRICARE" not in payer:
        return errors
    
    # Check for invalid modifiers (only GT allowed)
    for seg in svc:
        code, mods = _split_sv1(seg)
        if mods and mods != "GT":
            if not (code == "97156" and mods == "HS" and _is_concurrent_with_97153(seg, svc)):
                errors.append((
                    f"Tricare: Invalid modifier {mods} for CPT {code}",
                    "Remove modifier (only GT modifier allowed for Tricare, except HS for concurrent 97156)"
                ))
    
    # Check concurrent 97153 and 97156
    concurrent_codes = {}
    for seg in svc:
        code, mods = _split_sv1(seg)
        service_time = seg.get("Service Time", "")
        if service_time:
            concurrent_codes[service_time] = concurrent_codes.get(service_time, []) + [(code, mods)]
    
    for time, codes in concurrent_codes.items():
        if len(codes) > 1:
            codes_dict = dict(codes)
            if "97153" in codes_dict and "97156" in codes_dict:
                if "HS" not in codes_dict["97156"]:
                    errors.append((
                        "Tricare concurrent billing: 97156 requires HS modifier when concurrent with 97153",
                        "Add HS modifier to 97156 when concurrent with 97153"
                    ))
    
    return errors

def _is_concurrent_with_97153(seg: Dict[str, str], all_segs: List[Dict[str, str]]) -> bool:
    """Helper to check if a service is concurrent with 97153."""
    service_time = seg.get("Service Time", "")
    if not service_time:
        return False
    
    for other_seg in all_segs:
        if other_seg == seg:
            continue
        code, _ = _split_sv1(other_seg)
        if code == "97153" and other_seg.get("Service Time", "") == service_time:
            return True
    return False

def validate_careoregon_telehealth_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """CareOregon: POS 10/02 use 95 modifiers, all other insurance use GT modifiers."""
    errors = []
    mapped = [map_segment(s) for s in claim["claim_segments"]]
    svc = [s for s in mapped if "Service Identification" in s]
    
    payer = next(
        (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
        next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg), "")
    )
    
    pos = next(
        (seg.get("Place of Service Code", "") for seg in mapped if "Place of Service Code" in seg),
        ""
    )
    
    if pos in ("10", "02"):  # Telehealth POS codes
        for seg in svc:
            code, mod = _split_sv1(seg)
            if "CAREOREGON" in payer:
                if mod != "95":
                    errors.append((
                        f"CareOregon: CPT {code} POS {pos} requires 95 modifier",
                        "Use 95 modifier for CareOregon telehealth sessions"
                    ))
            elif "KAISER" not in payer:  # Skip modifier check for Kaiser
                if mod != "GT":
                    errors.append((
                        f"CPT {code} POS {pos} requires GT modifier for non-CareOregon/Kaiser",
                        "Use GT modifier for telehealth sessions (except CareOregon and Kaiser)"
                    ))
    
    return errors

def validate_pos_codes(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validate if service lines 97151/97155/97156 have at least one HO modifier in the claim, and 97153 uses HM modifier."""
    errors = []
    mapped = [map_segment(s) for s in claim.get("claim_segments", [])]
    svc_segments = [s for s in mapped if "Service Identification" in s]
    
    payer = next(
        (seg["Payer Name"].upper() for seg in mapped if "Payer Name" in seg),
        next((seg["Application Receiver's Code"].upper() for seg in mapped if "Application Receiver's Code" in seg), "")
    )
    
    # Skip modifier validation for Tricare
    if "TRICARE" in payer:
        return errors

    ho_required = []
    ho_found = False

    for seg in svc_segments:
        service_id = seg.get("Service Identification", "")
        if not service_id:
            continue

        parts = service_id.split(":")
        if len(parts) < 2:
            continue

        cpt = parts[1]
        modifiers = list(filter(None, [
            seg.get("Procedure Modifier", ""),
            seg.get("Procedure Modifier2", ""),
            *[p for p in parts[2:] if len(p) == 2]
        ]))

        if cpt in ("97151", "97155", "97156"):
            ho_required.append(cpt)
            if "HO" in modifiers:
                ho_found = True
        elif cpt == "97153" and "HM" not in modifiers:
            errors.append((
                f"CPT code {cpt} requires HM modifier",
                f"Add HM modifier to CPT code {cpt} service line"
            ))

    if ho_required and not ho_found:
        unique = list(set(ho_required))
        if len(unique) == 1:
            errors.append((
                f"CPT code {unique[0]} requires HO modifier",
                f"Add HO modifier to CPT code {unique[0]} service line"
            ))
        else:
            joined = ", ".join(unique)
            errors.append((
                f"At least one of CPT codes {joined} must have HO modifier",
                f"Add HO modifier to at least one of the following CPT codes: {joined}"
            ))

    return errors
    
def validate_embracing_autism_focus_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combine all Embracing Autism LLC rules that can be EDI-checked."""
    return (  
        validate_unit_limits(claim)
        + validate_careoregon_telehealth_modifiers(claim)
        + validate_pos_codes(claim)
        + validate_kaiser_rules(claim)
        + validate_tricare_rules(claim)
    )


