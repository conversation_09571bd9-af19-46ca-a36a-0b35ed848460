from typing import List, Tuple, Dict
from .utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants
TAX_ID = "86-3257624"
GROUP_NPI = "**********"
PROVIDER_NPIS = {
    "<PERSON>": "**********",
    "<PERSON>": "**********",
    "<PERSON>": "**********",
    "<PERSON><PERSON><PERSON>": "**********",
    "<PERSON>": "**********"
}
ABA_CPT_CODES = {"97151", "97152", "97153", "97154", "97155", "97156", "97157", "97158", "H2012", "H2014", "H2019"}
SPEECH_CPT_CODES = {"92507"}
PSYCHO_CPT_CODES = {"90791", "96130", "96131", "96136", "96137", "90837"}
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)
BCBSIL_MODIFIER_START_DATE = datetime(2024, 1, 1)  # Effective 2024 per BCBS IL update

def validate_phos_inc_bcbsil_hn_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: Require HN modifier for specific providers for BCBS Illinois insurance. For Donia Alkaraki, only until 05/27/2024."""
    errors = []
    # List of provider names (upper case for comparison)
    hn_providers = {
        "AYYAHT ALI",
        "BISAN ABUZATAR",
        "JAMIE WILKES",
        "GIA RUTKOWSKI",
        "SAREEN HAYEK",
        "NADIA QUAD",
        "JOURDAN RZACA",
        "ANDREW MORAVEC",
        "DONIA ALKARAKI"  # Special date logic
    }
    # Date cutoff for Donia Alkaraki
    cutoff_date = datetime(2024, 5, 27)

    # Map all segments for provider and payer info
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    # Check if insurance is BCBS Illinois
    is_bcbsil = any(
        "BCBS IL" in seg.get("Payer Name", "").upper() or "BCBS" in seg.get("Application Receiver's Code", "").upper()
        for seg in mapped_all
    )
    if not is_bcbsil:
        return []

    # Find DOS (Date of Service) for each service line
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = None
    if dos:
        try:
            dos_date = datetime.strptime(dos, "%Y%m%d")
        except Exception:
            dos_date = None

    for seg in service_lines:
        # Get rendering provider name (NM1*82)
        provider_name = None
        if seg.get("Entity Identifier Code") == "82":
            first = seg.get("Name First", "").strip().upper()
            last = seg.get("Name Last or Organization Name", "").strip().upper()
            if first and last:
                provider_name = f"{first} {last}"
            elif last:
                provider_name = last
        # If not in this segment, try to find in mapped_all
        if not provider_name:
            nm1_82 = next((s for s in mapped_all if s.get("Entity Identifier Code") == "82"), None)
            if nm1_82:
                first = nm1_82.get("Name First", "").strip().upper()
                last = nm1_82.get("Name Last or Organization Name", "").strip().upper()
                if first and last:
                    provider_name = f"{first} {last}"
                elif last:
                    provider_name = last
        if not provider_name:
            continue
        # Only check for listed providers
        if provider_name not in hn_providers:
            continue
        # Special logic for Donia Alkaraki
        if provider_name == "DONIA ALKARAKI":
            if not dos_date or dos_date > cutoff_date:
                continue  # Only require HN if DOS is on or before cutoff
        # Check for HN modifier in Service Identification (parts[2:6])
        parts = seg["Service Identification"].split(":")
        modifiers = set(parts[2:6])
        if "HN" not in modifiers:
            errors.append((
                f"BCBS IL claim for {provider_name} missing HN modifier (CPT {parts[1]})",
                "Add HN modifier for this provider for BCBS Illinois claims"
            ))
    return errors


def validate_phos_inc_bcbsil_hn_modifiers(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: Require HN modifier for specific providers for BCBS Illinois insurance. For Donia Alkaraki, only until 05/27/2024."""
    errors = []
    # List of provider names (upper case for comparison)
    ho_providers = {
        "VERONICA MARTINEZ",
        "THERESA KLIMOWSKI",
        "ANGELA EVANGELOPOULOS",
        "DONIA ALKARAKI",  # Special date logic
        "VICTORIA CHRISTENSEN"
    }
    # Date cutoff for Donia Alkaraki
    cutoff_date = datetime(2024, 5, 28)

    # Map all segments for provider and payer info
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]

    # Check if insurance is BCBS Illinois
    is_bcbsil = any(
        "BCBS ILLINOIS" in seg.get("Payer Name", "").upper() or "BCBS" in seg.get("Application Receiver's Code", "").upper()
        for seg in mapped_all
    )
    if not is_bcbsil:
        return []

    # Find DOS (Date of Service) for each service line
    dos = next((seg.get("Date Time Period") for seg in mapped_all if "Date Time Period" in seg and seg.get("Date/Time Qualifier") == "472"), None)
    dos_date = None
    if dos:
        try:
            dos_date = datetime.strptime(dos, "%Y%m%d")
        except Exception:
            dos_date = None

    for seg in service_lines:
        # Get rendering provider name (NM1*82)
        provider_name = None
        if seg.get("Entity Identifier Code") == "82":
            first = seg.get("Name First", "").strip().upper()
            last = seg.get("Name Last or Organization Name", "").strip().upper()
            if first and last:
                provider_name = f"{first} {last}"
            elif last:
                provider_name = last
        # If not in this segment, try to find in mapped_all
        if not provider_name:
            nm1_82 = next((s for s in mapped_all if s.get("Entity Identifier Code") == "82"), None)
            if nm1_82:
                first = nm1_82.get("Name First", "").strip().upper()
                last = nm1_82.get("Name Last or Organization Name", "").strip().upper()
                if first and last:
                    provider_name = f"{first} {last}"
                elif last:
                    provider_name = last
        if not provider_name:
            continue
        # Only check for listed providers
        if provider_name not in ho_providers:
            continue
        # Special logic for Donia Alkaraki
        if provider_name == "DONIA ALKARAKI":
            if not dos_date or dos_date > cutoff_date:
                continue  # Only require HN if DOS is on or before cutoff
        # Check for HN modifier in Service Identification (parts[2:6])
        parts = seg["Service Identification"].split(":")
        modifiers = set(parts[2:6])
        if "HO" not in modifiers:
            errors.append((
                f"BCBS IL claim for {provider_name} missing HN modifier (CPT {parts[1]})",
                "Add HN modifier for this provider for BCBS Illinois claims"
            ))
    return errors

def validate_phos_inc_telehealth_pos_10(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 2: If POS is 02, require change to POS 10 and add 95 modifier."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
    
    for seg in service_lines:
        pos = seg.get("Place of Service", "").strip()
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            if pos == "02":
                modifiers = set(parts[2:6])
                if "95" not in modifiers:
                    errors.append((
                        f"Telehealth CPT {parts[1]} billed with POS 02 and missing 95 modifier",
                        "Change POS to 10 and add 95 modifier for telehealth sessions per Phos Inc rules"
                    ))
                else:
                    errors.append((
                        f"Telehealth CPT {parts[1]} billed with POS 02 (has 95 modifier)",
                        "Change POS to 10 for telehealth sessions per Phos Inc rules"
                    ))
    return errors

def validate_phos_inc_psycho_dx_james_orseno(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Psycho DX Eval sessions must be billed under James Orseno (NPI: **********)."""
    errors = []
    mapped_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    psycho_dx_codes = {"90791", "96130", "96131", "96136", "96137", "90837"}
    expected_npi = PROVIDER_NPIS["James Orseno"]
    current_rendering_npi = None
    for seg in mapped_segments:
        if seg.get("Entity Identifier Code") == "82" and "Identification Code" in seg:
            current_rendering_npi = seg["Identification Code"]
        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            cpt_code = parts[1] if len(parts) >= 2 else "UNKNOWN"
            if cpt_code in psycho_dx_codes:
                if current_rendering_npi != expected_npi:
                    errors.append((
                        f"Psycho DX CPT {cpt_code} billed with Rendering NPI {current_rendering_npi}",
                        f"Bill under James Orseno (NPI: {expected_npi}) for Psycho DX Eval sessions"
                    ))
    return errors

def validate_phos_inc_st_andriana_evangelopoulos(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """ST (Speech) sessions must be billed under Andriana Mae Evangelopoulos (NPI: **********)."""
    errors = []
    mapped_segments = [map_segment(seg) for seg in claim["claim_segments"]]

    # Use the same set of speech CPT codes as theralympics
    speech_cpt_codes = {"92507", "92609", "92526", "92521", "92522", "92523", "92524", "92610"}
    expected_npi_st = PROVIDER_NPIS["Andriana Mae Evangelopoulos"]

    current_rendering_npi = None

    for seg in mapped_segments:
        if seg.get("Entity Identifier Code") == "82" and "Identification Code" in seg:
            current_rendering_npi = seg["Identification Code"]

        if "Service Identification" in seg:
            parts = seg["Service Identification"].split(":")
            cpt_code = parts[1] if len(parts) >= 2 else "UNKNOWN"

            if cpt_code in speech_cpt_codes:
                if current_rendering_npi != expected_npi_st:
                    errors.append((
                        f"ST CPT {cpt_code} billed with Rendering NPI {current_rendering_npi}",
                        f"Bill under Andriana Mae Evangelopoulos (NPI: {expected_npi_st}) for ST (Speech) sessions"
                    ))
    return errors

def validate_phos_inc_aba_codes(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Checks that only valid ABA CPT/HCPCS codes are used for ABA services."""
    errors = []
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) >= 2:
            cpt = parts[1]
            # If not ABA, Speech, or Psycho, flag as invalid
            if cpt not in ABA_CPT_CODES and cpt not in SPEECH_CPT_CODES and cpt not in PSYCHO_CPT_CODES:
                errors.append((
                    f"Invalid code '{cpt}'",
                    "Use Category I CPT codes 97151-97158, valid HCPCS codes (H2012, H2019, etc.), Speech (92507), or Psycho codes for ABA services"
                ))
    return errors

def validate_phos_inc_patients_victoria_christensen(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: For specific patients, claims must be under Victoria Christensen (NPI: **********)."""
    errors = []
    # List of patients (LASTNAME, FIRSTNAME) in uppercase
    patient_list = {
        ("ZEGAR", "REEMAS"),
        ("AIDONIS", "POLLYKSENI"),
        ("CASTILLO", "CALEB"),
        ("HAYEK", "SADEEL"),
        ("KRAMPAC", "NATALIE"),
        ("KARABETSOS", "ATHANASIOS"),
    }
    expected_npi = "**********"

    # Map all segments
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]

    # Find patient name (NM1*IL)
    patient_nm1 = next((seg for seg in mapped_all if seg.get("Entity Identifier Code") == "IL"), None)
    if not patient_nm1:
        return []  # No patient info found
    last = patient_nm1.get("Name Last or Organization Name", "").strip().upper()
    first = patient_nm1.get("Name First", "").strip().upper()
    if (last, first) not in patient_list:   
        return []  # Not a special patient

    # Find rendering provider NPI (NM1*82)
    rendering_npi = None
    for seg in mapped_all:
        if seg.get("Entity Identifier Code") == "82":
            rendering_npi = seg.get("Identification Code", "").strip()
            break
    if rendering_npi != expected_npi:
        errors.append((
            f"Claim for patient {first} {last} not billed under Victoria Christensen (NPI {expected_npi}) but billed under NPI {rendering_npi}",
            f"change rendering provider to Victoria Christensen (NPI {expected_npi})"
        ))
    return errors

def validate_phos_inc_cpt_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule: Check that correct rates are used for specific CPT/modifier combinations for PHOS claims."""
    errors = []
    # Required rates
    required_rates = {
        ("97153", "HN"): 19.92,
        ("97153", "HO"): 23.91,
        ("97155", None): 24.95,
        ("97156", None): 23.79,
        ("97151", None): 10.73,
    }
    # Map claim segments
    mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
    service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
    for seg in service_lines:
        parts = seg["Service Identification"].split(":")
        if len(parts) < 2:
            continue
        cpt = parts[1]
        # Get modifier (if any)
        modifier = parts[2] if len(parts) > 2 else None
        # Get rate/amount
        amount = float(seg.get("Monetary Amount", "").strip())
        units = float(seg.get("Units/Days", "").strip())
        per_unit_rate = amount / units

        # Check for 97153+HN or 97153+HO
        if cpt == "97153" and modifier in ("HN", "HO"):
            key = (cpt, modifier)
            expected_rate = required_rates.get(key)
            if expected_rate and round(per_unit_rate, 2) != expected_rate:
                errors.append((
                    f"CPT {cpt} with modifier {modifier} has rate ${amount:.2f}, expected ${required_rates[key]:.2f}",
                    f"Use rate ${required_rates[key]:.2f} for CPT {cpt} with {modifier} modifier"
                ))
        elif cpt in ("97155", "97156", "97151"):
            key = (cpt, None)
            expected_rate = required_rates.get(key)
            if expected_rate and round(per_unit_rate, 2) != expected_rate:
                errors.append((
                    f"CPT {cpt} has rate ${per_unit_rate:.2f}, expected ${expected_rate:.2f}",
                    f"Use rate ${expected_rate:.2f} for CPT {cpt}"
                ))
    
    return errors

def validate_phos_inc_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Phos Inc-specific validation rules."""
    return (
        validate_phos_inc_bcbsil_hn_modifiers(claim) +
        validate_phos_inc_telehealth_pos_10(claim) +
        validate_phos_inc_psycho_dx_james_orseno(claim)+
        validate_phos_inc_st_andriana_evangelopoulos(claim) +
        validate_phos_inc_aba_codes(claim) +
        validate_phos_inc_patients_victoria_christensen(claim) +
        validate_phos_inc_cpt_rates(claim)
    )