<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Healthcare Validator</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Lottie Player -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary: #4361ee;
            --primary-dark: #3a0ca3;
            --secondary: #4cc9f0;
            --accent: #f72585;
            --dark: #14213d;
            --light: #f8f9fa;
            --card-bg: #ffffff;
            --feature-card: #f9fafb;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --error-bg: #f8d7da;
            --error-text: #721c24;
            --success-bg: #d1e7dd;
            --success-text: #0f5132;
            --success: #4CAF50;
            --danger: #f44336;
            --warning: #ff9800;
            --info: #2196F3;
        }
        
        [data-theme="dark"] {
            --primary: #4895ef;
            --primary-dark: #4361ee;
            --secondary: #4cc9f0;
            --accent: #f72585;
            --dark: #f8f9fa;
            --light: #121212;
            --card-bg: #1e1e1e;
            --feature-card: #2d2d2d;
            --text-primary: #f8f9fa;
            --text-secondary: #adb5bd;
            --error-bg: #422b2d;
            --error-text: #f1aeb5;
            --success-bg: #1a3a2e;
            --success-text: #75b798;
            --success: #81c784;
            --danger: #e57373;
            --warning: #ffb74d;
            --info: #64b5f6;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--light);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }
        
        .text-primary-custom {
            color: var(--text-primary) !important;
        }
        
        .text-secondary-custom {
            color: var(--text-secondary) !important;
        }
        
        .card-3d {
            transform-style: preserve-3d;
            perspective: 1000px;
            transition: transform 0.5s;
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        .card-3d:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .upload-area {
            border: 2px dashed var(--primary);
            transition: all 0.3s;
            border-radius: 12px;
            background-color: rgba(67, 97, 238, 0.03);
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: var(--accent);
            background-color: rgba(247, 37, 133, 0.05);
        }
        
        .theme-toggle {
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(67, 97, 238, 0.1);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .theme-toggle:hover {
            transform: scale(1.1);
            background: rgba(67, 97, 238, 0.2);
        }
        
        .btn-primary-custom {
            background-color: var(--primary);
            border-color: var(--primary);
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary-custom:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            color: white;
        }
        
        .healthcare-icon {
            color: var(--secondary);
        }

        /* Custom error message style */
        .error-message {
            background-color: var(--error-bg);
            color: var(--error-text);
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
            display: none;
            border-left: 4px solid var(--error-text);
        }
        
        .header-title {
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: 700;
        }
        
        .feature-card {
            border-radius: 12px;
            transition: all 0.3s;
            background-color: var(--feature-card);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        [data-theme="dark"] .feature-card {
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .back-btn {
            border-radius: 50px;
            padding: 8px 16px;
            transition: all 0.3s;
        }
        
        .back-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        [data-theme="dark"] .back-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        footer {
            background-color: rgba(0, 0, 0, 0.02);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            color: var(--text-secondary);
        }
        
        [data-theme="dark"] footer {
            background-color: rgba(255, 255, 255, 0.02);
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        
        /* Module cards */
        .module-card {
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        .module-card:hover {
            transform: translateY(-5px);
        }
        
        /* Icon containers */
        .icon-container {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }
        
        .blue-icon-container {
            background-color: rgba(67, 97, 238, 0.1);
        }
        
        .green-icon-container {
            background-color: rgba(76, 201, 240, 0.1);
        }
        
        .purple-icon-container {
            background-color: rgba(247, 37, 133, 0.1);
        }
        
        [data-theme="dark"] {
            --light: #2d3748;
            --card-bg: #4a5568;
            --feature-card: #718096;
        }
                
        [data-theme="dark"] {
            --light: #3a3636;
            --card-bg: #504a4a;
            --feature-card: #6b6565;
        }
                
        [data-theme="dark"] {
            --light: #3d3d3d;
            --card-bg: #525252;
            --feature-card: #6b6b6b;
        }
                
        [data-theme="dark"] {
            --light: #1a202c;
            --card-bg: #2d3748;
            --feature-card: #4a5568;
        }

        /* Animation container */
        .animation-container {
            margin: -20px 0;
        }
        
        /* Header text */
        .header-text {
            letter-spacing: -0.025em;
        }
        
        /* Subheader text */
        .subheader-text {
            opacity: 0.8;
        }
        
        /* File name display */
        .file-name {
            font-size: 0.9rem;
            padding: 8px 12px;
            background-color: rgba(76, 201, 240, 0.1);
            border-radius: 6px;
            display: inline-block;
            margin-top: 10px;
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header with Theme Toggle -->
        <header class="flex justify-between items-center mb-12">
            <div class="flex items-center space-x-3">
                <i class="fas fa-heartbeat healthcare-icon text-3xl"></i>
                <h1 class="text-3xl font-bold header-title header-text">Unified Healthcare Validator</h1>
            </div>
            <button id="themeToggle" type="button" class="theme-toggle">
                <i class="fas fa-moon" id="themeIcon"></i>
            </button>
        </header>

        <!-- Hero Section -->
        <section class="mb-16 text-center">
            <div class="animation-container">
                <lottie-player 
                    src="https://assets5.lottiefiles.com/packages/lf20_5tkzkblw.json" 
                    background="transparent" 
                    speed="1" 
                    style="width: 200px; height: 200px; margin: 0 auto;" 
                    loop autoplay>
                </lottie-player>
            </div>
            <h2 class="text-3xl md:text-4xl font-bold mb-4 text-primary-custom">Healthcare Data Validation Suite</h2>
            <p class="text-lg text-secondary-custom max-w-2xl mx-auto subheader-text">
                Secure, compliant processing for healthcare data validation. Choose your validation module below.
            </p>
        </section>

        <!-- Processing Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <!-- ABA Validator Card -->
            <div class="card-3d module-card">
                <div class="p-6 h-full flex flex-col">
                    <div class="flex items-center mb-4">
                        <div class="icon-container blue-icon-container">
                            <i class="fas fa-file-invoice-dollar healthcare-icon text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-primary-custom">ABA Validator</h3>
                    </div>
                    <p class="text-secondary-custom mb-6 flex-grow">
                        Validate ABA routing numbers with our secure processing module. Ensures compliance with healthcare financial standards.
                    </p>
                    <a href="/aba/" class="block">
                        <button class="w-full py-3 px-6 btn-primary-custom font-medium transition flex items-center justify-center space-x-2">
                            <span>Select Module</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </a>
                </div>
            </div>

            <!-- DAQ Processor Card -->
            <div class="card-3d module-card">
                <div class="p-6 h-full flex flex-col">
                    <div class="flex items-center mb-4">
                        <div class="icon-container green-icon-container">
                            <i class="fas fa-microchip healthcare-icon text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-primary-custom">DAQ Processor</h3>
                    </div>
                    <p class="text-secondary-custom mb-6 flex-grow">
                        Process and analyze Data Acquisition files with our healthcare-optimized processor. HIPAA-compliant data handling.
                    </p>
                    <a href="/daq/" class="block">
                        <button class="w-full py-3 px-6 btn-primary-custom font-medium transition flex items-center justify-center space-x-2">
                            <span>Select Module</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </a>
                </div>
            </div>

            <!-- Theralympics Validator Card -->
            <div class="card-3d module-card">
                <div class="p-6 h-full flex flex-col">
                    <div class="flex items-center mb-4">
                        <div class="icon-container purple-icon-container">
                            <i class="fas fa-dumbbell healthcare-icon text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-primary-custom">Theralympics Validator</h3>
                    </div>
                    <p class="text-secondary-custom mb-6 flex-grow">
                        Validate Theralympics data with our specialized module. Ensures compliance with healthcare standards.
                    </p>
                    <a href="/theralympics/" class="block">
                        <button class="w-full py-3 px-6 btn-primary-custom font-medium transition flex items-center justify-center space-x-2">
                            <span>Select Module</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-16 py-6 text-center">
            <p class="text-secondary-custom">© 2025 Unified Healthcare Validator. All rights reserved.</p>
            <p class="mt-1 text-secondary-custom text-sm">HIPAA compliant processing for healthcare organizations</p>
        </footer>
    </div>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.getElementById('themeIcon');
        const html = document.documentElement;
        
        // Check for saved theme preference or use preferred color scheme
        const currentTheme = localStorage.getItem('theme') || 
                            (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
        
        if (currentTheme === 'dark') {
            html.setAttribute('data-theme', 'dark');
            themeIcon.classList.replace('fa-moon', 'fa-sun');
        }

        themeToggle.addEventListener('click', () => {
            if (html.getAttribute('data-theme') === 'light') {
                html.setAttribute('data-theme', 'dark');
                themeIcon.classList.replace('fa-moon', 'fa-sun');
                localStorage.setItem('theme', 'dark');
            } else {
                html.setAttribute('data-theme', 'light');
                themeIcon.classList.replace('fa-sun', 'fa-moon');
                localStorage.setItem('theme', 'light');
            }
        });

        // Listen for theme changes from other tabs/windows
        window.addEventListener('storage', (e) => {
            if (e.key === 'theme') {
                const newTheme = e.newValue;
                html.setAttribute('data-theme', newTheme);
                if (newTheme === 'dark') {
                    themeIcon.classList.replace('fa-moon', 'fa-sun');
                } else {
                    themeIcon.classList.replace('fa-sun', 'fa-moon');
                }
            }
        });
    </script>
</body>
</html>