<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ABA Validation Results | Healthcare Analytics</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Lottie Player -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary: #4361ee;
            --primary-dark: #3a0ca3;
            --secondary: #4cc9f0;
            --accent: #f72585;
            --dark: #14213d;
            --light: #f8f9fa;
            --card-bg: #ffffff;
            --feature-card: #f9fafb;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --error-bg: #f8d7da;
            --error-text: #721c24;
            --success-bg: #d1e7dd;
            --success-text: #0f5132;
            --success: #4CAF50;
            --danger: #f44336;
            --warning: #ff9800;
            --info: #2196F3;
        }
        
        [data-theme="dark"] {
            --primary: #4895ef;
            --primary-dark: #4361ee;
            --secondary: #4cc9f0;
            --accent: #f72585;
            --dark: #f8f9fa;
            --light: #1a202c;
            --card-bg: #2d3748;
            --feature-card: #4a5568;
            --text-primary: #f8f9fa;
            --text-secondary: #adb5bd;
            --error-bg: #422b2d;
            --error-text: #f1aeb5;
            --success-bg: #1a3a2e;
            --success-text: #75b798;
            --success: #81c784;
            --danger: #e57373;
            --warning: #ffb74d;
            --info: #64b5f6;
        }
        
        body {
            background-color: var(--light);
            color: var(--text-dark);
            transition: all 0.3s ease;
            font-family: 'Poppins', sans-serif;
        }
        
        .text-muted {
            color: var(--text-muted) !important;
        }
        
        .card-3d {
            transform-style: preserve-3d;
            perspective: 1000px;
            transition: transform 0.5s;
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        .card-3d:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border: none;
        }
        
        .severity-high {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger);
            font-weight: 600;
            border-radius: 6px;
            padding: 4px 8px;
            display: inline-block;
        }
        
        .severity-medium {
            background-color: rgba(255, 152, 0, 0.1);
            color: var(--warning);
            font-weight: 600;
            border-radius: 6px;
            padding: 4px 8px;
            display: inline-block;
        }
        
        [data-theme="dark"] .severity-high {
            background-color: rgba(244, 67, 54, 0.2);
        }
        
        [data-theme="dark"] .severity-medium {
            background-color: rgba(255, 152, 0, 0.2);
        }
        
        .btn-primary-custom {
            background-color: var(--primary);
            border-color: var(--primary);
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary-custom:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            color: white;
        }
        
        .healthcare-icon {
            color: var(--secondary);
        }

        .header-title {
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: 700;
        }
        
        .feature-card {
            border-radius: 12px;
            transition: all 0.3s;
            background-color: var(--card-bg);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        [data-theme="dark"] .feature-card {
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .back-btn {
            border-radius: 50px;
            padding: 8px 16px;
            transition: all 0.3s;
        }
        
        .back-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        [data-theme="dark"] .back-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        footer {
            background-color: rgba(0, 0, 0, 0.02);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            color: var(--text-muted);
        }
        
        [data-theme="dark"] footer {
            background-color: rgba(255, 255, 255, 0.02);
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .footer-text {
            color: #222; /* default for light mode */
            /* ...other styles... */
        }

        [data-theme="dark"] .footer-text {
            color: #fff !important;
        }

        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        
        .table-responsive {
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        [data-theme="dark"] .table-responsive {
            border-color: rgba(255, 255, 255, 0.05);
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table thead {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
        }
        
        .table th {
            border: none;
            font-weight: 600;
            padding: 16px;
        }
        
        .table td {
            vertical-align: middle;
            padding: 12px 16px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        [data-theme="dark"] .table td {
            border-top-color: rgba(255, 255, 255, 0.05);
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }
        
        [data-theme="dark"] .table-hover tbody tr:hover {
            background-color: rgba(67, 97, 238, 0.1);
        }
        
        .alert-info {
            background-color: rgba(33, 150, 243, 0.1);
            border-color: rgba(33, 150, 243, 0.2);
            color: var(--info);
            border-radius: 8px;
        }
        
        [data-theme="dark"] .alert-info {
            background-color: rgba(33, 150, 243, 0.2);
            color: var(--info);
        }
        
        .theme-toggle {
            position: fixed;
            bottom: 24px;
            right: 24px;
            z-index: 1000;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            transition: all 0.3s;
        }
        
        .theme-toggle:hover {
            transform: scale(1.1);
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 4px 12px rgba(67, 97, 238, 0.2);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 4px 12px rgba(67, 97, 238, 0.2);
            }
        }
        
        .validation-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 24px;
        }
        
        .success-message {
            padding: 32px 0;
        }
        
        .success-message h4 {
            font-size: 1.5rem;
            margin-top: 16px;
            font-weight: 600;
        }
        
        .success-message p {
            color: var(--text-muted);
            font-size: 1rem;
        }
    </style>
</head>
<body class="min-h-screen mx-5">
    <div class="container py-5">
        <!-- Header Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card card-3d gradient-bg text-white">
                    <div class="card-body">
                        <div class="d-flex flex-column flex-md-row justify-content-center justify-content-md-start align-items-center">
                            <i class="fas fa-heartbeat healthcare-icon me-3 mb-3 mb-md-0" style="font-size: 2.5rem;"></i>
                            <div class="text-center text-md-start">
                                <h1 class="mb-2">ABA Validation Results</h1>
                                <p class="mb-0 opacity-75">Healthcare Claims Processing System</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card card-3d">
                    <div class="card-body p-5">
                        <!-- Back Arrow Button -->
                        <button class="btn btn-outline-secondary back-btn mb-4" onclick="goBack()">
                            <i class="fas fa-arrow-left me-2"></i> Go Back
                        </button>
                        
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-4">
                            <h3 class="validation-title mb-3 mb-md-0">
                                <i class="fas fa-file-medical healthcare-icon me-2"></i> Validation Report
                            </h3>
                            <button class="btn btn-primary-custom pulse" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-upload me-2"></i>Upload New File
                            </button>
                            <form action="/aba/validate" method="post" enctype="multipart/form-data" id="uploadForm" style="display:none;">
                                <input type="file" id="fileInput" name="file" accept=".txt,.edi" onchange="this.form.submit()">
                            </form>
                        </div>

                        {% if message %}
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle me-2"></i> {{ message }}
                        </div>
                        {% endif %}

                        {% if errors %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-id-card me-2"></i>Claim ID</th>
                                        <th><i class="fas fa-exclamation-circle me-2"></i>Error</th>
                                        <th><i class="fas fa-lightbulb me-2"></i>Solution</th>
                                        <th><i class="fas fa-bolt me-2"></i>Severity</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for error in errors %}
                                    <tr>
                                        <td>{{ error.claim_id }}</td>
                                        <td>{{ error.error }}</td>
                                        <td>{{ error.solution }}</td>
                                        <td>
                                            <span class="{{ 'severity-high' if error.severity == 'High' else 'severity-medium' }}">
                                                <i class="fas {{ 'fa-exclamation-triangle' if error.severity == 'High' else 'fa-exclamation-circle' }} me-2"></i>
                                                {{ error.severity }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center success-message">
                            <div style="max-width: 280px; margin: 0 auto;">
                                <lottie-player src="https://assets5.lottiefiles.com/packages/lf20_5tkzkblw.json" background="transparent" speed="1" loop autoplay></lottie-player>
                            </div>
                            <h4>No errors found in the ABA file!</h4>
                            <p>All claims have been validated successfully.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Card -->
        {% if errors %}
        <div class="row justify-content-center mt-4">
            <div class="col-lg-10">
                <div class="card card-3d">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4 mb-3 mb-md-0">
                                <h3 class="text-primary"><i class="fas fa-file-alt me-2"></i> {{ errors|length }}</h3>
                                <p class="text-muted">Total Issues</p>
                            </div>
                            <div class="col-md-4 mb-3 mb-md-0">
                                <h3 style="color: var(--danger);"><i class="fas fa-exclamation-triangle me-2"></i> {{ errors|selectattr('severity', 'equalto', 'High')|list|length }}</h3>
                                <p class="text-muted">High Severity</p>
                            </div>
                            <div class="col-md-4">
                                <h3 style="color: var(--warning);"><i class="fas fa-exclamation-circle me-2"></i> {{ errors|selectattr('severity', 'equalto', 'Medium')|list|length }}</h3>
                                <p class="text-muted">Medium Severity</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Footer -->
    <footer class="py-4 text-center rounded-3 mt-5">
        <p class="footer-text">© 2025 TherapyPMS ABA Validator | Built with care for the medical community</p>
    </footer>
    
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="themeToggle">
        <i class="fas fa-moon" id="themeIcon"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.getElementById('themeIcon');
        const html = document.documentElement;
        
        // Check for saved theme preference or use preferred color scheme
        const currentTheme = localStorage.getItem('theme') || 
                            (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
        
        if (currentTheme === 'dark') {
            html.setAttribute('data-theme', 'dark');
            themeIcon.classList.replace('fa-moon', 'fa-sun');
        }

        themeToggle.addEventListener('click', () => {
            if (html.getAttribute('data-theme') === 'light') {
                html.setAttribute('data-theme', 'dark');
                themeIcon.classList.replace('fa-moon', 'fa-sun');
                localStorage.setItem('theme', 'dark');
            } else {
                html.setAttribute('data-theme', 'light');
                themeIcon.classList.replace('fa-sun', 'fa-moon');
                localStorage.setItem('theme', 'light');
            }
        });

        // Listen for theme changes from other templates
        window.addEventListener('storage', (e) => {
            if (e.key === 'theme') {
                const newTheme = e.newValue;
                html.setAttribute('data-theme', newTheme);
                if (newTheme === 'dark') {
                    themeIcon.classList.replace('fa-moon', 'fa-sun');
                } else {
                    themeIcon.classList.replace('fa-sun', 'fa-moon');
                }
            }
        });

        // Go Back to the previous page
        function goBack() {
            window.location.href = '/aba/';
        }
    </script>
</body>
</html>