from typing import List, Tu<PERSON>, Dict
from datetime import datetime
from zoneinfo import ZoneInfo

from app.validators.aba_validators.utils import map_segment
from collections import defaultdict

IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now().astimezone(IST_TIMEZONE).replace(tzinfo=None)


GROUP_NPI = "**********"
NPI = "**********"

BILLING_ADDRESS = "6034 HEATH VALLEY RD CHARLOTTE, NC 28210-4352"

ELITE_UNIT_LIMITS = {
    "97154": 18,
}



def validate_elite_therapy_max_units(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validates that procedure code units do not exceed max allowed per day for Elite Therapy Services LLC."""
    errors = []
    mapped_segments = [map_segment(seg) for seg in claim["claim_segments"]]

    service_lines = []
    for i, seg in enumerate(mapped_segments):
        if "Service Identification" in seg:
            service_lines.append((i, seg))

    # Map (CPT, DOS) to cumulative units
    cpt_dos_units_map = defaultdict(int)

    for idx, seg in service_lines:
        sv1 = seg.get("Service Identification", "")
        sv1_parts = sv1.split(":")
        if len(sv1_parts) < 2:
            continue

        cpt = sv1_parts[1].strip()
        units = int(seg.get("Units/Days"))


        # Locate corresponding DOS in the DTP segment
        dos = None
        for j in range(idx + 1, len(mapped_segments)):
            next_seg = mapped_segments[j]
            if next_seg.get("Date/Time Qualifier") == "472" and next_seg.get("Date Time Period Format Qualifier") == "D8":
                dos = next_seg.get("Date Time Period")
                break
            if "Service Identification" in next_seg:
                break


        key = (cpt, dos)
        cpt_dos_units_map[key] += units

    # Validate against allowed max
    for (cpt, dos), total_units in cpt_dos_units_map.items():
        max_units = ELITE_UNIT_LIMITS.get(cpt)
        if max_units is not None and total_units > max_units:
            error_msg = f"CPT {cpt} has {total_units} units on DOS {dos}, which exceeds max allowed {max_units}"
            fix_msg = f"Reduce units for CPT {cpt} on {dos} to <= {max_units}"
            errors.append((error_msg, fix_msg))

    return errors




def validate_alliance_partners_group_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Validate Group NPI for Alliance Behavioral Health and Partners Health Services."""
    errors = []
    
    # Mapping all segments together
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Checking for Alliance or Partners
    print("Checking for Alliance or Partners", '-' * 50)
    is_alliance_or_partners = any(
        seg.get("Name Last or Organization Name", "").upper() in ["ALLIANCE BEHAVIORAL HEALTH", "PARTNERS HEALTH SERVICES"]
        for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"
    )
    print(is_alliance_or_partners)
    
    if is_alliance_or_partners:
        billing_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "85"), None)
        
        # Billing NPI check
        print("Billing NPI Check", '=' * 50)
        print(f"Billing NPI: {billing_npi}, Expected: {GROUP_NPI}")
        
        # if billing_npi != GROUP_NPI:
        #     errors.append((
        #         f"Claim uses Billing NPI {billing_npi} instead of Group NPI {GROUP_NPI}",
        #         f"Use Group NPI {GROUP_NPI} in NM1*85 for Alliance or Partners Health claims"
        #     ))

        service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
        
        # Service Lines Check
        print("Service Lines Check", '#' * 50)
        print(service_lines)
        
        for seg in service_lines:
            # Correcting the lookup for Rendering NPI in NM1*82 segment
            rendering_npi = next((seg.get("Identification Code") for seg in mapped_all if seg.get("Entity Identifier Code") == "82"), None)
            cpt_code = seg.get("Service Identification", "").split(":")[1]  # Extract CPT code from service line
            
            # Print each service line's check
            print("Checking Service Line", '+' * 50)
            print(f"CPT Code: {cpt_code}, Rendering NPI: {rendering_npi}")
            
            # Rule application based on CPT code
            if cpt_code in ["97151", "97152"] and rendering_npi != NPI:
                errors.append((
                    f"For CPT {cpt_code} uses Rendering NPI {rendering_npi} instead of Individual NPI {NPI}",
                    f"Use Individual NPI {NPI} for CPT {cpt_code} under Alliance or Partners Health claims"
                ))
            elif cpt_code in ["97153", "97155", "97156"] and rendering_npi != GROUP_NPI:
                errors.append((
                    f"For CPT {cpt_code} uses Rendering NPI {rendering_npi} instead of Group NPI {GROUP_NPI}",
                    f"Use Group NPI {GROUP_NPI} for CPT {cpt_code} under Alliance or Partners Health claims"
                ))

    return errors



def validate_ubh_group_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 4: United Behavioral Health claims must use Group NPI (**********) for Rendering Provider (NM1*85)."""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]

    # Identify if the claim is from United Behavioral Health
    is_ubh = any(seg.get("Name Last or Organization Name", "").upper() == "UNITED BEHAVIORAL HEALTH" for seg in mapped_all if seg.get("Entity Identifier Code") == "PR")

    if is_ubh:
        # Find the Rendering Provider NPI
        for seg in mapped_all:
            if seg.get("Segment ID") == "NM1" and seg.get("Entity Identifier Code") == "82":
                rendering_npi = seg.get("Identification Code", None)

                # If the Rendering NPI is not the correct Group NPI, add an error
                if rendering_npi != GROUP_NPI:
                    errors.append((
                        f"United Behavioral Health claim has Rendering NPI {rendering_npi} instead of Group NPI {GROUP_NPI}",
                        f"Use Group NPI {GROUP_NPI} for United Behavioral Health claims"
                    ))
                
    return errors



def validate_medcost_group_npi(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Rule 7:	Medcost  Both Individual Both Individual provider and group bill under Group NPI (**********)"""
    errors = []
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]


    # Identify if the claim is from United Behavioral Health
    is_medcost = any(
        "MEDCOST" in seg.get("Name Last or Organization Name", "").upper() 
        and seg.get("Entity Identifier Code") == "PR"
        for seg in mapped_all
    )

    if is_medcost:
        # Find the Rendering Provider NPI
        for seg in mapped_all:
            if seg.get("Segment ID") == "NM1" and seg.get("Entity Identifier Code") == "82":
                rendering_npi = seg.get("Identification Code", None)
                # If the Rendering NPI is not the correct Group NPI, add an error
                if rendering_npi != GROUP_NPI:
                    errors.append((
                        f"MEDCOST claim has Rendering NPI {rendering_npi} instead of Group NPI {GROUP_NPI}",
                        f"Use Group NPI {GROUP_NPI} for MEDCOST"
                    ))
    return errors




def validate_elite_therapy_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:

    return (
        validate_elite_therapy_max_units(claim) +
        validate_alliance_partners_group_npi(claim) +
        validate_ubh_group_npi(claim) +
        validate_medcost_group_npi(claim)
        # rule_payer_specific_provider_logic(claim) +
        # rule_scott_piper_primary_amerihealth(claim) +
        # rule_tiera_edmonds_no_alliance_without_auth(claim) +
        # rule_tari_edmunds_alliance_after_5_1_2024(claim)
    )
