import os

def read_py_files():
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Open code.txt in write mode
    with open(os.path.join(current_dir, 'code.txt'), 'w', encoding='utf-8') as output_file:
        # Iterate through all files in the directory
        for filename in os.listdir(current_dir):
            # Check if the file is a .py file
            if filename.endswith('.py'):
                file_path = os.path.join(current_dir, filename)
                
                # Write file name as header
                output_file.write(f"\n{'='*50}\n")
                output_file.write(f"File: {filename}\n")
                output_file.write(f"{'='*50}\n\n")
                
                # Read and write the content of the .py file
                try:
                    with open(file_path, 'r', encoding='utf-8') as py_file:
                        content = py_file.read()
                        output_file.write(content)
                        output_file.write('\n\n')
                except Exception as e:
                    output_file.write(f"Error reading file {filename}: {str(e)}\n\n")

if __name__ == "__main__":
    read_py_files()