import random
from typing import List, Tuple, Dict,Set
from ..utils import map_segment
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# Practice-specific constants for SIZA
SIZA_GROUP_NPI = "**********"
IST_TIMEZONE = ZoneInfo("Asia/Kolkata")
CURRENT_DATE = datetime.now(timezone.utc).astimezone(IST_TIMEZONE).replace(tzinfo=None)







DEFAULT_RATES = {
    "97151": 45.00,
    "97153": 30.00,
    "97155": 45.00,
    "97156": 45.00,
}

# CCBH_RATES = {        
    # "97151": 35.00,
    # "97153": 18.48,
    # "97155": 32.08,
    # "97156": 32.08,
# }
def validate_siza_modifiers_and_rates(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    errors = []
    mapped_segments = [map_segment(seg) for seg in claim.get("claim_segments", [])]

    insurance = next(
        (map_segment(seg).get("Name Last or Organization Name", "") 
         for seg in claim.get("billing_provider", []) 
         if map_segment(seg).get("Entity Identifier Code") == "85"),
        ""
    )
    # print("insurance",insurance,"*"*50)
    current_lx = None

    for seg in mapped_segments:
        # print(f"Segment Raw: {seg}")
        if "Assigned Number" in seg and "Service Identification" not in seg:
            current_lx = seg["Assigned Number"]
            # print(f"Set current LX Assigned Number to: {current_lx}")
            continue 

        service_id = seg.get("Service Identification", "")
        if not service_id or len(service_id.split(":")) < 3:
            continue
        
        parts = service_id.split(":")
        cpt = parts[1]

        charge = float(seg["Monetary Amount"])
        units = float(seg["Units/Days"])


        per_unit_rate = round(charge / units,2)
        
        expected_rate = 0.00
        expected_rate = DEFAULT_RATES.get(cpt)

        if expected_rate != per_unit_rate:
                errors.append((
                    f"CPT {cpt} LX{current_lx or 'N/A'} billed at ${per_unit_rate:.2f} per unit, expected ${expected_rate:.2f}.",
                    f"Bill CPT {cpt} at exact rate ${expected_rate:.2f} per unit for all claims."
                ))

    return errors






# def validate_hepe_different_pos_not_clubbed(claim: Dict[str, List[Dict[str, str]]]) -> List[Tuple[str, str]]:
#     """He Pe Rule: Do not club sessions with different POS (Place of Service) codes. Each must be billed separately.
#     """
#     errors = []
#     # print("Starting validation for claim...")
    
#     mapped_claim_segments = [map_segment(seg) for seg in claim["claim_segments"]]
#     print(f"Total segments mapped: {len(mapped_claim_segments)}")
    
#     service_lines = [seg for seg in mapped_claim_segments if "Service Identification" in seg]
#     print(f"Service lines found: {len(service_lines)}")

#     cpt_pos_map: Dict[str, Set[str]] = {}

#     for idx, seg in enumerate(service_lines):
#         cpt = ""
#         pos = seg.get("Place of Service", "")
#         print(f"\nProcessing segment {idx}: POS = '{pos}'")

#         if "Service Identification" in seg:
#             parts = seg["Service Identification"].split(":")
#             if len(parts) >= 2:
#                 cpt = parts[1].strip()
#                 print(f"Extracted CPT code: {cpt}")
#             # else:
#                 # print("Warning: 'Service Identification' format unexpected:", seg["Service Identification"])
#         # else:
#             # print("Warning: 'Service Identification' missing in segment.")

#         # if cpt:
#             # cpt_pos_map.setdefault(cpt, set()).add(pos)
#             # print(f"Added POS '{pos}' to CPT '{cpt}'")

#     # print("\nSummary of CPT codes and associated POS codes:")
#     # for cpt, pos_set in cpt_pos_map.items():
#         # print(f"  CPT {cpt}: POS codes = {sorted(pos_set)}")

#     for cpt, pos in cpt_pos_map.items():
#         if len(pos) > 1:
#             error_msg = f"Multiple POS codes {sorted(pos)} found for CPT {cpt}"
#             correction_msg = "Bill sessions with different POS codes on separate claim lines"
#             # print(f"\n[ERROR] {error_msg}")
#             errors.append((error_msg, correction_msg))

#     return errors



def validate_ccbh_cpt_97151_modifier(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """CCBH rule: CPT 97151 must include 'AP' modifier for all CCBH claims."""
    errors = []

    # print("\n--- Running CCBH CPT 97151 Modifier Validation ---")

    # Map all segments
    mapped_all = [map_segment(seg) for seg in claim["billing_provider"] + claim["subscriber"] + claim["patient"] + claim["claim_segments"]]
    
    # Identify if claim is for CCBH insurance
    is_ccbh = any(
        seg.get("Name Last or Organization Name", "").upper() == "COMMUNITY CARE BEHAVIORAL HEALTH"
        for seg in mapped_all if seg.get("Entity Identifier Code") == "PR"
    )

    # print(f"Is CCBH Claim: {is_ccbh}")

    if not is_ccbh:
        return errors

    # Check each service line for CPT 97151
    service_lines = [seg for seg in mapped_all if "Service Identification" in seg]
    seen = set()
    for seg in service_lines:
        svc_info = seg.get("Service Identification", "")
        parts = svc_info.split(":")
        if len(parts) < 2:
            continue

        cpt = parts[1].strip()
        modifiers = parts[2:] if len(parts) > 2 else []
        mod_set = frozenset(mod.strip().upper() for mod in modifiers)

        # Deduplication key: CPT + modifiers
        key = (cpt, mod_set)
        if key in seen:
            continue
        seen.add(key)

        if cpt == "97151" and "AP" not in mod_set:
            errors.append((
                f"CCBH claim with CPT {cpt} is missing required 'AP' modifier.",
                "Add 'AP' modifier to CPT 97151 for CCBH claims."
            ))


    return errors


def validate_siza_practice_rules(claim: Dict[str, List[str]]) -> List[Tuple[str, str]]:
    """Combines all Honey Bee Therapy-specific validation rules"""
    return (
        validate_siza_modifiers_and_rates(claim)
        + validate_ccbh_cpt_97151_modifier(claim)
        # validate_cpt_and_units(claim)
    )
